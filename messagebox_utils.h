/**
 * @file messagebox_utils.h
 * @brief 消息框工具类头文件
 * @details 提供统一样式的消息框工具函数，确保整个应用程序的消息框外观一致
 * <AUTHOR>
 * @date 2025-07-03
 * @version 1.0
 */

#ifndef MESSAGEBOX_UTILS_H
#define MESSAGEBOX_UTILS_H

#include <QMessageBox>
#include <QWidget>
#include <QString>

/**
 * @brief 消息框工具类
 * @details 提供统一样式的消息框静态方法
 */
class MessageBoxUtils
{
public:
    /**
     * @brief 显示信息消息框
     * @param parent 父窗口
     * @param title 标题
     * @param text 消息文本
     * @return 用户选择的按钮
     */
    static int information(QWidget *parent, const QString &title, const QString &text);
    
    /**
     * @brief 显示警告消息框
     * @param parent 父窗口
     * @param title 标题
     * @param text 消息文本
     * @return 用户选择的按钮
     */
    static int warning(QWidget *parent, const QString &title, const QString &text);
    
    /**
     * @brief 显示错误消息框
     * @param parent 父窗口
     * @param title 标题
     * @param text 消息文本
     * @return 用户选择的按钮
     */
    static int critical(QWidget *parent, const QString &title, const QString &text);
    
    /**
     * @brief 显示问题消息框
     * @param parent 父窗口
     * @param title 标题
     * @param text 消息文本
     * @param buttons 按钮组合
     * @param defaultButton 默认按钮
     * @return 用户选择的按钮
     */
    static int question(QWidget *parent, const QString &title, const QString &text,
                       QMessageBox::StandardButtons buttons = QMessageBox::Yes | QMessageBox::No,
                       QMessageBox::StandardButton defaultButton = QMessageBox::No);

private:
    /**
     * @brief 获取通用消息框样式
     * @return CSS样式字符串
     */
    static QString getCommonStyle();
    
    /**
     * @brief 获取信息类型按钮样式
     * @return CSS样式字符串
     */
    static QString getInfoButtonStyle();
    
    /**
     * @brief 获取警告类型按钮样式
     * @return CSS样式字符串
     */
    static QString getWarningButtonStyle();
    
    /**
     * @brief 获取错误类型按钮样式
     * @return CSS样式字符串
     */
    static QString getCriticalButtonStyle();
    
    /**
     * @brief 获取问题类型按钮样式
     * @return CSS样式字符串
     */
    static QString getQuestionButtonStyle();
};

#endif // MESSAGEBOX_UTILS_H
