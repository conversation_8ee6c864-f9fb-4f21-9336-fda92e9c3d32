/**
 * @file basecaninterface.cpp
 * @brief CAN/CANFD通信接口基类实现文件
 * @details 实现了CAN和CANFD通信接口的公共功能，包括日志管理、
 *          定时器管理、设备连接等公共逻辑。
 * <AUTHOR>
 * @date 2025-07-23
 * @version 1.0
 */

#include "basecaninterface.h"
#include <stdexcept>
#include <exception>
#include <QApplication>

// 静态成员变量定义
QMutex BaseCANInterface::s_deviceMutex;
bool BaseCANInterface::s_deviceInUse = false;

/**
 * @brief 构造函数实现
 * @param parent 父窗口指针
 */
BaseCANInterface::BaseCANInterface(QWidget *parent)
    : QWidget(parent)
    , isConnected(false)
    , useRealDevice(false)
    , isHeartbeatRunning(false)
    , isContinuousSending(false)
    , sendCount(0)
    , receiveCount(0)
    , heartbeatTimer(nullptr)
    , receiveTimer(nullptr)
    , continuousSendTimer(nullptr)
    , deviceHandle(INVALID_DEVICE_HANDLE)
    , channelHandle(INVALID_CHANNEL_HANDLE)
{
    qDebug() << "BaseCANInterface: 开始构造函数";

    // 确保静态变量已初始化
    ensureStaticInitialization();

    // 初始化定时器
    initializeTimers();

    // 连接定时器信号
    connectTimerSignals();

    // 尝试加载ZLGCAN库
    loadZLGCANLibrary();

    qDebug() << "BaseCANInterface: 构造函数完成";
}

/**
 * @brief 析构函数实现
 */
BaseCANInterface::~BaseCANInterface()
{
    // 停止通信
    stopCommunication();
    
    // 清理定时器
    if (heartbeatTimer) {
        delete heartbeatTimer;
        heartbeatTimer = nullptr;
    }
    if (receiveTimer) {
        delete receiveTimer;
        receiveTimer = nullptr;
    }
    if (continuousSendTimer) {
        delete continuousSendTimer;
        continuousSendTimer = nullptr;
    }
}

/**
 * @brief 添加日志文本（优化版本）
 * @param text 日志文本
 * @param isReceived 是否为接收数据
 */
void BaseCANInterface::appendLog(const QString &text, bool isReceived)
{
    QTextEdit *targetEdit = getTargetTextEdit(isReceived);
    if (!targetEdit) return;

    // 限制日志行数，防止内存泄漏
    if (targetEdit->document()->blockCount() > MAX_LOG_LINES) {
        QTextCursor cursor = targetEdit->textCursor();
        cursor.movePosition(QTextCursor::Start);
        cursor.movePosition(QTextCursor::Down, QTextCursor::KeepAnchor, 200);  // 删除前200行
        cursor.removeSelectedText();
    }

    // 添加新的日志文本
    targetEdit->append(text);

    // 自动滚动到底部
    QTextCursor cursor = targetEdit->textCursor();
    cursor.movePosition(QTextCursor::End);
    targetEdit->setTextCursor(cursor);
}

/**
 * @brief 初始化定时器
 */
void BaseCANInterface::initializeTimers()
{
    try {
        // 创建心跳定时器
        if (!heartbeatTimer) {
            heartbeatTimer = new QTimer(this);
            heartbeatTimer->setInterval(1000);  // 默认1秒
            qDebug() << "BaseCANInterface: 心跳定时器创建成功";
        }

        // 创建接收定时器
        if (!receiveTimer) {
            receiveTimer = new QTimer(this);
            receiveTimer->setInterval(400);     // 默认400ms
            qDebug() << "BaseCANInterface: 接收定时器创建成功";
        }

        // 创建连续发送定时器
        if (!continuousSendTimer) {
            continuousSendTimer = new QTimer(this);
            continuousSendTimer->setInterval(100);  // 默认100ms
            qDebug() << "BaseCANInterface: 连续发送定时器创建成功";
        }

        qDebug() << "BaseCANInterface: 所有定时器初始化完成";
    } catch (const std::exception& e) {
        qDebug() << "BaseCANInterface: 定时器初始化异常:" << e.what();
    } catch (...) {
        qDebug() << "BaseCANInterface: 定时器初始化发生未知异常";
    }
}

/**
 * @brief 确保静态变量已正确初始化
 */
void BaseCANInterface::ensureStaticInitialization()
{
    static bool initialized = false;
    if (!initialized) {
        qDebug() << "BaseCANInterface: 初始化静态变量";
        // 静态变量已在文件顶部定义，这里只是确保它们被访问过
        s_deviceInUse = false;  // 确保静态变量被初始化
        initialized = true;
        qDebug() << "BaseCANInterface: 静态变量初始化完成";
    }
}

/**
 * @brief 连接定时器信号
 */
void BaseCANInterface::connectTimerSignals()
{
    if (heartbeatTimer) {
        connect(heartbeatTimer, &QTimer::timeout, this, &BaseCANInterface::onHeartbeatTimer);
        qDebug() << "BaseCANInterface: 心跳定时器信号连接成功";
    } else {
        qDebug() << "BaseCANInterface: 警告 - 心跳定时器为空，无法连接信号";
    }

    if (receiveTimer) {
        connect(receiveTimer, &QTimer::timeout, this, &BaseCANInterface::onReceiveTimer);
        qDebug() << "BaseCANInterface: 接收定时器信号连接成功";
    } else {
        qDebug() << "BaseCANInterface: 警告 - 接收定时器为空，无法连接信号";
    }
}

/**
 * @brief 加载ZLGCAN库
 * @return 是否加载成功
 */
bool BaseCANInterface::loadZLGCANLibrary()
{
    try {
        // 获取应用程序目录
        QString appDir = QApplication::applicationDirPath();
        QString kernelDllsDir = appDir + "/kerneldlls";

        // 添加kerneldlls目录到系统PATH，让zlgcan.dll能找到依赖的DLL
        QString currentPath = qgetenv("PATH");
        QString newPath = kernelDllsDir + ";" + currentPath;
        qputenv("PATH", newPath.toLocal8Bit());

        qDebug() << "BaseCANInterface: 设置PATH包含kerneldlls目录:" << kernelDllsDir;

        // 设置库文件路径（直接从程序目录加载）
        QString libraryPath = appDir + "/zlgcan.dll";
        zlgcanLib.setFileName(libraryPath);

        qDebug() << "BaseCANInterface: 尝试加载ZLGCAN库:" << libraryPath;

        // 尝试加载库
        if (zlgcanLib.load()) {
            // 验证关键API函数是否存在
            auto ZCAN_OpenDevice = zlgcanLib.resolve("ZCAN_OpenDevice");
            auto ZCAN_InitCAN = zlgcanLib.resolve("ZCAN_InitCAN");
            auto ZCAN_StartCAN = zlgcanLib.resolve("ZCAN_StartCAN");

            if (!ZCAN_OpenDevice || !ZCAN_InitCAN || !ZCAN_StartCAN) {
                qDebug() << "BaseCANInterface: 关键API函数缺失，库可能不完整";
                zlgcanLib.unload();
                return false;
            }

            qDebug() << "BaseCANInterface: ZLGCAN库加载成功:" << libraryPath;
            return true;
        } else {
            qDebug() << "BaseCANInterface: ZLGCAN库加载失败:" << zlgcanLib.errorString();
            return false;
        }
    } catch (const std::exception& e) {
        qDebug() << "BaseCANInterface: 加载ZLGCAN库时发生异常:" << e.what();
        return false;
    } catch (...) {
        qDebug() << "BaseCANInterface: 加载ZLGCAN库时发生未知异常";
        return false;
    }
}

/**
 * @brief 设置模拟模式
 */
void BaseCANInterface::setupSimulationMode()
{
    useRealDevice = false;
    appendLog("设备连接成功 (模拟模式)", false);
}

/**
 * @brief 启动通信
 */
void BaseCANInterface::startCommunication()
{
    if (receiveTimer && !receiveTimer->isActive()) {
        receiveTimer->start();
    }
}

/**
 * @brief 停止通信
 */
void BaseCANInterface::stopCommunication()
{
    // 停止所有定时器
    if (heartbeatTimer && heartbeatTimer->isActive()) {
        heartbeatTimer->stop();
    }
    if (receiveTimer && receiveTimer->isActive()) {
        receiveTimer->stop();
    }
    if (continuousSendTimer && continuousSendTimer->isActive()) {
        continuousSendTimer->stop();
    }
    
    // 重置状态
    isHeartbeatRunning = false;
    isContinuousSending = false;
}

/**
 * @brief 心跳定时器槽函数（基础实现）
 */
void BaseCANInterface::onHeartbeatTimer()
{
    if (!isConnected) {
        if (heartbeatTimer->isActive()) {
            heartbeatTimer->stop();
        }
        isHeartbeatRunning = false;
        return;
    }

    // 生成心跳数据
    QString heartbeatData = "00 11 22 33 44 55 66 77";
    QString logText = QString("[%1] 心跳包: %2")
                     .arg(QTime::currentTime().toString("HH:mm:ss.zzz"))
                     .arg(heartbeatData);

    appendLog(logText, false);
}

/**
 * @brief 接收定时器槽函数（基础实现）
 */
void BaseCANInterface::onReceiveTimer()
{
    if (!isConnected) return;

    // 基础实现 - 子类可以重写此方法
    // 这里只是一个占位符，具体实现由子类完成
}
