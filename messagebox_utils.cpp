/**
 * @file messagebox_utils.cpp
 * @brief 消息框工具类实现文件
 * @details 实现统一样式的消息框工具函数
 * <AUTHOR>
 * @date 2025-07-03
 * @version 1.0
 */

#include "messagebox_utils.h"

int MessageBoxUtils::information(QWidget *parent, const QString &title, const QString &text)
{
    QMessageBox msgBox(parent);
    msgBox.setWindowTitle(title);
    msgBox.setIcon(QMessageBox::Information);
    msgBox.setText(text);
    msgBox.setStandardButtons(QMessageBox::Ok);
    msgBox.setStyleSheet(getCommonStyle() + getInfoButtonStyle());
    return msgBox.exec();
}

int MessageBoxUtils::warning(QWidget *parent, const QString &title, const QString &text)
{
    QMessageBox msgBox(parent);
    msgBox.setWindowTitle(title);
    msgBox.setIcon(QMessageBox::Warning);
    msgBox.setText(text);
    msgBox.setStandardButtons(QMessageBox::Ok);
    msgBox.setStyleSheet(getCommonStyle() + getWarningButtonStyle());
    return msgBox.exec();
}

int MessageBoxUtils::critical(QWidget *parent, const QString &title, const QString &text)
{
    QMessageBox msgBox(parent);
    msgBox.setWindowTitle(title);
    msgBox.setIcon(QMessageBox::Critical);
    msgBox.setText(text);
    msgBox.setStandardButtons(QMessageBox::Ok);
    msgBox.setStyleSheet(getCommonStyle() + getCriticalButtonStyle());
    return msgBox.exec();
}

int MessageBoxUtils::question(QWidget *parent, const QString &title, const QString &text,
                             QMessageBox::StandardButtons buttons, QMessageBox::StandardButton defaultButton)
{
    QMessageBox msgBox(parent);
    msgBox.setWindowTitle(title);
    msgBox.setIcon(QMessageBox::Question);
    msgBox.setText(text);
    msgBox.setStandardButtons(buttons);
    msgBox.setDefaultButton(defaultButton);
    msgBox.setStyleSheet(getCommonStyle() + getQuestionButtonStyle());
    return msgBox.exec();
}

QString MessageBoxUtils::getCommonStyle()
{
    return
        "QMessageBox {"
        "    background: qlineargradient(x1:0, y1:0, x2:1, y2:1, "
        "                               stop:0 #ffffff, "
        "                               stop:0.2 #fafafa, "
        "                               stop:0.4 #f8f9fa, "
        "                               stop:0.6 #f1f3f4, "
        "                               stop:0.8 #e9ecef, "
        "                               stop:1 #f0f0f0);"
        "    color: #dc3545;"
        "    border: 2px solid rgba(220, 53, 69, 0.3);"
        "    border-radius: 18px;"
        "    min-width: 450px;"
        "    min-height: 220px;"
        "}"
        "QMessageBox QLabel {"
        "    color: #dc3545;"
        "    font-family: 'Microsoft YaHei UI', 'Segoe UI', 'Arial';"
        "    font-size: 16px;"
        "    font-weight: bold;"
        "    background: transparent;"
        "    padding: 20px 30px;"
        "    line-height: 1.8;"
        "}"
        "QMessageBox::icon {"
        "    width: 48px;"
        "    height: 48px;"
        "}";
}

QString MessageBoxUtils::getInfoButtonStyle()
{
    return
        "QMessageBox QPushButton {"
        "    background: qlineargradient(x1:0, y1:0, x2:0, y2:1, "
        "                               stop:0 rgba(255, 255, 255, 0.98), "
        "                               stop:0.05 rgba(248, 249, 250, 0.95), "
        "                               stop:0.95 rgba(241, 243, 244, 0.92), "
        "                               stop:1 rgba(233, 236, 239, 0.9));"
        "    color: #dc3545;"
        "    border: 1px solid rgba(220, 53, 69, 0.4);"
        "    border-radius: 14px;"
        "    padding: 12px 30px;"
        "    font-family: 'Microsoft YaHei UI', 'Segoe UI', 'Arial';"
        "    font-size: 14px;"
        "    font-weight: bold;"
        "    min-width: 100px;"
        "    min-height: 40px;"
        "}"
        "QMessageBox QPushButton:hover {"
        "    background: qlineargradient(x1:0, y1:0, x2:0, y2:1, "
        "                               stop:0 #dc3545, stop:0.05 #d32f2f, "
        "                               stop:0.5 #c62828, stop:0.95 #b71c1c, "
        "                               stop:1 #a71e2a);"
        "    color: white;"
        "    border: 1px solid rgba(167, 30, 42, 0.6);"
        "}"
        "QMessageBox QPushButton:pressed {"
        "    background: qlineargradient(x1:0, y1:0, x2:0, y2:1, "
        "                               stop:0 #b71c1c, stop:0.5 #a71e2a, "
        "                               stop:1 #8d1e27);"
        "}";
}

QString MessageBoxUtils::getWarningButtonStyle()
{
    return
        "QMessageBox QPushButton {"
        "    background: qlineargradient(x1:0, y1:0, x2:0, y2:1, "
        "                               stop:0 rgba(255, 255, 255, 0.98), "
        "                               stop:0.05 rgba(248, 249, 250, 0.95), "
        "                               stop:0.95 rgba(241, 243, 244, 0.92), "
        "                               stop:1 rgba(233, 236, 239, 0.9));"
        "    color: #dc3545;"
        "    border: 1px solid rgba(220, 53, 69, 0.4);"
        "    border-radius: 14px;"
        "    padding: 12px 30px;"
        "    font-family: 'Microsoft YaHei UI', 'Segoe UI', 'Arial';"
        "    font-size: 14px;"
        "    font-weight: bold;"
        "    min-width: 100px;"
        "    min-height: 40px;"
        "}"
        "QMessageBox QPushButton:hover {"
        "    background: qlineargradient(x1:0, y1:0, x2:0, y2:1, "
        "                               stop:0 #dc3545, stop:0.05 #d32f2f, "
        "                               stop:0.5 #c62828, stop:0.95 #b71c1c, "
        "                               stop:1 #a71e2a);"
        "    color: white;"
        "    border: 1px solid rgba(167, 30, 42, 0.6);"
        "}"
        "QMessageBox QPushButton:pressed {"
        "    background: qlineargradient(x1:0, y1:0, x2:0, y2:1, "
        "                               stop:0 #b71c1c, stop:0.5 #a71e2a, "
        "                               stop:1 #8d1e27);"
        "}";
}

QString MessageBoxUtils::getCriticalButtonStyle()
{
    return
        "QMessageBox QPushButton {"
        "    background: qlineargradient(x1:0, y1:0, x2:0, y2:1, "
        "                               stop:0 rgba(255, 255, 255, 0.98), "
        "                               stop:0.05 rgba(248, 249, 250, 0.95), "
        "                               stop:0.95 rgba(241, 243, 244, 0.92), "
        "                               stop:1 rgba(233, 236, 239, 0.9));"
        "    color: #dc3545;"
        "    border: 1px solid rgba(220, 53, 69, 0.4);"
        "    border-radius: 14px;"
        "    padding: 12px 30px;"
        "    font-family: 'Microsoft YaHei UI', 'Segoe UI', 'Arial';"
        "    font-size: 14px;"
        "    font-weight: bold;"
        "    min-width: 100px;"
        "    min-height: 40px;"
        "}"
        "QMessageBox QPushButton:hover {"
        "    background: qlineargradient(x1:0, y1:0, x2:0, y2:1, "
        "                               stop:0 #dc3545, stop:0.05 #d32f2f, "
        "                               stop:0.5 #c62828, stop:0.95 #b71c1c, "
        "                               stop:1 #a71e2a);"
        "    color: white;"
        "    border: 1px solid rgba(167, 30, 42, 0.6);"
        "}"
        "QMessageBox QPushButton:pressed {"
        "    background: qlineargradient(x1:0, y1:0, x2:0, y2:1, "
        "                               stop:0 #b71c1c, stop:0.5 #a71e2a, "
        "                               stop:1 #8d1e27);"
        "}";
}

QString MessageBoxUtils::getQuestionButtonStyle()
{
    return
        "QMessageBox QPushButton {"
        "    background: qlineargradient(x1:0, y1:0, x2:0, y2:1, "
        "                               stop:0 rgba(255, 255, 255, 0.98), "
        "                               stop:0.05 rgba(248, 249, 250, 0.95), "
        "                               stop:0.95 rgba(241, 243, 244, 0.92), "
        "                               stop:1 rgba(233, 236, 239, 0.9));"
        "    color: #dc3545;"
        "    border: 1px solid rgba(220, 53, 69, 0.4);"
        "    border-radius: 14px;"
        "    padding: 12px 30px;"
        "    font-family: 'Microsoft YaHei UI', 'Segoe UI', 'Arial';"
        "    font-size: 14px;"
        "    font-weight: bold;"
        "    min-width: 100px;"
        "    min-height: 40px;"
        "}"
        "QMessageBox QPushButton:hover {"
        "    background: qlineargradient(x1:0, y1:0, x2:0, y2:1, "
        "                               stop:0 #dc3545, stop:0.05 #d32f2f, "
        "                               stop:0.5 #c62828, stop:0.95 #b71c1c, "
        "                               stop:1 #a71e2a);"
        "    color: white;"
        "    border: 1px solid rgba(167, 30, 42, 0.6);"
        "}"
        "QMessageBox QPushButton:pressed {"
        "    background: qlineargradient(x1:0, y1:0, x2:0, y2:1, "
        "                               stop:0 #b71c1c, stop:0.5 #a71e2a, "
        "                               stop:1 #8d1e27);"
        "}";
}
