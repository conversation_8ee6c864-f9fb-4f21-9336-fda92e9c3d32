<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>CANFDInterface</class>
 <widget class="QWidget" name="CANFDInterface">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>1000</width>
    <height>800</height>
   </rect>
  </property>
  <property name="minimumSize">
   <size>
    <width>1000</width>
    <height>800</height>
   </size>
  </property>
  <property name="windowTitle">
   <string>CANFD通信</string>
  </property>
  <property name="styleSheet">
   <string>QWidget#CANFDInterface {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                               stop:0 #ffffff, stop:1 #f8f9fa);
    color: #333333;
    font-family: 'Microsoft YaHei UI', 'Segoe UI', Arial, sans-serif;
}

QGroupBox {
    background: rgba(255, 255, 255, 0.9);
    border: 2px solid #dc3545;
    border-radius: 8px;
    font-size: 14px;
    font-weight: bold;
    color: #333333;
    padding-top: 20px;
    padding-left: 15px;
    padding-right: 15px;
    padding-bottom: 15px;
    margin-top: 10px;
    margin-bottom: 10px;
}

QGroupBox::title {
    subcontrol-origin: margin;
    left: 15px;
    padding: 6px 15px;
    background: #dc3545;
    border-radius: 5px;
    color: white;
    font-size: 14px;
    font-weight: bold;
}

QLabel {
    color: #333333;
    font-size: 13px;
    font-weight: 500;
    padding: 5px;
}

QPushButton {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                               stop:0 #ffffff, stop:1 #f8f9fa);
    color: #dc3545;
    border: 2px solid #dc3545;
    border-radius: 8px;
    padding: 8px 15px;
    font-size: 13px;
    font-weight: bold;
    min-width: 80px;
}

QPushButton:hover {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                               stop:0 #dc3545, stop:1 #c82333);
    color: white;
}

QPushButton:pressed {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                               stop:0 #c82333, stop:1 #a71e2a);
    color: white;
}

QLineEdit, QSpinBox, QComboBox {
    background: #ffffff;
    border: 1px solid #ced4da;
    border-radius: 4px;
    padding: 8px 12px;
    color: #333333;
    font-size: 13px;
}

QLineEdit:focus, QSpinBox:focus, QComboBox:focus {
    border: 2px solid #dc3545;
    outline: none;
}

QTextEdit {
    background: #ffffff;
    border: 1px solid #ced4da;
    border-radius: 4px;
    color: #333333;
    font-family: 'Consolas', 'Monaco', monospace;
    font-size: 12px;
    padding: 8px;
}

QCheckBox {
    color: #333333;
    font-size: 13px;
    font-weight: 500;
    spacing: 6px;
}

QCheckBox::indicator {
    width: 16px;
    height: 16px;
    border: 1px solid #ced4da;
    border-radius: 3px;
    background: #ffffff;
}

QCheckBox::indicator:checked {
    background: #dc3545;
    border: 1px solid #dc3545;
}</string>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout">
   <property name="spacing">
    <number>15</number>
   </property>
   <property name="leftMargin">
    <number>20</number>
   </property>
   <property name="topMargin">
    <number>20</number>
   </property>
   <property name="rightMargin">
    <number>20</number>
   </property>
   <property name="bottomMargin">
    <number>20</number>
   </property>
   <item>
    <widget class="QGroupBox" name="controlGroup">
     <property name="title">
      <string>通信控制</string>
     </property>
     <layout class="QGridLayout" name="controlLayout">
      <item row="0" column="0">
       <widget class="QLabel" name="protocolLabel_2">
        <property name="text">
         <string>协议:</string>
        </property>
       </widget>
      </item>
      <item row="0" column="1">
       <widget class="QLabel" name="protocolLabel">
        <property name="text">
         <string>CANFD</string>
        </property>
        <property name="styleSheet">
         <string>font-weight: bold; color: blue; font-size: 14px;</string>
        </property>
       </widget>
      </item>
      <item row="1" column="0">
       <widget class="QLabel" name="statusLabel_2">
        <property name="text">
         <string>状态:</string>
        </property>
       </widget>
      </item>
      <item row="1" column="1">
       <widget class="QLabel" name="statusLabel">
        <property name="text">
         <string>未连接</string>
        </property>
        <property name="styleSheet">
         <string>color: red; font-weight: bold;</string>
        </property>
       </widget>
      </item>
      <item row="2" column="0">
       <widget class="QLabel" name="canIdLabel">
        <property name="text">
         <string>CAN ID:</string>
        </property>
       </widget>
      </item>
      <item row="2" column="1">
       <widget class="QLineEdit" name="canIdEdit">
        <property name="text">
         <string>123</string>
        </property>
        <property name="placeholderText">
         <string>输入十六进制ID，如: 123</string>
        </property>
        <property name="maxLength">
         <number>8</number>
        </property>
       </widget>
      </item>
      <item row="3" column="0">
       <widget class="QLabel" name="sendDataLabel">
        <property name="text">
         <string>发送数据:</string>
        </property>
       </widget>
      </item>
      <item row="3" column="1">
       <widget class="QLineEdit" name="sendDataEdit">
        <property name="text">
         <string>00 11 22 33 44 55 66 77</string>
        </property>
        <property name="placeholderText">
         <string>输入十六进制数据，空格分隔 (最多64字节)</string>
        </property>
       </widget>
      </item>
      <item row="4" column="0">
       <widget class="QLabel" name="sendFrequencyLabel">
        <property name="text">
         <string>发送频率(ms):</string>
        </property>
       </widget>
      </item>
      <item row="4" column="1">
       <layout class="QVBoxLayout" name="sendFrequencyLayout">
        <item>
         <widget class="QSpinBox" name="sendFrequencySpinBox">
          <property name="suffix">
           <string> ms</string>
          </property>
          <property name="minimum">
           <number>10</number>
          </property>
          <property name="maximum">
           <number>10000</number>
          </property>
          <property name="value">
           <number>1000</number>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QComboBox" name="sendFrequencyPresetCombo">
          <property name="toolTip">
           <string>选择常用发送频率</string>
          </property>
          <item>
           <property name="text">
            <string>常用频率</string>
           </property>
          </item>
          <item>
           <property name="text">
            <string>50 ms</string>
           </property>
          </item>
          <item>
           <property name="text">
            <string>100 ms</string>
           </property>
          </item>
          <item>
           <property name="text">
            <string>200 ms</string>
           </property>
          </item>
          <item>
           <property name="text">
            <string>500 ms</string>
           </property>
          </item>
          <item>
           <property name="text">
            <string>1000 ms</string>
           </property>
          </item>
          <item>
           <property name="text">
            <string>2000 ms</string>
           </property>
          </item>
          <item>
           <property name="text">
            <string>5000 ms</string>
           </property>
          </item>
         </widget>
        </item>
       </layout>
      </item>
      <item row="5" column="0">
       <widget class="QLabel" name="receiveFrequencyLabel">
        <property name="text">
         <string>接收频率(ms):</string>
        </property>
       </widget>
      </item>
      <item row="5" column="1">
       <layout class="QVBoxLayout" name="receiveFrequencyLayout">
        <item>
         <widget class="QSpinBox" name="receiveFrequencySpinBox">
          <property name="suffix">
           <string> ms</string>
          </property>
          <property name="minimum">
           <number>50</number>
          </property>
          <property name="maximum">
           <number>5000</number>
          </property>
          <property name="value">
           <number>500</number>
          </property>
          <property name="toolTip">
           <string>设置接收数据的刷新频率，数值越小刷新越快</string>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QComboBox" name="receiveFrequencyPresetCombo">
          <property name="toolTip">
           <string>选择常用接收频率</string>
          </property>
          <item>
           <property name="text">
            <string>常用频率</string>
           </property>
          </item>
          <item>
           <property name="text">
            <string>50 ms</string>
           </property>
          </item>
          <item>
           <property name="text">
            <string>100 ms</string>
           </property>
          </item>
          <item>
           <property name="text">
            <string>200 ms</string>
           </property>
          </item>
          <item>
           <property name="text">
            <string>500 ms</string>
           </property>
          </item>
          <item>
           <property name="text">
            <string>800 ms</string>
           </property>
          </item>
          <item>
           <property name="text">
            <string>1000 ms</string>
           </property>
          </item>
          <item>
           <property name="text">
            <string>2000 ms</string>
           </property>
          </item>
         </widget>
        </item>
       </layout>
      </item>
      <item row="6" column="0">
       <widget class="QLabel" name="fdModeLabel">
        <property name="text">
         <string>FD模式:</string>
        </property>
       </widget>
      </item>
      <item row="6" column="1">
       <widget class="QComboBox" name="fdModeComboBox">
        <item>
         <property name="text">
          <string>标准CAN</string>
         </property>
        </item>
        <item>
         <property name="text">
          <string>CAN FD</string>
         </property>
        </item>
        <item>
         <property name="text">
          <string>CAN FD + BRS</string>
         </property>
        </item>
       </widget>
      </item>
      <item row="7" column="0" colspan="2">
       <widget class="QCheckBox" name="brsCheckBox">
        <property name="text">
         <string>BRS (位速率切换)</string>
        </property>
       </widget>
      </item>
      <item row="8" column="0" colspan="2">
       <widget class="QCheckBox" name="esiCheckBox">
        <property name="text">
         <string>ESI (错误状态指示)</string>
        </property>
       </widget>
      </item>
      <item row="9" column="0" colspan="2">
       <layout class="QHBoxLayout" name="buttonLayout">
        <item>
         <widget class="QPushButton" name="connectButton">
          <property name="text">
           <string>连接设备</string>
          </property>
          <property name="styleSheet">
           <string>QPushButton { background-color: #4CAF50; color: white; font-weight: bold; padding: 8px; }</string>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QPushButton" name="disconnectButton">
          <property name="text">
           <string>断开连接</string>
          </property>
          <property name="styleSheet">
           <string>QPushButton { background-color: #f44336; color: white; font-weight: bold; padding: 8px; }</string>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QPushButton" name="sendButton">
          <property name="text">
           <string>发送数据</string>
          </property>
          <property name="styleSheet">
           <string>QPushButton { background-color: #2196F3; color: white; font-weight: bold; padding: 8px; }</string>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QPushButton" name="continuousSendButton">
          <property name="text">
           <string>连续发送</string>
          </property>
          <property name="styleSheet">
           <string>QPushButton { background-color: #4CAF50; color: white; font-weight: bold; padding: 8px; }</string>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QPushButton" name="heartbeatButton">
          <property name="text">
           <string>发送心跳包</string>
          </property>
          <property name="styleSheet">
           <string>QPushButton { background-color: #FF9800; color: white; font-weight: bold; padding: 8px; }</string>
          </property>
         </widget>
        </item>
        <item>
         <spacer name="horizontalSpacer">
          <property name="orientation">
           <enum>Qt::Horizontal</enum>
          </property>
          <property name="sizeHint" stdset="0">
           <size>
            <width>40</width>
            <height>20</height>
           </size>
          </property>
         </spacer>
        </item>
       </layout>
      </item>
     </layout>
    </widget>
   </item>
   <item>
    <widget class="QSplitter" name="dataSplitter">
     <property name="orientation">
      <enum>Qt::Horizontal</enum>
     </property>
     <widget class="QGroupBox" name="sendGroup">
      <property name="title">
       <string>发送数据记录</string>
      </property>
      <layout class="QVBoxLayout" name="sendLayout">
       <item>
        <widget class="QTextEdit" name="sendTextEdit">
         <property name="readOnly">
          <bool>true</bool>
         </property>
         <property name="font">
          <font>
           <family>Consolas</family>
           <pointsize>14</pointsize>
          </font>
         </property>
        </widget>
       </item>
      </layout>
     </widget>
     <widget class="QGroupBox" name="receiveGroup">
      <property name="title">
       <string>接收数据记录</string>
      </property>
      <layout class="QVBoxLayout" name="receiveLayout">
       <item>
        <widget class="QTextEdit" name="receiveTextEdit">
         <property name="readOnly">
          <bool>true</bool>
         </property>
         <property name="font">
          <font>
           <family>Consolas</family>
           <pointsize>14</pointsize>
          </font>
         </property>
        </widget>
       </item>
      </layout>
     </widget>
    </widget>
   </item>
   <item>
    <layout class="QHBoxLayout" name="bottomLayout">
     <item>
      <widget class="QPushButton" name="clearSendButton">
       <property name="text">
        <string>清空发送区</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QPushButton" name="clearReceiveButton">
       <property name="text">
        <string>清空接收区</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QPushButton" name="saveDataButton">
       <property name="text">
        <string>保存数据</string>
       </property>
      </widget>
     </item>
     <item>
      <spacer name="horizontalSpacer_2">
       <property name="orientation">
        <enum>Qt::Horizontal</enum>
       </property>
       <property name="sizeHint" stdset="0">
        <size>
         <width>40</width>
         <height>20</height>
        </size>
       </property>
      </spacer>
     </item>
     <item>
      <widget class="QPushButton" name="backButton">
       <property name="text">
        <string>返回</string>
       </property>
       <property name="styleSheet">
        <string>QPushButton {
    background-color: #ffffff;
    color: #dc3545;
    border: 2px solid #dc3545;
    border-radius: 8px;
    font-weight: bold;
    padding: 8px 16px;
    min-width: 60px;
}
QPushButton:hover {
    background-color: #dc3545;
    color: white;
}
QPushButton:pressed {
    background-color: #c82333;
    color: white;
}</string>
       </property>
      </widget>
     </item>
    </layout>
   </item>
  </layout>
 </widget>
 <resources/>
 <connections/>
</ui>
