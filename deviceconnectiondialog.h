/**
 * @file deviceconnectiondialog.h
 * @brief 设备连接检测对话框头文件
 * @details 在进入功能界面前检测CANFD设备连接状态，提供用户选择
 * <AUTHOR>
 * @date 2025-01-31
 * @version 1.0
 */

#ifndef DEVICECONNECTIONDIALOG_H
#define DEVICECONNECTIONDIALOG_H

#include <QDialog>
#include <QLabel>
#include <QPushButton>
#include <QProgressBar>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QTimer>
#include <QMovie>
#include "canfddevicemanager.h"

/**
 * @brief 用户选择结果枚举
 */
enum class UserChoice {
    USE_REAL_DATA = 0,      ///< 使用真实数据
    USE_SIMULATION = 1,     ///< 使用模拟数据
    CONNECT_DEVICE = 2,     ///< 连接设备
    CANCEL = 3             ///< 取消操作
};

/**
 * @class DeviceConnectionDialog
 * @brief 设备连接检测对话框
 * @details 检测CANFD设备连接状态，根据结果提供不同的用户选择
 */
class DeviceConnectionDialog : public QDialog
{
    Q_OBJECT

public:
    /**
     * @brief 构造函数
     * @param functionName 功能名称（如"虚拟示波器"、"多电机控制"）
     * @param parent 父窗口
     */
    explicit DeviceConnectionDialog(const QString& functionName, QWidget *parent = nullptr);
    
    /**
     * @brief 析构函数
     */
    ~DeviceConnectionDialog();
    
    /**
     * @brief 获取用户选择结果
     * @return 用户选择
     */
    UserChoice getUserChoice() const { return m_userChoice; }
    
    /**
     * @brief 获取检测到的数据源类型
     * @return 数据源类型
     */
    DataSourceType getDataSourceType() const { return m_dataSourceType; }

public slots:
    /**
     * @brief 显示对话框并开始检测
     * @return 对话框执行结果
     */
    int exec() override;

private slots:
    /**
     * @brief 设备状态检测完成槽函数
     */
    void onDetectionCompleted();
    
    /**
     * @brief 连接设备按钮点击槽函数
     */
    void onConnectDeviceClicked();
    
    /**
     * @brief 使用模拟数据按钮点击槽函数
     */
    void onUseSimulationClicked();
    
    /**
     * @brief 使用真实数据按钮点击槽函数
     */
    void onUseRealDataClicked();
    
    /**
     * @brief 取消按钮点击槽函数
     */
    void onCancelClicked();
    
    /**
     * @brief 连接状态改变槽函数
     */
    void onConnectionStatusChanged(CANFDConnectionStatus status);
    
    /**
     * @brief 设备错误槽函数
     */
    void onDeviceError(const QString& errorMessage);

private:
    /**
     * @brief 初始化UI界面
     */
    void initializeUI();
    
    /**
     * @brief 开始设备检测
     */
    void startDetection();
    
    /**
     * @brief 显示检测结果界面
     * @param status 检测到的连接状态
     */
    void showDetectionResult(CANFDConnectionStatus status);
    
    /**
     * @brief 显示检测中界面
     */
    void showDetectingUI();
    
    /**
     * @brief 显示已连接界面
     */
    void showConnectedUI();
    
    /**
     * @brief 显示未连接界面
     */
    void showDisconnectedUI();
    
    /**
     * @brief 显示连接中界面
     */
    void showConnectingUI();
    
    /**
     * @brief 设置对话框样式
     */
    void setupStyles();

private:
    QString m_functionName;                     ///< 功能名称
    UserChoice m_userChoice;                    ///< 用户选择结果
    DataSourceType m_dataSourceType;           ///< 数据源类型
    CANFDConnectionStatus m_detectedStatus;     ///< 检测到的连接状态
    
    // UI控件
    QVBoxLayout* m_mainLayout;                  ///< 主布局
    QLabel* m_titleLabel;                       ///< 标题标签
    QLabel* m_statusLabel;                      ///< 状态标签
    QLabel* m_iconLabel;                        ///< 图标标签
    QProgressBar* m_progressBar;                ///< 进度条
    QMovie* m_loadingMovie;                     ///< 加载动画
    
    // 按钮
    QPushButton* m_connectDeviceBtn;            ///< 连接设备按钮
    QPushButton* m_useSimulationBtn;            ///< 使用模拟数据按钮
    QPushButton* m_useRealDataBtn;              ///< 使用真实数据按钮
    QPushButton* m_cancelBtn;                   ///< 取消按钮
    
    QHBoxLayout* m_buttonLayout;                ///< 按钮布局
    
    // 定时器
    QTimer* m_detectionTimer;                   ///< 检测定时器
    
    // 设备管理器
    CANFDDeviceManager* m_deviceManager;        ///< 设备管理器实例
};

#endif // DEVICECONNECTIONDIALOG_H
