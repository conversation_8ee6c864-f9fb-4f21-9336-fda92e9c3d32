/**
 * @file modbusprotocol.cpp
 * @brief Modbus协议处理类实现文件
 * @details 实现了ModbusProtocol类的所有功能，包括Modbus RTU协议的核心功能：
 *          CRC-16校验计算、数据帧构建和解析、各种功能码的实现、数据类型
 *          转换等完整的Modbus协议处理功能。
 * <AUTHOR>
 * @date 2025-07-02
 * @version 1.0
 */

#include "modbusprotocol.h"  // Modbus协议处理类声明

/**
 * @brief Modbus功能码描述映射表
 * @details 静态常量映射表，用于将功能码数值转换为中文描述，
 *          便于用户理解和调试输出
 */
const QMap<uint8_t, QString> ModbusProtocol::functionCodeDescriptions = {
    {0x01, "读取线圈状态"},        // 读取线圈状态功能码
    {0x02, "读取输入状态"},        // 读取离散输入状态功能码
    {0x03, "读取保持寄存器"},      // 读取保持寄存器功能码
    {0x04, "读取输入寄存器"},      // 读取输入寄存器功能码
    {0x05, "写单个线圈"},          // 写单个线圈功能码
    {0x06, "写单个寄存器"},        // 写单个寄存器功能码
    {0x0F, "写多个线圈"},          // 写多个线圈功能码
    {0x10, "写多个寄存器"}         // 写多个寄存器功能码
};

/**
 * @brief CRC-16校验高字节查找表
 * @details 用于快速计算Modbus RTU协议CRC-16校验码的高字节部分。
 *          该查找表基于CRC-16/MODBUS算法（多项式0x8005），
 *          可以显著提高CRC计算的效率。表中包含256个预计算值，
 *          对应输入字节的所有可能值（0x00-0xFF）。
 */
const uint8_t ModbusProtocol::CRC_HIGH_TABLE[256] = {
    0x00, 0xC1, 0x81, 0x40, 0x01, 0xC0, 0x80, 0x41, 0x01, 0xC0, 0x80, 0x41, 0x00, 0xC1, 0x81,
    0x40, 0x01, 0xC0, 0x80, 0x41, 0x00, 0xC1, 0x81, 0x40, 0x00, 0xC1, 0x81, 0x40, 0x01, 0xC0,
    0x80, 0x41, 0x01, 0xC0, 0x80, 0x41, 0x00, 0xC1, 0x81, 0x40, 0x00, 0xC1, 0x81, 0x40, 0x01,
    0xC0, 0x80, 0x41, 0x00, 0xC1, 0x81, 0x40, 0x01, 0xC0, 0x80, 0x41, 0x01, 0xC0, 0x80, 0x41,
    0x00, 0xC1, 0x81, 0x40, 0x01, 0xC0, 0x80, 0x41, 0x00, 0xC1, 0x81, 0x40, 0x00, 0xC1, 0x81,
    0x40, 0x01, 0xC0, 0x80, 0x41, 0x00, 0xC1, 0x81, 0x40, 0x01, 0xC0, 0x80, 0x41, 0x01, 0xC0,
    0x80, 0x41, 0x00, 0xC1, 0x81, 0x40, 0x00, 0xC1, 0x81, 0x40, 0x01, 0xC0, 0x80, 0x41, 0x01,
    0xC0, 0x80, 0x41, 0x00, 0xC1, 0x81, 0x40, 0x01, 0xC0, 0x80, 0x41, 0x00, 0xC1, 0x81, 0x40,
    0x00, 0xC1, 0x81, 0x40, 0x01, 0xC0, 0x80, 0x41, 0x01, 0xC0, 0x80, 0x41, 0x00, 0xC1, 0x81,
    0x40, 0x00, 0xC1, 0x81, 0x40, 0x01, 0xC0, 0x80, 0x41, 0x00, 0xC1, 0x81, 0x40, 0x01, 0xC0,
    0x80, 0x41, 0x01, 0xC0, 0x80, 0x41, 0x00, 0xC1, 0x81, 0x40, 0x00, 0xC1, 0x81, 0x40, 0x01,
    0xC0, 0x80, 0x41, 0x01, 0xC0, 0x80, 0x41, 0x00, 0xC1, 0x81, 0x40, 0x01, 0xC0, 0x80, 0x41,
    0x00, 0xC1, 0x81, 0x40, 0x00, 0xC1, 0x81, 0x40, 0x01, 0xC0, 0x80, 0x41, 0x00, 0xC1, 0x81,
    0x40, 0x01, 0xC0, 0x80, 0x41, 0x01, 0xC0, 0x80, 0x41, 0x00, 0xC1, 0x81, 0x40, 0x01, 0xC0,
    0x80, 0x41, 0x00, 0xC1, 0x81, 0x40, 0x00, 0xC1, 0x81, 0x40, 0x01, 0xC0, 0x80, 0x41, 0x01,
    0xC0, 0x80, 0x41, 0x00, 0xC1, 0x81, 0x40, 0x00, 0xC1, 0x81, 0x40, 0x01, 0xC0, 0x80, 0x41,
    0x00, 0xC1, 0x81, 0x40, 0x01, 0xC0, 0x80, 0x41, 0x01, 0xC0, 0x80, 0x41, 0x00, 0xC1, 0x81,
    0x40
};

// CRC低字节查找表
const uint8_t ModbusProtocol::CRC_LOW_TABLE[256] = {
    0x00, 0xC0, 0xC1, 0x01, 0xC3, 0x03, 0x02, 0xC2, 0xC6, 0x06, 0x07, 0xC7, 0x05, 0xC5, 0xC4,
    0x04, 0xCC, 0x0C, 0x0D, 0xCD, 0x0F, 0xCF, 0xCE, 0x0E, 0x0A, 0xCA, 0xCB, 0x0B, 0xC9, 0x09,
    0x08, 0xC8, 0xD8, 0x18, 0x19, 0xD9, 0x1B, 0xDB, 0xDA, 0x1A, 0x1E, 0xDE, 0xDF, 0x1F, 0xDD,
    0x1D, 0x1C, 0xDC, 0x14, 0xD4, 0xD5, 0x15, 0xD7, 0x17, 0x16, 0xD6, 0xD2, 0x12, 0x13, 0xD3,
    0x11, 0xD1, 0xD0, 0x10, 0xF0, 0x30, 0x31, 0xF1, 0x33, 0xF3, 0xF2, 0x32, 0x36, 0xF6, 0xF7,
    0x37, 0xF5, 0x35, 0x34, 0xF4, 0x3C, 0xFC, 0xFD, 0x3D, 0xFF, 0x3F, 0x3E, 0xFE, 0xFA, 0x3A,
    0x3B, 0xFB, 0x39, 0xF9, 0xF8, 0x38, 0x28, 0xE8, 0xE9, 0x29, 0xEB, 0x2B, 0x2A, 0xEA, 0xEE,
    0x2E, 0x2F, 0xEF, 0x2D, 0xED, 0xEC, 0x2C, 0xE4, 0x24, 0x25, 0xE5, 0x27, 0xE7, 0xE6, 0x26,
    0x22, 0xE2, 0xE3, 0x23, 0xE1, 0x21, 0x20, 0xE0, 0xA0, 0x60, 0x61, 0xA1, 0x63, 0xA3, 0xA2,
    0x62, 0x66, 0xA6, 0xA7, 0x67, 0xA5, 0x65, 0x64, 0xA4, 0x6C, 0xAC, 0xAD, 0x6D, 0xAF, 0x6F,
    0x6E, 0xAE, 0xAA, 0x6A, 0x6B, 0xAB, 0x69, 0xA9, 0xA8, 0x68, 0x78, 0xB8, 0xB9, 0x79, 0xBB,
    0x7B, 0x7A, 0xBA, 0xBE, 0x7E, 0x7F, 0xBF, 0x7D, 0xBD, 0xBC, 0x7C, 0xB4, 0x74, 0x75, 0xB5,
    0x77, 0xB7, 0xB6, 0x76, 0x72, 0xB2, 0xB3, 0x73, 0xB1, 0x71, 0x70, 0xB0, 0x50, 0x90, 0x91,
    0x51, 0x93, 0x53, 0x52, 0x92, 0x96, 0x56, 0x57, 0x97, 0x55, 0x95, 0x94, 0x54, 0x9C, 0x5C,
    0x5D, 0x9D, 0x5F, 0x9F, 0x9E, 0x5E, 0x5A, 0x9A, 0x9B, 0x5B, 0x99, 0x59, 0x58, 0x98, 0x88,
    0x48, 0x49, 0x89, 0x4B, 0x8B, 0x8A, 0x4A, 0x4E, 0x8E, 0x8F, 0x4F, 0x8D, 0x4D, 0x4C, 0x8C,
    0x44, 0x84, 0x85, 0x45, 0x87, 0x47, 0x46, 0x86, 0x82, 0x42, 0x43, 0x83, 0x41, 0x81, 0x80,
    0x40
};

ModbusProtocol::ModbusProtocol(QObject *parent)
    : QObject(parent)
{
}

QByteArray ModbusProtocol::createReadHoldingRegistersRequest(uint8_t slaveAddress, uint16_t startAddress, uint16_t registerCount)
{
    QByteArray frame;
    
    // 从站地址
    frame.append(static_cast<char>(slaveAddress));
    
    // 功能码 (0x03 - 读保持寄存器)
    frame.append(static_cast<char>(ReadHoldingRegisters));
    
    // 起始地址 (高字节在前)
    frame.append(static_cast<char>((startAddress >> 8) & 0xFF));
    frame.append(static_cast<char>(startAddress & 0xFF));
    
    // 寄存器数量 (高字节在前)
    frame.append(static_cast<char>((registerCount >> 8) & 0xFF));
    frame.append(static_cast<char>(registerCount & 0xFF));
    
    // 添加CRC校验
    QByteArray crc = calculateCRC(frame);
    frame.append(crc);
    
    return frame;
}

QByteArray ModbusProtocol::createReadInputRegistersRequest(uint8_t slaveAddress, uint16_t startAddress, uint16_t registerCount)
{
    QByteArray frame;
    
    // 从站地址
    frame.append(static_cast<char>(slaveAddress));
    
    // 功能码 (0x04 - 读输入寄存器)
    frame.append(static_cast<char>(ReadInputRegisters));
    
    // 起始地址 (高字节在前)
    frame.append(static_cast<char>((startAddress >> 8) & 0xFF));
    frame.append(static_cast<char>(startAddress & 0xFF));
    
    // 寄存器数量 (高字节在前)
    frame.append(static_cast<char>((registerCount >> 8) & 0xFF));
    frame.append(static_cast<char>(registerCount & 0xFF));
    
    // 添加CRC校验
    QByteArray crc = calculateCRC(frame);
    frame.append(crc);
    
    return frame;
}

QByteArray ModbusProtocol::createWriteSingleRegisterRequest(uint8_t slaveAddress, uint16_t registerAddress, uint16_t value)
{
    QByteArray frame;
    
    // 从站地址
    frame.append(static_cast<char>(slaveAddress));
    
    // 功能码 (0x06 - 写单个寄存器)
    frame.append(static_cast<char>(WriteSingleRegister));
    
    // 寄存器地址 (高字节在前)
    frame.append(static_cast<char>((registerAddress >> 8) & 0xFF));
    frame.append(static_cast<char>(registerAddress & 0xFF));
    
    // 寄存器值 (高字节在前)
    frame.append(static_cast<char>((value >> 8) & 0xFF));
    frame.append(static_cast<char>(value & 0xFF));
    
    // 添加CRC校验
    QByteArray crc = calculateCRC(frame);
    frame.append(crc);
    
    return frame;
}

QByteArray ModbusProtocol::createWriteMultipleRegistersRequest(uint8_t slaveAddress, uint16_t startAddress, const QList<uint16_t>& values)
{
    QByteArray frame;
    
    // 从站地址
    frame.append(static_cast<char>(slaveAddress));
    
    // 功能码 (0x10 - 写多个寄存器)
    frame.append(static_cast<char>(WriteMultipleRegisters));
    
    // 起始地址 (高字节在前)
    frame.append(static_cast<char>((startAddress >> 8) & 0xFF));
    frame.append(static_cast<char>(startAddress & 0xFF));
    
    // 寄存器数量 (高字节在前)
    uint16_t registerCount = values.size();
    frame.append(static_cast<char>((registerCount >> 8) & 0xFF));
    frame.append(static_cast<char>(registerCount & 0xFF));
    
    // 字节计数
    frame.append(static_cast<char>(registerCount * 2));
    
    // 寄存器值
    for (uint16_t value : values) {
        frame.append(static_cast<char>((value >> 8) & 0xFF));
        frame.append(static_cast<char>(value & 0xFF));
    }
    
    // 添加CRC校验
    QByteArray crc = calculateCRC(frame);
    frame.append(crc);
    
    return frame;
}

QPair<bool, QString> ModbusProtocol::parseResponse(const QByteArray &response, uint8_t expectedSlaveAddress, uint8_t expectedFunctionCode)
{
    QString errorMsg;
    
    // 检查帧长度
    if (!checkFrameLength(response, errorMsg)) {
        return qMakePair(false, errorMsg);
    }

    // 验证CRC
    if (!validateCRC(response)) {
        return qMakePair(false, "CRC校验失败");
    }

    // 检查从站地址
    if (!checkSlaveAddress(response, expectedSlaveAddress, errorMsg)) {
        return qMakePair(false, errorMsg);
    }

    // 获取功能码
    uint8_t functionCode = static_cast<uint8_t>(response.at(1));

    // 检查是否是异常响应
    if (functionCode & 0x80) {
        return qMakePair(false, parseErrorResponse(response));
    }

    // 检查功能码
    if (!checkFunctionCode(response, expectedFunctionCode, errorMsg)) {
        return qMakePair(false, errorMsg);
    }

    // 根据功能码解析响应
    QString result;
    switch (functionCode) {
        case 0x01:  // 读取线圈状态
        case 0x02:  // 读取输入状态
            result = parseReadCoilsResponse(response);
            break;
        case 0x03:  // 读取保持寄存器
        case 0x04:  // 读取输入寄存器
            result = parseReadRegistersResponse(response);
            break;
        case 0x05:  // 写单个线圈
        case 0x06:  // 写单个寄存器
        case 0x0F:  // 写多个线圈
        case 0x10:  // 写多个寄存器
            result = parseWriteResponse(response);
            break;
        default:
            return qMakePair(false, QString("不支持的功能码: 0x%1").arg(functionCode, 2, 16, QChar('0')));
    }

    return qMakePair(true, result);
}

QByteArray ModbusProtocol::createRequest(uint8_t slaveAddress, uint8_t functionCode, uint16_t startAddress, uint16_t quantity)
{
    QByteArray request;
    
    // 添加从站地址
    request.append(static_cast<char>(slaveAddress));
    
    // 添加功能码
    request.append(static_cast<char>(functionCode));
    
    // 添加起始地址（高字节在前）
    request.append(static_cast<char>((startAddress >> 8) & 0xFF));
    request.append(static_cast<char>(startAddress & 0xFF));
    
    // 添加数量（高字节在前）
    request.append(static_cast<char>((quantity >> 8) & 0xFF));
    request.append(static_cast<char>(quantity & 0xFF));
    
    // 计算并添加CRC
    QByteArray crc = calculateCRC(request);
    request.append(crc);
    
    return request;
}

QByteArray ModbusProtocol::calculateCRC(const QByteArray &data)
{
    quint16 crc = 0xFFFF;
    
    for (int i = 0; i < data.size(); ++i) {
        crc ^= static_cast<quint8>(data.at(i));
        for (int j = 0; j < 8; ++j) {
            if (crc & 0x0001) {
                crc = (crc >> 1) ^ 0xA001;
            } else {
                crc = crc >> 1;
            }
        }
    }
    
    QByteArray result;
    result.append(static_cast<char>(crc & 0xFF));         // 低字节在前
    result.append(static_cast<char>((crc >> 8) & 0xFF));  // 高字节在后
    return result;
}

bool ModbusProtocol::validateCRC(const QByteArray &data)
{
    if (data.size() < 2) {
        return false;                                            // 数据长度不足，CRC校验失败
    }

    // === 第二步：分离消息数据和CRC ===
    QByteArray messageData = data.left(data.size() - 2);        // 获取消息数据部分（除去最后2字节CRC）
    QByteArray receivedCRC = data.right(2);                     // 获取接收到的CRC（最后2字节）
    QByteArray calculatedCRC = calculateCRC(messageData);       // 计算消息数据的CRC

    // === 第三步：比较CRC值 ===
    return receivedCRC == calculatedCRC;                        // 返回CRC校验结果
}

/**
 * @brief 解析读取线圈响应数据函数实现
 * @param response 响应数据字节数组
 * @return 解析后的线圈状态字符串
 * @details 解析Modbus读取线圈功能码的响应数据：
 *          1. 提取字节数信息
 *          2. 逐字节解析线圈状态
 *          3. 按位提取每个线圈的开关状态
 *          4. 格式化输出结果字符串
 */
QString ModbusProtocol::parseReadCoilsResponse(const QByteArray &response)
{
    // === 第一步：提取字节数信息 ===
    uint8_t byteCount = static_cast<uint8_t>(response.at(2));   // 获取数据字节数
    QString result = QString("字节数: %1 | 线圈状态: ").arg(byteCount);

    // === 第二步：逐字节解析线圈状态 ===
    for (int i = 0; i < byteCount; ++i) {
        uint8_t coilByte = static_cast<uint8_t>(response.at(3 + i));  // 获取当前字节

        // === 第三步：按位提取线圈状态 ===
        for (int bit = 0; bit < 8; ++bit) {
            if (i * 8 + bit < byteCount * 8) {                  // 确保不超出有效位数
                result += QString("%1 ").arg((coilByte & (1 << bit)) ? "1" : "0");  // 提取位状态
            }
        }
    }

    return result;                                              // 返回格式化的线圈状态字符串
}

/**
 * @brief 解析读取寄存器响应数据函数实现
 * @param response 响应数据字节数组
 * @return 解析后的寄存器值字符串
 * @details 解析Modbus读取寄存器功能码的响应数据：
 *          1. 提取字节数信息并计算寄存器数量
 *          2. 逐个寄存器解析16位数值
 *          3. 按大端字节序组合高低字节
 *          4. 格式化输出十六进制寄存器值
 */
QString ModbusProtocol::parseReadRegistersResponse(const QByteArray &response)
{
    // === 第一步：提取字节数和计算寄存器数量 ===
    uint8_t byteCount = static_cast<uint8_t>(response.at(2));   // 获取数据字节数
    int registerCount = byteCount / 2;                          // 计算寄存器数量（每个寄存器2字节）
    QString result = QString("字节数: %1 | 寄存器值: ").arg(byteCount);

    // === 第二步：逐个寄存器解析数值 ===
    for (int i = 0; i < registerCount; ++i) {
        // 按大端字节序组合16位寄存器值（高字节在前，低字节在后）
        uint16_t registerValue = (static_cast<uint8_t>(response.at(3 + i * 2)) << 8) |
                                static_cast<uint8_t>(response.at(4 + i * 2));
        result += QString("0x%1 ").arg(registerValue, 4, 16, QChar('0'));  // 格式化为4位十六进制
    }

    return result;                                              // 返回格式化的寄存器值字符串
}

/**
 * @brief 解析写入操作响应数据函数实现
 * @param response 响应数据字节数组
 * @return 解析后的写入确认信息字符串
 * @details 解析Modbus写入功能码的响应数据：
 *          1. 提取写入地址信息
 *          2. 提取写入数值信息
 *          3. 按大端字节序组合16位数值
 *          4. 格式化输出地址和数值确认信息
 */
QString ModbusProtocol::parseWriteResponse(const QByteArray &response)
{
    // === 第一步：提取写入地址（大端字节序） ===
    uint16_t address = (static_cast<uint8_t>(response.at(2)) << 8) |
                       static_cast<uint8_t>(response.at(3));

    // === 第二步：提取写入数值（大端字节序） ===
    uint16_t value = (static_cast<uint8_t>(response.at(4)) << 8) |
                     static_cast<uint8_t>(response.at(5));

    // === 第三步：格式化输出确认信息 ===
    return QString("地址: 0x%1 | 值: 0x%2")
            .arg(address, 4, 16, QChar('0'))                    // 格式化地址为4位十六进制
            .arg(value, 4, 16, QChar('0'));
}

QString ModbusProtocol::parseErrorResponse(const QByteArray &response)
{
    uint8_t errorCode = static_cast<uint8_t>(response.at(2));
    QString errorMsg;
    
    switch (errorCode) {
        case 0x01: errorMsg = "非法功能码"; break;
        case 0x02: errorMsg = "非法数据地址"; break;
        case 0x03: errorMsg = "非法数据值"; break;
        case 0x04: errorMsg = "从站设备故障"; break;
        case 0x05: errorMsg = "确认"; break;
        case 0x06: errorMsg = "从站设备忙"; break;
        case 0x08: errorMsg = "存储奇偶性差错"; break;
        default: errorMsg = QString("未知错误(0x%1)").arg(errorCode, 2, 16, QChar('0'));
    }
    
    return QString("异常响应 | %1").arg(errorMsg);
}

bool ModbusProtocol::checkFrameLength(const QByteArray &frame, QString &errorMsg)
{
    if (frame.size() < 4) {  // 最小帧长度：地址(1) + 功能码(1) + CRC(2)
        errorMsg = "帧长度不足";
        return false;
    }
    
    if (frame.size() > 256) {  // Modbus最大帧长度
        errorMsg = "帧长度超出限制";
        return false;
    }
    
    return true;
}

bool ModbusProtocol::checkSlaveAddress(const QByteArray &frame, uint8_t expectedAddress, QString &errorMsg)
{
    uint8_t address = static_cast<uint8_t>(frame.at(0));
    if (address != expectedAddress) {
        errorMsg = QString("从站地址不匹配，期望: %1，实际: %2")
                .arg(expectedAddress)
                .arg(address);
        return false;
    }
    return true;
}

bool ModbusProtocol::checkFunctionCode(const QByteArray &frame, uint8_t expectedCode, QString &errorMsg)
{
    uint8_t functionCode = static_cast<uint8_t>(frame.at(1));
    if (functionCode != expectedCode) {
        errorMsg = QString("功能码不匹配，期望: 0x%1，实际: 0x%2")
                .arg(expectedCode, 2, 16, QChar('0'))
                .arg(functionCode, 2, 16, QChar('0'));
        return false;
    }
    return true;
}

QString ModbusProtocol::getFunctionCodeDescription(uint8_t functionCode)
{
    if (functionCodeDescriptions.contains(functionCode)) {
        return functionCodeDescriptions[functionCode];
    } else {
        return QString("未知功能码: 0x%1").arg(functionCode, 2, 16, QChar('0'));
    }
} 