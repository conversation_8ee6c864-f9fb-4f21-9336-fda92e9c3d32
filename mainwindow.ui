<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>MainWindow</class>
 <widget class="QMainWindow" name="MainWindow">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>1200</width>
    <height>800</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>电机上位机助手</string>
  </property>
  <property name="styleSheet">
   <string>/* 主窗口背景 - 增强科技感渐变 */
QMainWindow {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
        stop:0 #ffffff,
        stop:0.2 #fafafa,
        stop:0.4 #f8f9fa,
        stop:0.6 #f1f3f4,
        stop:0.8 #e9ecef,
        stop:1 #f0f0f0);
}

/* 中央widget - 添加logo背景 */
QWidget#centralwidget {
    background: transparent;
}

/* 按钮区域背景 - 强化科技感设计 */
QFrame#buttonFrame {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
        stop:0 rgba(255, 255, 255, 0.98),
        stop:0.1 rgba(248, 249, 250, 0.95),
        stop:0.5 rgba(241, 243, 244, 0.92),
        stop:0.9 rgba(248, 249, 250, 0.95),
        stop:1 rgba(255, 255, 255, 0.98));
    border: 2px solid rgba(220, 53, 69, 0.3);
    border-radius: 28px;
    margin: 18px;

}

/* 标题区域样式 - 强化科技感 */
QFrame#titleFrame {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
        stop:0 rgba(220, 53, 69, 0.12),
        stop:0.15 rgba(255, 255, 255, 0.98),
        stop:0.85 rgba(255, 255, 255, 0.98),
        stop:1 rgba(220, 53, 69, 0.12));
    border: 2px solid rgba(220, 53, 69, 0.3);
    border-radius: 20px;
    margin: 10px;

}

/* 标题文字样式 - 增强科技感 */
QLabel#titleLabel {
    color: #dc3545;
    font-weight: bold;

}

QLabel#subtitleLabel {
    color: #6c757d;
    font-style: italic;

}



/* 按钮统一样式 - 强化科技感设计 */
QPushButton {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
        stop:0 rgba(255, 255, 255, 0.98),
        stop:0.05 rgba(248, 249, 250, 0.95),
        stop:0.95 rgba(241, 243, 244, 0.92),
        stop:1 rgba(233, 236, 239, 0.9));
    border: 2px solid #dc3545;
    border-radius: 16px;
    color: #dc3545;
    font-family: "Microsoft YaHei UI", "Segoe UI", Arial;
    font-size: 11pt;
    font-weight: bold;
    padding: 8px 12px;
    text-align: center;
    margin: 6px;
    icon-size: 36px 36px;

}

QPushButton:hover {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
        stop:0 #dc3545,
        stop:0.05 #d32f2f,
        stop:0.5 #c62828,
        stop:0.95 #b71c1c,
        stop:1 #a71e2a);
    color: white;
    border: 2px solid #a71e2a;
    border-radius: 12px;
    font-weight: bold;
}

QPushButton:pressed {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
        stop:0 #b71c1c,
        stop:0.5 #a71e2a,
        stop:1 #8d1e27);
    color: white;
    border: 2px solid #8d1e27;
    border-radius: 12px;
}

QPushButton:focus {
    outline: none;
    border: 2px solid #dc3545 !important;

}

/* 强制统一所有按钮样式 */
QPushButton[objectName*="btn"], QPushButton[objectName*="protocol"] {
    border: 2px solid #dc3545 !important;
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
        stop:0 rgba(255, 255, 255, 0.98),
        stop:0.05 rgba(248, 249, 250, 0.95),
        stop:0.95 rgba(241, 243, 244, 0.92),
        stop:1 rgba(233, 236, 239, 0.9)) !important;
    color: #dc3545 !important;
}

/* 按钮hover效果 - 针对具体选择器 */
QPushButton[objectName*="btn"]:hover, QPushButton[objectName*="protocol"]:hover {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
        stop:0 #dc3545,
        stop:0.05 #d32f2f,
        stop:0.5 #c62828,
        stop:0.95 #b71c1c,
        stop:1 #a71e2a) !important;
    color: white !important;
    border: 2px solid #a71e2a !important;
    border-radius: 12px;
    font-weight: bold;
}

QPushButton[objectName*="btn"]:pressed, QPushButton[objectName*="protocol"]:pressed {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
        stop:0 #b71c1c,
        stop:0.5 #a71e2a,
        stop:1 #8d1e27) !important;
    color: white !important;
    border: 2px solid #8d1e27 !important;
    border-radius: 12px;
}

/* 所有按钮统一样式 - 无特殊处理 */
QPushButton#protocolButton, QPushButton#btnWave {
    font-size: 11pt;
    icon-size: 36px 36px;
}

/* 公司标签样式 */
QLabel#companyLabel {
    color: #6c757d;
    font-size: 11pt;
    font-weight: normal;
    background: rgba(255, 255, 255, 0.7);
    border-radius: 8px;
    padding: 8px;
    margin: 5px;
}</string>
  </property>
  <widget class="QWidget" name="centralwidget">
   <layout class="QVBoxLayout" name="verticalLayout">
    <property name="spacing">
     <number>20</number>
    </property>
    <property name="leftMargin">
     <number>20</number>
    </property>
    <property name="topMargin">
     <number>20</number>
    </property>
    <property name="rightMargin">
     <number>20</number>
    </property>
    <property name="bottomMargin">
     <number>20</number>
    </property>
    <item>
     <widget class="QFrame" name="titleFrame">
      <property name="frameShape">
       <enum>QFrame::Shape::NoFrame</enum>
      </property>
      <layout class="QHBoxLayout" name="titleLayout">
       <property name="spacing">
        <number>30</number>
       </property>
       <property name="leftMargin">
        <number>30</number>
       </property>
       <property name="topMargin">
        <number>20</number>
       </property>
       <property name="rightMargin">
        <number>30</number>
       </property>
       <property name="bottomMargin">
        <number>20</number>
       </property>
       <item>
        <widget class="QLabel" name="companyLogo">
         <property name="minimumSize">
          <size>
           <width>100</width>
           <height>100</height>
          </size>
         </property>
         <property name="maximumSize">
          <size>
           <width>100</width>
           <height>100</height>
          </size>
         </property>
         <property name="text">
          <string/>
         </property>
         <property name="pixmap">
          <pixmap resource="resources.qrc">:/icon/qiange.png</pixmap>
         </property>
         <property name="scaledContents">
          <bool>true</bool>
         </property>
         <property name="alignment">
          <set>Qt::AlignmentFlag::AlignCenter</set>
         </property>
        </widget>
       </item>
       <item>
        <layout class="QVBoxLayout" name="titleTextLayout">
         <property name="spacing">
          <number>5</number>
         </property>
         <item>
          <widget class="QLabel" name="titleLabel">
           <property name="font">
            <font>
             <family>Microsoft YaHei UI</family>
             <pointsize>26</pointsize>
             <bold>true</bold>
            </font>
           </property>
           <property name="text">
            <string>千歌电机上位机助手</string>
           </property>
           <property name="alignment">
            <set>Qt::AlignmentFlag::AlignLeading|Qt::AlignmentFlag::AlignLeft|Qt::AlignmentFlag::AlignVCenter</set>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QLabel" name="subtitleLabel">
           <property name="font">
            <font>
             <family>Microsoft YaHei UI</family>
             <pointsize>12</pointsize>
             <italic>true</italic>
            </font>
           </property>
           <property name="text">
            <string>专业的电机控制解决方案</string>
           </property>
           <property name="alignment">
            <set>Qt::AlignmentFlag::AlignLeading|Qt::AlignmentFlag::AlignLeft|Qt::AlignmentFlag::AlignVCenter</set>
           </property>
          </widget>
         </item>
        </layout>
       </item>
       <item>
        <spacer name="titleSpacer">
         <property name="orientation">
          <enum>Qt::Orientation::Horizontal</enum>
         </property>
         <property name="sizeHint" stdset="0">
          <size>
           <width>40</width>
           <height>20</height>
          </size>
         </property>
        </spacer>
       </item>
      </layout>
     </widget>
    </item>
    <item>
     <widget class="QFrame" name="buttonFrame">
      <property name="frameShape">
       <enum>QFrame::Shape::NoFrame</enum>
      </property>
      <layout class="QVBoxLayout" name="mainButtonLayout">
       <property name="spacing">
        <number>20</number>
       </property>
       <property name="leftMargin">
        <number>20</number>
       </property>
       <property name="topMargin">
        <number>20</number>
       </property>
       <property name="rightMargin">
        <number>20</number>
       </property>
       <property name="bottomMargin">
        <number>20</number>
       </property>
       <item>
        <layout class="QGridLayout" name="buttonsGridLayout">
         <property name="leftMargin">
          <number>20</number>
         </property>
         <property name="topMargin">
          <number>20</number>
         </property>
         <property name="rightMargin">
          <number>20</number>
         </property>
         <property name="bottomMargin">
          <number>20</number>
         </property>
         <property name="horizontalSpacing">
          <number>12</number>
         </property>
         <property name="verticalSpacing">
          <number>12</number>
         </property>
         <item row="0" column="0">
          <widget class="QPushButton" name="protocolButton">
           <property name="minimumSize">
            <size>
             <width>180</width>
             <height>90</height>
            </size>
           </property>
           <property name="font">
            <font>
             <family>Microsoft YaHei UI</family>
             <pointsize>12</pointsize>
             <bold>true</bold>
            </font>
           </property>
           <property name="text">
            <string>连接设置</string>
           </property>
           <property name="icon">
            <iconset resource="resources.qrc">
             <normaloff>:/icon/connect.png</normaloff>:/icon/connect.png</iconset>
           </property>
           <property name="iconSize">
            <size>
             <width>36</width>
             <height>36</height>
            </size>
           </property>
          </widget>
         </item>
         <item row="0" column="1">
          <widget class="QPushButton" name="btnWave">
           <property name="minimumSize">
            <size>
             <width>180</width>
             <height>90</height>
            </size>
           </property>
           <property name="font">
            <font>
             <family>Microsoft YaHei UI</family>
             <pointsize>12</pointsize>
             <bold>true</bold>
            </font>
           </property>
           <property name="text">
            <string>虚拟示波器</string>
           </property>
           <property name="icon">
            <iconset resource="resources.qrc">
             <normaloff>:/icon/oscilloscope.png</normaloff>:/icon/oscilloscope.png</iconset>
           </property>
           <property name="iconSize">
            <size>
             <width>36</width>
             <height>36</height>
            </size>
           </property>
          </widget>
         </item>
         <item row="0" column="2">
          <widget class="QPushButton" name="btnFunctionSettings">
           <property name="minimumSize">
            <size>
             <width>180</width>
             <height>90</height>
            </size>
           </property>
           <property name="font">
            <font>
             <family>Microsoft YaHei UI</family>
             <pointsize>12</pointsize>
             <bold>true</bold>
            </font>
           </property>
           <property name="text">
            <string>功率设置</string>
           </property>
           <property name="icon">
            <iconset resource="resources.qrc">
             <normaloff>:/icon/power_settings.png</normaloff>:/icon/power_settings.png</iconset>
           </property>
           <property name="iconSize">
            <size>
             <width>36</width>
             <height>36</height>
            </size>
           </property>
          </widget>
         </item>
         <item row="0" column="3">
          <widget class="QPushButton" name="btnMotion">
           <property name="minimumSize">
            <size>
             <width>180</width>
             <height>90</height>
            </size>
           </property>
           <property name="font">
            <font>
             <family>Microsoft YaHei UI</family>
             <pointsize>12</pointsize>
             <bold>true</bold>
            </font>
           </property>
           <property name="text">
            <string>运动控制</string>
           </property>
           <property name="icon">
            <iconset resource="resources.qrc">
             <normaloff>:/icon/motion.png</normaloff>:/icon/motion.png</iconset>
           </property>
           <property name="iconSize">
            <size>
             <width>36</width>
             <height>36</height>
            </size>
           </property>
          </widget>
         </item>
         <item row="1" column="0">
          <widget class="QPushButton" name="btnParameterStorage">
           <property name="minimumSize">
            <size>
             <width>180</width>
             <height>90</height>
            </size>
           </property>
           <property name="font">
            <font>
             <family>Microsoft YaHei UI</family>
             <pointsize>12</pointsize>
             <bold>true</bold>
            </font>
           </property>
           <property name="text">
            <string>参数存储</string>
           </property>
           <property name="icon">
            <iconset resource="resources.qrc">
             <normaloff>:/icon/parameter_storage.png</normaloff>:/icon/parameter_storage.png</iconset>
           </property>
           <property name="iconSize">
            <size>
             <width>36</width>
             <height>36</height>
            </size>
           </property>
          </widget>
         </item>
         <item row="1" column="1">
          <widget class="QPushButton" name="btnPIDSettings">
           <property name="minimumSize">
            <size>
             <width>180</width>
             <height>90</height>
            </size>
           </property>
           <property name="font">
            <font>
             <family>Microsoft YaHei UI</family>
             <pointsize>12</pointsize>
             <bold>true</bold>
            </font>
           </property>
           <property name="text">
            <string>PID设置</string>
           </property>
           <property name="icon">
            <iconset resource="resources.qrc">
             <normaloff>:/icon/pid.png</normaloff>:/icon/pid.png</iconset>
           </property>
           <property name="iconSize">
            <size>
             <width>36</width>
             <height>36</height>
            </size>
           </property>
          </widget>
         </item>
         <item row="1" column="2">
          <widget class="QPushButton" name="btnEncoder">
           <property name="minimumSize">
            <size>
             <width>180</width>
             <height>90</height>
            </size>
           </property>
           <property name="font">
            <font>
             <family>Microsoft YaHei UI</family>
             <pointsize>12</pointsize>
             <bold>true</bold>
            </font>
           </property>
           <property name="text">
            <string>编码器</string>
           </property>
           <property name="icon">
            <iconset resource="resources.qrc">
             <normaloff>:/icon/encoder.png</normaloff>:/icon/encoder.png</iconset>
           </property>
           <property name="iconSize">
            <size>
             <width>36</width>
             <height>36</height>
            </size>
           </property>
          </widget>
         </item>
         <item row="1" column="3">
          <widget class="QPushButton" name="btnStatusMonitor">
           <property name="minimumSize">
            <size>
             <width>180</width>
             <height>90</height>
            </size>
           </property>
           <property name="font">
            <font>
             <family>Microsoft YaHei UI</family>
             <pointsize>12</pointsize>
             <bold>true</bold>
            </font>
           </property>
           <property name="text">
            <string>状态监控</string>
           </property>
           <property name="icon">
            <iconset resource="resources.qrc">
             <normaloff>:/icon/status_monitor.png</normaloff>:/icon/status_monitor.png</iconset>
           </property>
           <property name="iconSize">
            <size>
             <width>36</width>
             <height>36</height>
            </size>
           </property>
          </widget>
         </item>
         <item row="2" column="0">
          <widget class="QPushButton" name="btnSafePower">
           <property name="minimumSize">
            <size>
             <width>180</width>
             <height>90</height>
            </size>
           </property>
           <property name="font">
            <font>
             <family>Microsoft YaHei UI</family>
             <pointsize>12</pointsize>
             <bold>true</bold>
            </font>
           </property>
           <property name="text">
            <string>安全电源</string>
           </property>
           <property name="icon">
            <iconset resource="resources.qrc">
             <normaloff>:/icon/safe_power.png</normaloff>:/icon/safe_power.png</iconset>
           </property>
           <property name="iconSize">
            <size>
             <width>36</width>
             <height>36</height>
            </size>
           </property>
          </widget>
         </item>
         <item row="2" column="1">
          <widget class="QPushButton" name="btnPositionProtection">
           <property name="minimumSize">
            <size>
             <width>180</width>
             <height>90</height>
            </size>
           </property>
           <property name="font">
            <font>
             <family>Microsoft YaHei UI</family>
             <pointsize>12</pointsize>
             <bold>true</bold>
            </font>
           </property>
           <property name="text">
            <string>位置保护</string>
           </property>
           <property name="icon">
            <iconset resource="resources.qrc">
             <normaloff>:/icon/position_protection.png</normaloff>:/icon/position_protection.png</iconset>
           </property>
           <property name="iconSize">
            <size>
             <width>36</width>
             <height>36</height>
            </size>
           </property>
          </widget>
         </item>
         <item row="2" column="2">
          <widget class="QPushButton" name="btnSafeSpeed">
           <property name="minimumSize">
            <size>
             <width>180</width>
             <height>90</height>
            </size>
           </property>
           <property name="font">
            <font>
             <family>Microsoft YaHei UI</family>
             <pointsize>12</pointsize>
             <bold>true</bold>
            </font>
           </property>
           <property name="text">
            <string>安全速度</string>
           </property>
           <property name="icon">
            <iconset resource="resources.qrc">
             <normaloff>:/icon/safe_speed.png</normaloff>:/icon/safe_speed.png</iconset>
           </property>
           <property name="iconSize">
            <size>
             <width>36</width>
             <height>36</height>
            </size>
           </property>
          </widget>
         </item>
         <item row="2" column="3">
          <widget class="QPushButton" name="btnFaultDiagnosis">
           <property name="minimumSize">
            <size>
             <width>180</width>
             <height>90</height>
            </size>
           </property>
           <property name="font">
            <font>
             <family>Microsoft YaHei UI</family>
             <pointsize>12</pointsize>
             <bold>true</bold>
            </font>
           </property>
           <property name="text">
            <string>故障诊断</string>
           </property>
           <property name="icon">
            <iconset resource="resources.qrc">
             <normaloff>:/icon/fault_diagnosis.png</normaloff>:/icon/fault_diagnosis.png</iconset>
           </property>
           <property name="iconSize">
            <size>
             <width>36</width>
             <height>36</height>
            </size>
           </property>
          </widget>
         </item>
         <item row="3" column="0">
          <widget class="QPushButton" name="btnCurrentLoop">
           <property name="minimumSize">
            <size>
             <width>180</width>
             <height>90</height>
            </size>
           </property>
           <property name="font">
            <font>
             <family>Microsoft YaHei UI</family>
             <pointsize>12</pointsize>
             <bold>true</bold>
            </font>
           </property>
           <property name="text">
            <string>电流环</string>
           </property>
           <property name="icon">
            <iconset resource="resources.qrc">
             <normaloff>:/icon/current_loop.png</normaloff>:/icon/current_loop.png</iconset>
           </property>
           <property name="iconSize">
            <size>
             <width>36</width>
             <height>36</height>
            </size>
           </property>
          </widget>
         </item>
         <item row="3" column="1">
          <widget class="QPushButton" name="btnIncrementalProtection">
           <property name="minimumSize">
            <size>
             <width>180</width>
             <height>90</height>
            </size>
           </property>
           <property name="font">
            <font>
             <family>Microsoft YaHei UI</family>
             <pointsize>12</pointsize>
             <bold>true</bold>
            </font>
           </property>
           <property name="text">
            <string>堵转保护</string>
           </property>
           <property name="icon">
            <iconset resource="resources.qrc">
             <normaloff>:/icon/stall_protection.png</normaloff>:/icon/stall_protection.png</iconset>
           </property>
           <property name="iconSize">
            <size>
             <width>36</width>
             <height>36</height>
            </size>
           </property>
          </widget>
         </item>
         <item row="3" column="2">
          <widget class="QPushButton" name="btnMechanicalZeroCalibration">
           <property name="minimumSize">
            <size>
             <width>180</width>
             <height>90</height>
            </size>
           </property>
           <property name="font">
            <font>
             <family>Microsoft YaHei UI</family>
             <pointsize>12</pointsize>
             <bold>true</bold>
            </font>
           </property>
           <property name="text">
            <string>机械零点标定</string>
           </property>
           <property name="icon">
            <iconset resource="resources.qrc">
             <normaloff>:/icon/mechanical_zero_calibration.png</normaloff>:/icon/mechanical_zero_calibration.png</iconset>
           </property>
           <property name="iconSize">
            <size>
             <width>36</width>
             <height>36</height>
            </size>
           </property>
          </widget>
         </item>
         <item row="3" column="3">
          <widget class="QPushButton" name="btnConsole">
           <property name="minimumSize">
            <size>
             <width>180</width>
             <height>90</height>
            </size>
           </property>
           <property name="font">
            <font>
             <family>Microsoft YaHei UI</family>
             <pointsize>12</pointsize>
             <bold>true</bold>
            </font>
           </property>
           <property name="text">
            <string>控制台</string>
           </property>
           <property name="icon">
            <iconset resource="resources.qrc">
             <normaloff>:/icon/console.png</normaloff>:/icon/console.png</iconset>
           </property>
           <property name="iconSize">
            <size>
             <width>36</width>
             <height>36</height>
            </size>
           </property>
          </widget>
         </item>
         <item row="4" column="0">
          <widget class="QPushButton" name="btnSave">
           <property name="minimumSize">
            <size>
             <width>180</width>
             <height>90</height>
            </size>
           </property>
           <property name="font">
            <font>
             <family>Microsoft YaHei UI</family>
             <pointsize>12</pointsize>
             <bold>true</bold>
            </font>
           </property>
           <property name="text">
            <string>保存</string>
           </property>
           <property name="icon">
            <iconset resource="resources.qrc">
             <normaloff>:/icon/save.png</normaloff>:/icon/save.png</iconset>
           </property>
           <property name="iconSize">
            <size>
             <width>36</width>
             <height>36</height>
            </size>
           </property>
          </widget>
         </item>
         <item row="4" column="1">
          <widget class="QPushButton" name="btnHelp">
           <property name="minimumSize">
            <size>
             <width>180</width>
             <height>90</height>
            </size>
           </property>
           <property name="font">
            <font>
             <family>Microsoft YaHei UI</family>
             <pointsize>12</pointsize>
             <bold>true</bold>
            </font>
           </property>
           <property name="text">
            <string>帮助</string>
           </property>
           <property name="icon">
            <iconset resource="resources.qrc">
             <normaloff>:/icon/help.png</normaloff>:/icon/help.png</iconset>
           </property>
           <property name="iconSize">
            <size>
             <width>36</width>
             <height>36</height>
            </size>
           </property>
          </widget>
         </item>
         <item row="4" column="2">
          <widget class="QPushButton" name="btnMultiMotorControl">
           <property name="minimumSize">
            <size>
             <width>180</width>
             <height>90</height>
            </size>
           </property>
           <property name="font">
            <font>
             <family>Microsoft YaHei UI</family>
             <pointsize>12</pointsize>
             <bold>true</bold>
            </font>
           </property>
           <property name="text">
            <string>多电机控制</string>
           </property>
           <property name="icon">
            <iconset resource="resources.qrc">
             <normaloff>:/icon/motor_setup.png</normaloff>:/icon/motor_setup.png</iconset>
           </property>
           <property name="iconSize">
            <size>
             <width>36</width>
             <height>36</height>
            </size>
           </property>
          </widget>
         </item>
         <item row="4" column="3">
          <widget class="QPushButton" name="btnExit">
           <property name="minimumSize">
            <size>
             <width>180</width>
             <height>90</height>
            </size>
           </property>
           <property name="font">
            <font>
             <family>Microsoft YaHei UI</family>
             <pointsize>12</pointsize>
             <bold>true</bold>
            </font>
           </property>
           <property name="text">
            <string>退出</string>
           </property>
           <property name="icon">
            <iconset resource="resources.qrc">
             <normaloff>:/icon/exit.png</normaloff>:/icon/exit.png</iconset>
           </property>
           <property name="iconSize">
            <size>
             <width>36</width>
             <height>36</height>
            </size>
           </property>
          </widget>
         </item>
        </layout>
       </item>
      </layout>
     </widget>
    </item>
    <item>
     <widget class="QLabel" name="companyLabel">
      <property name="font">
       <font>
        <family>Microsoft YaHei UI</family>
        <pointsize>12</pointsize>
       </font>
      </property>
      <property name="text">
       <string>洛阳千歌机器人科技有限公司</string>
      </property>
      <property name="alignment">
       <set>Qt::AlignmentFlag::AlignCenter</set>
      </property>
     </widget>
    </item>
   </layout>
  </widget>
 </widget>
 <resources>
  <include location="resources.qrc"/>
 </resources>
 <connections/>
</ui>
