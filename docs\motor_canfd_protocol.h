/**
 * @file motor_canfd_protocol.h
 * @brief 六电机控制系统CANFD协议头文件 (下位机专用)
 * @details 定义了完整的CANFD协议数据结构，适用于嵌入式下位机开发
 * @version V1.0
 * @date 2025-01-28
 * <AUTHOR>
 */

#ifndef MOTOR_CANFD_PROTOCOL_H
#define MOTOR_CANFD_PROTOCOL_H

#include <stdint.h>
#include <stdbool.h>

// 强制1字节对齐，确保数据结构一致性
#pragma pack(1)

// ================================
// 协议常量定义
// ================================

/// 电机数量
#define MOTOR_COUNT 6

/// 最大数据长度
#define MAX_DATA_LENGTH 64

/// 协议版本
#define PROTOCOL_VERSION 0x01

// ================================
// CAN ID 定义
// ================================

/// 系统广播命令ID
#define CAN_ID_SYSTEM_BROADCAST    0x000
/// 系统状态反馈ID  
#define CAN_ID_SYSTEM_STATUS       0x080
/// 电机控制命令基址
#define CAN_ID_MOTOR_CMD_BASE      0x100
/// 电机状态反馈基址
#define CAN_ID_MOTOR_STATUS_BASE   0x180
/// 紧急停止命令ID
#define CAN_ID_EMERGENCY_STOP      0x700
/// 心跳包ID
#define CAN_ID_HEARTBEAT           0x701

/// 根据电机ID计算控制命令CAN ID
#define GET_MOTOR_CMD_ID(motor_id)    (CAN_ID_MOTOR_CMD_BASE + motor_id)
/// 根据电机ID计算状态反馈CAN ID  
#define GET_MOTOR_STATUS_ID(motor_id) (CAN_ID_MOTOR_STATUS_BASE + motor_id)

// ================================
// 枚举类型定义
// ================================

/**
 * @brief 帧类型枚举
 */
typedef enum {
    FRAME_TYPE_CONTROL = 0x01,      ///< 控制命令帧
    FRAME_TYPE_STATUS = 0x02,       ///< 状态反馈帧
    FRAME_TYPE_BROADCAST = 0x03,    ///< 系统广播帧
    FRAME_TYPE_HEARTBEAT = 0x04,    ///< 心跳帧
    FRAME_TYPE_EMERGENCY = 0x05     ///< 紧急停止帧
} FrameType;

/**
 * @brief 控制命令类型枚举
 */
typedef enum {
    CMD_STOP = 0x00,           ///< 停止电机
    CMD_START = 0x01,          ///< 启动电机
    CMD_RESET = 0x02,          ///< 复位电机控制器
    CMD_ENABLE = 0x03,         ///< 使能电机
    CMD_DISABLE = 0x04,        ///< 失能电机
    CMD_SET_SPEED = 0x10,      ///< 设置目标速度
    CMD_SET_POSITION = 0x11,   ///< 设置目标位置
    CMD_JOG_FORWARD = 0x20,    ///< 正向点动
    CMD_JOG_REVERSE = 0x21,    ///< 反向点动
    CMD_HOME = 0x30,           ///< 回零操作
    CMD_CLEAR_ERROR = 0x40     ///< 清除错误状态
} CommandType;

/**
 * @brief 控制模式枚举
 */
typedef enum {
    MODE_POSITION = 0x01,      ///< 位置控制模式
    MODE_SPEED = 0x02,         ///< 速度控制模式
    MODE_TORQUE = 0x03,        ///< 转矩控制模式
    MODE_PROFILE_POS = 0x04,   ///< 轮廓位置模式
    MODE_PROFILE_VEL = 0x05,   ///< 轮廓速度模式
    MODE_HOMING = 0x06         ///< 回零模式
} ControlMode;

/**
 * @brief 状态标志位枚举
 */
typedef enum {
    STATUS_ENABLED = 0x01,         ///< 电机使能状态
    STATUS_RUNNING = 0x02,         ///< 电机运行状态
    STATUS_ERROR = 0x04,           ///< 错误状态
    STATUS_WARNING = 0x08,         ///< 警告状态
    STATUS_HOMED = 0x10,           ///< 已完成回零
    STATUS_MOVING = 0x20,          ///< 正在运动
    STATUS_TARGET_REACHED = 0x40,  ///< 已到达目标位置
    STATUS_EMERGENCY = 0x80        ///< 紧急停止状态
} StatusFlags;

/**
 * @brief 错误代码枚举
 */
typedef enum {
    ERR_NO_ERROR = 0x0000,         ///< 无错误
    ERR_OVERCURRENT = 0x0001,      ///< 过流保护
    ERR_OVERVOLTAGE = 0x0002,      ///< 过压保护
    ERR_UNDERVOLTAGE = 0x0003,     ///< 欠压保护
    ERR_OVERTEMPERATURE = 0x0004,  ///< 过温保护
    ERR_ENCODER_FAULT = 0x0005,    ///< 编码器故障
    ERR_COMMUNICATION = 0x0006,    ///< 通信故障
    ERR_POSITION_LIMIT = 0x0007,   ///< 位置限制
    ERR_SPEED_LIMIT = 0x0008,      ///< 速度限制
    ERR_FOLLOWING_ERROR = 0x0009,  ///< 跟随误差过大
    ERR_EMERGENCY_STOP = 0x000A,   ///< 紧急停止
    ERR_SYSTEM_FAULT = 0x000B      ///< 系统故障
} ErrorCode;

// ================================
// 数据结构定义
// ================================

/**
 * @brief 电机控制命令帧结构 (上位机→下位机)
 * @details 总长度: 20字节，适合CANFD传输
 */
typedef struct {
    // === 帧头信息 (4字节) ===
    uint8_t frame_type;        ///< 帧类型: 0x01=控制命令
    uint8_t motor_id;          ///< 电机ID: 1-6
    uint8_t sequence;          ///< 序列号(防重复，循环0-255)
    uint8_t priority;          ///< 优先级: 0=最高, 255=最低
    
    // === 控制参数 (12字节) ===
    uint8_t command;           ///< 控制命令(见CommandType枚举)
    uint8_t control_mode;      ///< 控制模式(见ControlMode枚举)
    uint16_t target_speed;     ///< 目标速度 (RPM, 0-65535)
    uint32_t target_position;  ///< 目标位置 (脉冲数)
    uint16_t acceleration;     ///< 加速度 (RPM/s)
    uint16_t deceleration;     ///< 减速度 (RPM/s)
    
    // === 校验和时间戳 (4字节) ===
    uint16_t checksum;         ///< 校验和(CRC16)
    uint16_t timestamp;        ///< 时间戳(毫秒低16位)
} MotorControlFrame;

/**
 * @brief 电机状态反馈帧结构 (下位机→上位机)
 * @details 总长度: 40字节，包含完整的电机状态信息
 */
typedef struct {
    // === 帧头信息 (4字节) ===
    uint8_t frame_type;        ///< 帧类型: 0x02=状态反馈
    uint8_t motor_id;          ///< 电机ID: 1-6
    uint8_t sequence;          ///< 序列号(与控制命令对应)
    uint8_t status_flags;      ///< 状态标志位(见StatusFlags)
    
    // === 实时数据 (24字节) - 给示波器显示 ===
    uint16_t current_speed;    ///< 当前速度 (RPM) → 示波器蓝色通道
    uint32_t current_position; ///< 当前位置 (脉冲数)
    uint16_t motor_current;    ///< 电机电流 (mA) → 示波器红色通道
    uint16_t motor_voltage;    ///< 电机电压 (mV) → 示波器绿色通道
    uint16_t temperature;      ///< 温度 (0.1°C) → 示波器黄色通道
    uint16_t torque;           ///< 转矩 (0.1Nm)
    uint32_t encoder_position; ///< 编码器位置 (脉冲)
    uint16_t following_error;  ///< 跟随误差 (脉冲)
    uint16_t load_ratio;       ///< 负载率 (0.1%)
    
    // === 错误和诊断信息 (8字节) ===
    uint16_t error_code;       ///< 错误代码(见ErrorCode枚举)
    uint16_t warning_code;     ///< 警告代码
    uint32_t diagnostic_info;  ///< 诊断信息
    
    // === 校验和时间戳 (4字节) ===
    uint16_t checksum;         ///< 校验和(CRC16)
    uint16_t timestamp;        ///< 时间戳(毫秒低16位)
} MotorStatusFrame;

/**
 * @brief 心跳帧结构
 * @details 总长度: 8字节
 */
typedef struct {
    uint8_t frame_type;        ///< 帧类型: 0x04=心跳帧
    uint8_t system_status;     ///< 系统状态
    uint16_t alive_counter;    ///< 心跳计数器
    uint32_t timestamp;        ///< 完整时间戳
} HeartbeatFrame;

/**
 * @brief 紧急停止帧结构
 * @details 总长度: 4字节
 */
typedef struct {
    uint8_t frame_type;        ///< 帧类型: 0x05=紧急停止
    uint8_t stop_reason;       ///< 停止原因
    uint16_t reserved;         ///< 保留字段
} EmergencyStopFrame;

/**
 * @brief 协议管理器结构
 * @details 用于管理协议状态和数据
 */
typedef struct {
    uint8_t last_sequence[MOTOR_COUNT];        ///< 最后处理的序列号
    uint32_t last_command_time[MOTOR_COUNT];   ///< 最后命令时间
    MotorStatusFrame status[MOTOR_COUNT];      ///< 电机状态数据
    uint16_t heartbeat_counter;                ///< 心跳计数器
    uint32_t system_start_time;                ///< 系统启动时间
} ProtocolManager;

// 恢复默认对齐
#pragma pack()

// ================================
// 函数声明
// ================================

/**
 * @brief 计算CRC16校验和
 * @param data 数据指针
 * @param length 数据长度
 * @return CRC16校验和
 */
uint16_t calculate_crc16(const uint8_t* data, uint16_t length);

/**
 * @brief 验证CRC16校验和
 * @param frame 数据帧指针
 * @param length 数据长度
 * @return 校验是否通过
 */
bool verify_crc16(const void* frame, uint16_t length);

/**
 * @brief 填充控制帧校验和
 * @param frame 控制帧指针
 */
void fill_control_frame_checksum(MotorControlFrame* frame);

/**
 * @brief 填充状态帧校验和
 * @param frame 状态帧指针
 */
void fill_status_frame_checksum(MotorStatusFrame* frame);

/**
 * @brief 获取当前时间戳
 * @return 当前时间戳(毫秒)
 */
uint32_t get_current_timestamp(void);

/**
 * @brief 初始化协议管理器
 * @param manager 协议管理器指针
 */
void init_protocol_manager(ProtocolManager* manager);

/**
 * @brief 处理控制命令
 * @param manager 协议管理器指针
 * @param frame 控制命令帧指针
 * @return 处理是否成功
 */
bool process_control_command(ProtocolManager* manager, const MotorControlFrame* frame);

/**
 * @brief 更新电机状态
 * @param manager 协议管理器指针
 * @param motor_id 电机ID (1-6)
 * @param status 状态数据指针
 */
void update_motor_status(ProtocolManager* manager, uint8_t motor_id, const MotorStatusFrame* status);

/**
 * @brief 获取电机状态
 * @param manager 协议管理器指针
 * @param motor_id 电机ID (1-6)
 * @return 状态数据指针
 */
const MotorStatusFrame* get_motor_status(const ProtocolManager* manager, uint8_t motor_id);

/**
 * @brief 检查通信超时
 * @param manager 协议管理器指针
 * @param timeout_ms 超时时间(毫秒)
 * @return 超时的电机掩码
 */
uint8_t check_communication_timeout(const ProtocolManager* manager, uint32_t timeout_ms);

#endif // MOTOR_CANFD_PROTOCOL_H
