# C++ objects and libs
*.slo
*.lo
*.o
*.a
*.la
*.lai
*.so
*.so.*
*.dll
*.dylib

# Qt-es
object_script.*.Release
object_script.*.Debug
*_plugin_import.cpp
/.qmake.cache
/.qmake.stash
*.pro.user
*.pro.user.*
*.qbs.user
*.qbs.user.*
*.moc
moc_*.cpp
moc_*.h
qrc_*.cpp
ui_*.h
*.qmlc
*.jsc
Makefile*
*build-*
*.qm
*.prl

# Qt unit tests
target_wrapper.*

# QtCreator
*.autosave

# QtCreator Qml
*.qmlproject.user
*.qmlproject.user.*

# QtCreator CMake
CMakeLists.txt.user*

# QtCreator local machine specific files for imported projects
*creator.user*

# Debug and Release folders
debug/
release/

# Build directories
build/
build-*/

# Qt Creator clangd cache
.qtc_clangd/

# IDE files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Temporary files
*.tmp
*.temp
*.log
*.bak
*.orig

# Object files
*.obj

# Executables
*.exe
*.out
*.app

# Debug files
*.pdb
*.ilk
*.exp

# Other projects
Hostcomputer/

# 项目特定忽略文件
.cunzhi-memory/
*.ui.backup*
*_clean.ui
*_modern.ui
test_*
*_test.*
verify_*
*.bat.log

# 编译脚本
clean_*.bat
compile_*.bat
rebuild*.bat
force_*.bat
run_*.bat
simple_*.bat

# Python脚本生成的文件
*.pyc
__pycache__/