<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>MultiMotorControlPure</class>
 <widget class="QWidget" name="MultiMotorControlPure">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>1200</width>
    <height>800</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>六电机控制系统 - 纯UI版本</string>
  </property>
  <property name="styleSheet">
   <string>/* 主界面样式 - 红白配色风格 */
QWidget {
    background-color: #FFFFFF;
    font-family: &quot;Microsoft YaHei&quot;, &quot;SimHei&quot;, sans-serif;
    font-size: 13px;
}

/* 标题样式 */
QLabel#titleLabel {
    font-size: 18px;
    font-weight: bold;
    color: #CC0000;
    background-color: #FFFFFF;
    border: 2px solid #CC0000;
    border-radius: 8px;
    padding: 8px;
    margin: 5px;
}

/* 状态标签样式 */
QLabel {
    color: #333333;
    padding: 2px;
}

/* 按钮基础样式 */
QPushButton {
    background-color: #FFFFFF;
    border: 2px solid #CC0000;
    border-radius: 6px;
    color: #CC0000;
    font-weight: bold;
    padding: 6px 12px;
    min-height: 25px;
}

QPushButton:hover {
    background-color: #FFE6E6;
}

QPushButton:pressed {
    background-color: #CC0000;
    color: #FFFFFF;
}

/* 主要按钮样式 */
QPushButton#primaryButton {
    background-color: #CC0000;
    color: #FFFFFF;
}

QPushButton#primaryButton:hover {
    background-color: #AA0000;
}

/* 分组框样式 */
QGroupBox {
    font-weight: bold;
    border: 2px solid #CC0000;
    border-radius: 8px;
    margin-top: 10px;
    padding-top: 10px;
    background-color: #FFFFFF;
}

QGroupBox::title {
    subcontrol-origin: margin;
    left: 10px;
    padding: 0 8px 0 8px;
    color: #CC0000;
    font-size: 13px;
    font-weight: bold;
    background-color: #FFFFFF;
}

/* 输入框样式 */
QLineEdit, QSpinBox, QComboBox {
    border: 1px solid #CCCCCC;
    border-radius: 4px;
    padding: 4px;
    background-color: #FFFFFF;
}

QLineEdit:focus, QSpinBox:focus, QComboBox:focus {
    border: 2px solid #CC0000;
}

/* 进度条样式 */
QProgressBar {
    border: 1px solid #CCCCCC;
    border-radius: 4px;
    text-align: center;
    background-color: #F0F0F0;
}

QProgressBar::chunk {
    background-color: #CC0000;
    border-radius: 3px;
}
</string>
  </property>
  <layout class="QVBoxLayout" name="mainLayout">
   <property name="spacing">
    <number>10</number>
   </property>
   <property name="leftMargin">
    <number>10</number>
   </property>
   <property name="topMargin">
    <number>10</number>
   </property>
   <property name="rightMargin">
    <number>10</number>
   </property>
   <property name="bottomMargin">
    <number>10</number>
   </property>
   <item>
    <widget class="QLabel" name="titleLabel">
     <property name="text">
      <string>多电机控制系统 - CANFD协议</string>
     </property>
     <property name="alignment">
      <set>Qt::AlignmentFlag::AlignCenter</set>
     </property>
    </widget>
   </item>
   <item>
    <widget class="QFrame" name="controlButtonFrame">
     <property name="frameShape">
      <enum>QFrame::Shape::StyledPanel</enum>
     </property>
     <property name="frameShadow">
      <enum>QFrame::Shadow::Raised</enum>
     </property>
     <layout class="QHBoxLayout" name="controlButtonLayout">
      <property name="spacing">
       <number>10</number>
      </property>
      <item>
       <widget class="QPushButton" name="startAllBtn">
        <property name="text">
         <string>全部启动</string>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QPushButton" name="stopAllBtn">
        <property name="text">
         <string>全部停止</string>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QPushButton" name="enableAllBtn">
        <property name="text">
         <string>全部使能</string>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QPushButton" name="disableAllBtn">
        <property name="text">
         <string>全部失能</string>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QPushButton" name="emergencyStopBtn">
        <property name="text">
         <string>紧急停止</string>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QPushButton" name="clearAllErrorsBtn">
        <property name="text">
         <string>清除所有错误</string>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QPushButton" name="oscilloscopeBtn">
        <property name="text">
         <string>示波器</string>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QPushButton" name="backBtn">
        <property name="text">
         <string>返回主界面</string>
        </property>
       </widget>
      </item>
     </layout>
    </widget>
   </item>
   <item>
    <widget class="QScrollArea" name="motorScrollArea">
     <property name="verticalScrollBarPolicy">
      <enum>Qt::ScrollBarPolicy::ScrollBarAsNeeded</enum>
     </property>
     <property name="horizontalScrollBarPolicy">
      <enum>Qt::ScrollBarPolicy::ScrollBarAsNeeded</enum>
     </property>
     <property name="widgetResizable">
      <bool>true</bool>
     </property>
     <widget class="QWidget" name="scrollAreaWidgetContents">
      <property name="geometry">
       <rect>
        <x>0</x>
        <y>0</y>
        <width>1400</width>
        <height>950</height>
       </rect>
      </property>
      <layout class="QGridLayout" name="motorGridLayout">
       <property name="leftMargin">
        <number>15</number>
       </property>
       <property name="topMargin">
        <number>15</number>
       </property>
       <property name="rightMargin">
        <number>15</number>
       </property>
       <property name="bottomMargin">
        <number>15</number>
       </property>
       <property name="spacing">
        <number>20</number>
       </property>
       <item row="0" column="0">
        <widget class="QGroupBox" name="motor1Group">
         <property name="minimumSize">
          <size>
           <width>380</width>
           <height>380</height>
          </size>
         </property>
         <property name="maximumSize">
          <size>
           <width>450</width>
           <height>450</height>
          </size>
         </property>
         <property name="title">
          <string>电机 1</string>
         </property>
         <layout class="QVBoxLayout" name="motor1Layout">
          <property name="spacing">
           <number>12</number>
          </property>
          <property name="leftMargin">
           <number>15</number>
          </property>
          <property name="topMargin">
           <number>15</number>
          </property>
          <property name="rightMargin">
           <number>15</number>
          </property>
          <property name="bottomMargin">
           <number>15</number>
          </property>
          <item>
           <widget class="QGroupBox" name="motor1StatusGroup">
            <property name="title">
             <string>状态信息</string>
            </property>
            <layout class="QGridLayout" name="motor1StatusLayout">
             <property name="leftMargin">
              <number>10</number>
             </property>
             <property name="topMargin">
              <number>10</number>
             </property>
             <property name="rightMargin">
              <number>10</number>
             </property>
             <property name="bottomMargin">
              <number>10</number>
             </property>
             <property name="spacing">
              <number>8</number>
             </property>
             <item row="0" column="0">
              <widget class="QLabel" name="motor1StatusLabel_1">
               <property name="text">
                <string>状态:</string>
               </property>
              </widget>
             </item>
             <item row="0" column="1">
              <widget class="QLabel" name="motor1StatusValue">
               <property name="minimumSize">
                <size>
                 <width>100</width>
                 <height>25</height>
                </size>
               </property>
               <property name="text">
                <string>未连接</string>
               </property>
               <property name="alignment">
                <set>Qt::AlignmentFlag::AlignLeading|Qt::AlignmentFlag::AlignLeft|Qt::AlignmentFlag::AlignVCenter</set>
               </property>
              </widget>
             </item>
             <item row="1" column="0">
              <widget class="QLabel" name="motor1SpeedLabel">
               <property name="text">
                <string>速度:</string>
               </property>
              </widget>
             </item>
             <item row="1" column="1">
              <widget class="QLabel" name="motor1SpeedValue">
               <property name="minimumSize">
                <size>
                 <width>100</width>
                 <height>25</height>
                </size>
               </property>
               <property name="text">
                <string>0 RPM</string>
               </property>
               <property name="alignment">
                <set>Qt::AlignmentFlag::AlignLeading|Qt::AlignmentFlag::AlignLeft|Qt::AlignmentFlag::AlignVCenter</set>
               </property>
              </widget>
             </item>
             <item row="2" column="0">
              <widget class="QLabel" name="motor1PositionLabel">
               <property name="text">
                <string>位置:</string>
               </property>
              </widget>
             </item>
             <item row="2" column="1">
              <widget class="QLabel" name="motor1PositionValue">
               <property name="minimumSize">
                <size>
                 <width>100</width>
                 <height>25</height>
                </size>
               </property>
               <property name="text">
                <string>0</string>
               </property>
               <property name="alignment">
                <set>Qt::AlignmentFlag::AlignLeading|Qt::AlignmentFlag::AlignLeft|Qt::AlignmentFlag::AlignVCenter</set>
               </property>
              </widget>
             </item>
            </layout>
           </widget>
          </item>
          <item>
           <widget class="QGroupBox" name="motor1ControlGroup">
            <property name="title">
             <string>控制参数</string>
            </property>
            <layout class="QGridLayout" name="motor1ControlLayout">
             <property name="spacing">
              <number>5</number>
             </property>
             <item row="0" column="0">
              <widget class="QLabel" name="motor1ModeLabel">
               <property name="text">
                <string>模式:</string>
               </property>
              </widget>
             </item>
             <item row="0" column="1">
              <widget class="QComboBox" name="motor1ModeCombo">
               <item>
                <property name="text">
                 <string>速度控制</string>
                </property>
               </item>
               <item>
                <property name="text">
                 <string>位置控制</string>
                </property>
               </item>
              </widget>
             </item>
             <item row="1" column="0">
              <widget class="QLabel" name="motor1TargetSpeedLabel">
               <property name="text">
                <string>目标速度:</string>
               </property>
              </widget>
             </item>
             <item row="1" column="1">
              <widget class="QSpinBox" name="motor1TargetSpeedSpin">
               <property name="suffix">
                <string> RPM</string>
               </property>
               <property name="minimum">
                <number>0</number>
               </property>
               <property name="maximum">
                <number>3000</number>
               </property>
               <property name="value">
                <number>1000</number>
               </property>
              </widget>
             </item>
            </layout>
           </widget>
          </item>
          <item>
           <widget class="QGroupBox" name="motor1ButtonGroup">
            <property name="title">
             <string>控制操作</string>
            </property>
            <layout class="QGridLayout" name="motor1ButtonLayout">
             <property name="leftMargin">
              <number>8</number>
             </property>
             <property name="topMargin">
              <number>8</number>
             </property>
             <property name="rightMargin">
              <number>8</number>
             </property>
             <property name="bottomMargin">
              <number>8</number>
             </property>
             <property name="spacing">
              <number>6</number>
             </property>
             <item row="0" column="0">
              <widget class="QPushButton" name="motor1EnableBtn">
               <property name="minimumSize">
                <size>
                 <width>75</width>
                 <height>35</height>
                </size>
               </property>
               <property name="maximumSize">
                <size>
                 <width>85</width>
                 <height>40</height>
                </size>
               </property>
               <property name="text">
                <string>使能</string>
               </property>
               <property name="checkable">
                <bool>true</bool>
               </property>
              </widget>
             </item>
             <item row="0" column="1">
              <widget class="QPushButton" name="motor1StartBtn">
               <property name="minimumSize">
                <size>
                 <width>75</width>
                 <height>35</height>
                </size>
               </property>
               <property name="maximumSize">
                <size>
                 <width>85</width>
                 <height>40</height>
                </size>
               </property>
               <property name="text">
                <string>启动</string>
               </property>
              </widget>
             </item>
             <item row="1" column="0">
              <widget class="QPushButton" name="motor1StopBtn">
               <property name="minimumSize">
                <size>
                 <width>75</width>
                 <height>35</height>
                </size>
               </property>
               <property name="maximumSize">
                <size>
                 <width>85</width>
                 <height>40</height>
                </size>
               </property>
               <property name="text">
                <string>停止</string>
               </property>
              </widget>
             </item>
             <item row="1" column="1">
              <widget class="QPushButton" name="motor1SetSpeedBtn">
               <property name="minimumSize">
                <size>
                 <width>75</width>
                 <height>35</height>
                </size>
               </property>
               <property name="maximumSize">
                <size>
                 <width>85</width>
                 <height>40</height>
                </size>
               </property>
               <property name="text">
                <string>设置速度</string>
               </property>
              </widget>
             </item>
            </layout>
           </widget>
          </item>
         </layout>
        </widget>
       </item>
       <item row="0" column="1">
        <widget class="QGroupBox" name="motor2Group">
         <property name="minimumSize">
          <size>
           <width>380</width>
           <height>380</height>
          </size>
         </property>
         <property name="maximumSize">
          <size>
           <width>450</width>
           <height>450</height>
          </size>
         </property>
         <property name="title">
          <string>电机 2</string>
         </property>
         <layout class="QVBoxLayout" name="motor2Layout">
          <property name="spacing">
           <number>12</number>
          </property>
          <property name="leftMargin">
           <number>15</number>
          </property>
          <property name="topMargin">
           <number>15</number>
          </property>
          <property name="rightMargin">
           <number>15</number>
          </property>
          <property name="bottomMargin">
           <number>15</number>
          </property>
          <item>
           <widget class="QGroupBox" name="motor2StatusGroup">
            <property name="title">
             <string>状态信息</string>
            </property>
            <layout class="QGridLayout" name="motor2StatusLayout">
             <property name="leftMargin">
              <number>10</number>
             </property>
             <property name="topMargin">
              <number>10</number>
             </property>
             <property name="rightMargin">
              <number>10</number>
             </property>
             <property name="bottomMargin">
              <number>10</number>
             </property>
             <property name="spacing">
              <number>8</number>
             </property>
             <item row="0" column="0">
              <widget class="QLabel" name="motor2StatusLabel_1">
               <property name="text">
                <string>状态:</string>
               </property>
              </widget>
             </item>
             <item row="0" column="1">
              <widget class="QLabel" name="motor2StatusValue">
               <property name="minimumSize">
                <size>
                 <width>80</width>
                 <height>0</height>
                </size>
               </property>
               <property name="text">
                <string>未连接</string>
               </property>
              </widget>
             </item>
             <item row="1" column="0">
              <widget class="QLabel" name="motor2SpeedLabel">
               <property name="text">
                <string>速度:</string>
               </property>
              </widget>
             </item>
             <item row="1" column="1">
              <widget class="QLabel" name="motor2SpeedValue">
               <property name="minimumSize">
                <size>
                 <width>80</width>
                 <height>0</height>
                </size>
               </property>
               <property name="text">
                <string>0 RPM</string>
               </property>
              </widget>
             </item>
             <item row="2" column="0">
              <widget class="QLabel" name="motor2PositionLabel">
               <property name="text">
                <string>位置:</string>
               </property>
              </widget>
             </item>
             <item row="2" column="1">
              <widget class="QLabel" name="motor2PositionValue">
               <property name="minimumSize">
                <size>
                 <width>80</width>
                 <height>0</height>
                </size>
               </property>
               <property name="text">
                <string>0</string>
               </property>
              </widget>
             </item>
            </layout>
           </widget>
          </item>
          <item>
           <widget class="QGroupBox" name="motor2ControlGroup">
            <property name="title">
             <string>控制参数</string>
            </property>
            <layout class="QGridLayout" name="motor2ControlLayout">
             <property name="spacing">
              <number>5</number>
             </property>
             <item row="0" column="0">
              <widget class="QLabel" name="motor2ModeLabel">
               <property name="text">
                <string>模式:</string>
               </property>
              </widget>
             </item>
             <item row="0" column="1">
              <widget class="QComboBox" name="motor2ModeCombo">
               <item>
                <property name="text">
                 <string>速度控制</string>
                </property>
               </item>
               <item>
                <property name="text">
                 <string>位置控制</string>
                </property>
               </item>
              </widget>
             </item>
             <item row="1" column="0">
              <widget class="QLabel" name="motor2TargetSpeedLabel">
               <property name="text">
                <string>目标速度:</string>
               </property>
              </widget>
             </item>
             <item row="1" column="1">
              <widget class="QSpinBox" name="motor2TargetSpeedSpin">
               <property name="suffix">
                <string> RPM</string>
               </property>
               <property name="minimum">
                <number>0</number>
               </property>
               <property name="maximum">
                <number>3000</number>
               </property>
               <property name="value">
                <number>1000</number>
               </property>
              </widget>
             </item>
            </layout>
           </widget>
          </item>
          <item>
           <widget class="QGroupBox" name="motor2ButtonGroup">
            <property name="title">
             <string>控制操作</string>
            </property>
            <layout class="QGridLayout" name="motor2ButtonLayout">
             <property name="leftMargin">
              <number>8</number>
             </property>
             <property name="topMargin">
              <number>8</number>
             </property>
             <property name="rightMargin">
              <number>8</number>
             </property>
             <property name="bottomMargin">
              <number>8</number>
             </property>
             <property name="spacing">
              <number>6</number>
             </property>
             <item row="0" column="0">
              <widget class="QPushButton" name="motor2EnableBtn">
               <property name="minimumSize">
                <size>
                 <width>75</width>
                 <height>35</height>
                </size>
               </property>
               <property name="maximumSize">
                <size>
                 <width>85</width>
                 <height>40</height>
                </size>
               </property>
               <property name="text">
                <string>使能</string>
               </property>
               <property name="checkable">
                <bool>true</bool>
               </property>
              </widget>
             </item>
             <item row="0" column="1">
              <widget class="QPushButton" name="motor2StartBtn">
               <property name="minimumSize">
                <size>
                 <width>75</width>
                 <height>35</height>
                </size>
               </property>
               <property name="maximumSize">
                <size>
                 <width>85</width>
                 <height>40</height>
                </size>
               </property>
               <property name="text">
                <string>启动</string>
               </property>
              </widget>
             </item>
             <item row="1" column="0">
              <widget class="QPushButton" name="motor2StopBtn">
               <property name="minimumSize">
                <size>
                 <width>75</width>
                 <height>35</height>
                </size>
               </property>
               <property name="maximumSize">
                <size>
                 <width>85</width>
                 <height>40</height>
                </size>
               </property>
               <property name="text">
                <string>停止</string>
               </property>
              </widget>
             </item>
             <item row="1" column="1">
              <widget class="QPushButton" name="motor2SetSpeedBtn">
               <property name="minimumSize">
                <size>
                 <width>75</width>
                 <height>35</height>
                </size>
               </property>
               <property name="maximumSize">
                <size>
                 <width>85</width>
                 <height>40</height>
                </size>
               </property>
               <property name="text">
                <string>设置速度</string>
               </property>
              </widget>
             </item>
            </layout>
           </widget>
          </item>
         </layout>
        </widget>
       </item>
       <item row="0" column="2">
        <widget class="QGroupBox" name="motor3Group">
         <property name="minimumSize">
          <size>
           <width>380</width>
           <height>380</height>
          </size>
         </property>
         <property name="maximumSize">
          <size>
           <width>450</width>
           <height>450</height>
          </size>
         </property>
         <property name="title">
          <string>电机 3</string>
         </property>
         <layout class="QVBoxLayout" name="motor3Layout">
          <property name="spacing">
           <number>12</number>
          </property>
          <property name="leftMargin">
           <number>15</number>
          </property>
          <property name="topMargin">
           <number>15</number>
          </property>
          <property name="rightMargin">
           <number>15</number>
          </property>
          <property name="bottomMargin">
           <number>15</number>
          </property>
          <item>
           <widget class="QGroupBox" name="motor3StatusGroup">
            <property name="title">
             <string>状态信息</string>
            </property>
            <layout class="QGridLayout" name="motor3StatusLayout">
             <property name="leftMargin">
              <number>10</number>
             </property>
             <property name="topMargin">
              <number>10</number>
             </property>
             <property name="rightMargin">
              <number>10</number>
             </property>
             <property name="bottomMargin">
              <number>10</number>
             </property>
             <property name="spacing">
              <number>8</number>
             </property>
             <item row="0" column="0">
              <widget class="QLabel" name="motor3StatusLabel_1">
               <property name="text">
                <string>状态:</string>
               </property>
              </widget>
             </item>
             <item row="0" column="1">
              <widget class="QLabel" name="motor3StatusValue">
               <property name="minimumSize">
                <size>
                 <width>80</width>
                 <height>0</height>
                </size>
               </property>
               <property name="text">
                <string>未连接</string>
               </property>
              </widget>
             </item>
             <item row="1" column="0">
              <widget class="QLabel" name="motor3SpeedLabel">
               <property name="text">
                <string>速度:</string>
               </property>
              </widget>
             </item>
             <item row="1" column="1">
              <widget class="QLabel" name="motor3SpeedValue">
               <property name="minimumSize">
                <size>
                 <width>80</width>
                 <height>0</height>
                </size>
               </property>
               <property name="text">
                <string>0 RPM</string>
               </property>
              </widget>
             </item>
             <item row="2" column="0">
              <widget class="QLabel" name="motor3PositionLabel">
               <property name="text">
                <string>位置:</string>
               </property>
              </widget>
             </item>
             <item row="2" column="1">
              <widget class="QLabel" name="motor3PositionValue">
               <property name="minimumSize">
                <size>
                 <width>80</width>
                 <height>0</height>
                </size>
               </property>
               <property name="text">
                <string>0</string>
               </property>
              </widget>
             </item>
            </layout>
           </widget>
          </item>
          <item>
           <widget class="QGroupBox" name="motor3ControlGroup">
            <property name="title">
             <string>控制参数</string>
            </property>
            <layout class="QGridLayout" name="motor3ControlLayout">
             <property name="spacing">
              <number>5</number>
             </property>
             <item row="0" column="0">
              <widget class="QLabel" name="motor3ModeLabel">
               <property name="text">
                <string>模式:</string>
               </property>
              </widget>
             </item>
             <item row="0" column="1">
              <widget class="QComboBox" name="motor3ModeCombo">
               <item>
                <property name="text">
                 <string>速度控制</string>
                </property>
               </item>
               <item>
                <property name="text">
                 <string>位置控制</string>
                </property>
               </item>
              </widget>
             </item>
             <item row="1" column="0">
              <widget class="QLabel" name="motor3TargetSpeedLabel">
               <property name="text">
                <string>目标速度:</string>
               </property>
              </widget>
             </item>
             <item row="1" column="1">
              <widget class="QSpinBox" name="motor3TargetSpeedSpin">
               <property name="suffix">
                <string> RPM</string>
               </property>
               <property name="minimum">
                <number>0</number>
               </property>
               <property name="maximum">
                <number>3000</number>
               </property>
               <property name="value">
                <number>1000</number>
               </property>
              </widget>
             </item>
            </layout>
           </widget>
          </item>
          <item>
           <widget class="QGroupBox" name="motor3ButtonGroup">
            <property name="title">
             <string>控制操作</string>
            </property>
            <layout class="QGridLayout" name="motor3ButtonLayout">
             <property name="leftMargin">
              <number>8</number>
             </property>
             <property name="topMargin">
              <number>8</number>
             </property>
             <property name="rightMargin">
              <number>8</number>
             </property>
             <property name="bottomMargin">
              <number>8</number>
             </property>
             <property name="spacing">
              <number>6</number>
             </property>
             <item row="0" column="0">
              <widget class="QPushButton" name="motor3EnableBtn">
               <property name="minimumSize">
                <size>
                 <width>75</width>
                 <height>35</height>
                </size>
               </property>
               <property name="maximumSize">
                <size>
                 <width>85</width>
                 <height>40</height>
                </size>
               </property>
               <property name="text">
                <string>使能</string>
               </property>
               <property name="checkable">
                <bool>true</bool>
               </property>
              </widget>
             </item>
             <item row="0" column="1">
              <widget class="QPushButton" name="motor3StartBtn">
               <property name="minimumSize">
                <size>
                 <width>75</width>
                 <height>35</height>
                </size>
               </property>
               <property name="maximumSize">
                <size>
                 <width>85</width>
                 <height>40</height>
                </size>
               </property>
               <property name="text">
                <string>启动</string>
               </property>
              </widget>
             </item>
             <item row="1" column="0">
              <widget class="QPushButton" name="motor3StopBtn">
               <property name="minimumSize">
                <size>
                 <width>75</width>
                 <height>35</height>
                </size>
               </property>
               <property name="maximumSize">
                <size>
                 <width>85</width>
                 <height>40</height>
                </size>
               </property>
               <property name="text">
                <string>停止</string>
               </property>
              </widget>
             </item>
             <item row="1" column="1">
              <widget class="QPushButton" name="motor3SetSpeedBtn">
               <property name="minimumSize">
                <size>
                 <width>75</width>
                 <height>35</height>
                </size>
               </property>
               <property name="maximumSize">
                <size>
                 <width>85</width>
                 <height>40</height>
                </size>
               </property>
               <property name="text">
                <string>设置速度</string>
               </property>
              </widget>
             </item>
            </layout>
           </widget>
          </item>
         </layout>
        </widget>
       </item>
       <item row="1" column="0">
        <widget class="QGroupBox" name="motor4Group">
         <property name="minimumSize">
          <size>
           <width>380</width>
           <height>380</height>
          </size>
         </property>
         <property name="maximumSize">
          <size>
           <width>450</width>
           <height>450</height>
          </size>
         </property>
         <property name="title">
          <string>电机 4</string>
         </property>
         <layout class="QVBoxLayout" name="motor4Layout">
          <property name="spacing">
           <number>12</number>
          </property>
          <property name="leftMargin">
           <number>15</number>
          </property>
          <property name="topMargin">
           <number>15</number>
          </property>
          <property name="rightMargin">
           <number>15</number>
          </property>
          <property name="bottomMargin">
           <number>15</number>
          </property>
          <item>
           <widget class="QGroupBox" name="motor4StatusGroup">
            <property name="title">
             <string>状态信息</string>
            </property>
            <layout class="QGridLayout" name="motor4StatusLayout">
             <property name="leftMargin">
              <number>10</number>
             </property>
             <property name="topMargin">
              <number>10</number>
             </property>
             <property name="rightMargin">
              <number>10</number>
             </property>
             <property name="bottomMargin">
              <number>10</number>
             </property>
             <property name="spacing">
              <number>8</number>
             </property>
             <item row="0" column="0">
              <widget class="QLabel" name="motor4StatusLabel_1">
               <property name="text">
                <string>状态:</string>
               </property>
              </widget>
             </item>
             <item row="0" column="1">
              <widget class="QLabel" name="motor4StatusValue">
               <property name="minimumSize">
                <size>
                 <width>80</width>
                 <height>0</height>
                </size>
               </property>
               <property name="text">
                <string>未连接</string>
               </property>
              </widget>
             </item>
             <item row="1" column="0">
              <widget class="QLabel" name="motor4SpeedLabel">
               <property name="text">
                <string>速度:</string>
               </property>
              </widget>
             </item>
             <item row="1" column="1">
              <widget class="QLabel" name="motor4SpeedValue">
               <property name="minimumSize">
                <size>
                 <width>80</width>
                 <height>0</height>
                </size>
               </property>
               <property name="text">
                <string>0 RPM</string>
               </property>
              </widget>
             </item>
             <item row="2" column="0">
              <widget class="QLabel" name="motor4PositionLabel">
               <property name="text">
                <string>位置:</string>
               </property>
              </widget>
             </item>
             <item row="2" column="1">
              <widget class="QLabel" name="motor4PositionValue">
               <property name="minimumSize">
                <size>
                 <width>80</width>
                 <height>0</height>
                </size>
               </property>
               <property name="text">
                <string>0</string>
               </property>
              </widget>
             </item>
            </layout>
           </widget>
          </item>
          <item>
           <widget class="QGroupBox" name="motor4ControlGroup">
            <property name="title">
             <string>控制参数</string>
            </property>
            <layout class="QGridLayout" name="motor4ControlLayout">
             <property name="spacing">
              <number>5</number>
             </property>
             <item row="0" column="0">
              <widget class="QLabel" name="motor4ModeLabel">
               <property name="text">
                <string>模式:</string>
               </property>
              </widget>
             </item>
             <item row="0" column="1">
              <widget class="QComboBox" name="motor4ModeCombo">
               <item>
                <property name="text">
                 <string>速度控制</string>
                </property>
               </item>
               <item>
                <property name="text">
                 <string>位置控制</string>
                </property>
               </item>
              </widget>
             </item>
             <item row="1" column="0">
              <widget class="QLabel" name="motor4TargetSpeedLabel">
               <property name="text">
                <string>目标速度:</string>
               </property>
              </widget>
             </item>
             <item row="1" column="1">
              <widget class="QSpinBox" name="motor4TargetSpeedSpin">
               <property name="suffix">
                <string> RPM</string>
               </property>
               <property name="minimum">
                <number>0</number>
               </property>
               <property name="maximum">
                <number>3000</number>
               </property>
               <property name="value">
                <number>1000</number>
               </property>
              </widget>
             </item>
            </layout>
           </widget>
          </item>
          <item>
           <widget class="QGroupBox" name="motor4ButtonGroup">
            <property name="title">
             <string>控制操作</string>
            </property>
            <layout class="QGridLayout" name="motor4ButtonLayout">
             <property name="leftMargin">
              <number>8</number>
             </property>
             <property name="topMargin">
              <number>8</number>
             </property>
             <property name="rightMargin">
              <number>8</number>
             </property>
             <property name="bottomMargin">
              <number>8</number>
             </property>
             <property name="spacing">
              <number>6</number>
             </property>
             <item row="0" column="0">
              <widget class="QPushButton" name="motor4EnableBtn">
               <property name="minimumSize">
                <size>
                 <width>75</width>
                 <height>35</height>
                </size>
               </property>
               <property name="maximumSize">
                <size>
                 <width>85</width>
                 <height>40</height>
                </size>
               </property>
               <property name="text">
                <string>使能</string>
               </property>
               <property name="checkable">
                <bool>true</bool>
               </property>
              </widget>
             </item>
             <item row="0" column="1">
              <widget class="QPushButton" name="motor4StartBtn">
               <property name="minimumSize">
                <size>
                 <width>75</width>
                 <height>35</height>
                </size>
               </property>
               <property name="maximumSize">
                <size>
                 <width>85</width>
                 <height>40</height>
                </size>
               </property>
               <property name="text">
                <string>启动</string>
               </property>
              </widget>
             </item>
             <item row="1" column="0">
              <widget class="QPushButton" name="motor4StopBtn">
               <property name="minimumSize">
                <size>
                 <width>75</width>
                 <height>35</height>
                </size>
               </property>
               <property name="maximumSize">
                <size>
                 <width>85</width>
                 <height>40</height>
                </size>
               </property>
               <property name="text">
                <string>停止</string>
               </property>
              </widget>
             </item>
             <item row="1" column="1">
              <widget class="QPushButton" name="motor4SetSpeedBtn">
               <property name="minimumSize">
                <size>
                 <width>75</width>
                 <height>35</height>
                </size>
               </property>
               <property name="maximumSize">
                <size>
                 <width>85</width>
                 <height>40</height>
                </size>
               </property>
               <property name="text">
                <string>设置速度</string>
               </property>
              </widget>
             </item>
            </layout>
           </widget>
          </item>
         </layout>
        </widget>
       </item>
       <item row="1" column="1">
        <widget class="QGroupBox" name="motor5Group">
         <property name="minimumSize">
          <size>
           <width>380</width>
           <height>380</height>
          </size>
         </property>
         <property name="maximumSize">
          <size>
           <width>450</width>
           <height>450</height>
          </size>
         </property>
         <property name="title">
          <string>电机 5</string>
         </property>
         <layout class="QVBoxLayout" name="motor5Layout">
          <property name="spacing">
           <number>12</number>
          </property>
          <property name="leftMargin">
           <number>15</number>
          </property>
          <property name="topMargin">
           <number>15</number>
          </property>
          <property name="rightMargin">
           <number>15</number>
          </property>
          <property name="bottomMargin">
           <number>15</number>
          </property>
          <item>
           <widget class="QGroupBox" name="motor5StatusGroup">
            <property name="title">
             <string>状态信息</string>
            </property>
            <layout class="QGridLayout" name="motor5StatusLayout">
             <property name="leftMargin">
              <number>10</number>
             </property>
             <property name="topMargin">
              <number>10</number>
             </property>
             <property name="rightMargin">
              <number>10</number>
             </property>
             <property name="bottomMargin">
              <number>10</number>
             </property>
             <property name="spacing">
              <number>8</number>
             </property>
             <item row="0" column="0">
              <widget class="QLabel" name="motor5StatusLabel_1">
               <property name="text">
                <string>状态:</string>
               </property>
              </widget>
             </item>
             <item row="0" column="1">
              <widget class="QLabel" name="motor5StatusValue">
               <property name="minimumSize">
                <size>
                 <width>80</width>
                 <height>0</height>
                </size>
               </property>
               <property name="text">
                <string>未连接</string>
               </property>
              </widget>
             </item>
             <item row="1" column="0">
              <widget class="QLabel" name="motor5SpeedLabel">
               <property name="text">
                <string>速度:</string>
               </property>
              </widget>
             </item>
             <item row="1" column="1">
              <widget class="QLabel" name="motor5SpeedValue">
               <property name="minimumSize">
                <size>
                 <width>80</width>
                 <height>0</height>
                </size>
               </property>
               <property name="text">
                <string>0 RPM</string>
               </property>
              </widget>
             </item>
             <item row="2" column="0">
              <widget class="QLabel" name="motor5PositionLabel">
               <property name="text">
                <string>位置:</string>
               </property>
              </widget>
             </item>
             <item row="2" column="1">
              <widget class="QLabel" name="motor5PositionValue">
               <property name="minimumSize">
                <size>
                 <width>80</width>
                 <height>0</height>
                </size>
               </property>
               <property name="text">
                <string>0</string>
               </property>
              </widget>
             </item>
            </layout>
           </widget>
          </item>
          <item>
           <widget class="QGroupBox" name="motor5ControlGroup">
            <property name="title">
             <string>控制参数</string>
            </property>
            <layout class="QGridLayout" name="motor5ControlLayout">
             <property name="spacing">
              <number>5</number>
             </property>
             <item row="0" column="0">
              <widget class="QLabel" name="motor5ModeLabel">
               <property name="text">
                <string>模式:</string>
               </property>
              </widget>
             </item>
             <item row="0" column="1">
              <widget class="QComboBox" name="motor5ModeCombo">
               <item>
                <property name="text">
                 <string>速度控制</string>
                </property>
               </item>
               <item>
                <property name="text">
                 <string>位置控制</string>
                </property>
               </item>
              </widget>
             </item>
             <item row="1" column="0">
              <widget class="QLabel" name="motor5TargetSpeedLabel">
               <property name="text">
                <string>目标速度:</string>
               </property>
              </widget>
             </item>
             <item row="1" column="1">
              <widget class="QSpinBox" name="motor5TargetSpeedSpin">
               <property name="suffix">
                <string> RPM</string>
               </property>
               <property name="minimum">
                <number>0</number>
               </property>
               <property name="maximum">
                <number>3000</number>
               </property>
               <property name="value">
                <number>1000</number>
               </property>
              </widget>
             </item>
            </layout>
           </widget>
          </item>
          <item>
           <widget class="QGroupBox" name="motor5ButtonGroup">
            <property name="title">
             <string>控制操作</string>
            </property>
            <layout class="QGridLayout" name="motor5ButtonLayout">
             <property name="leftMargin">
              <number>8</number>
             </property>
             <property name="topMargin">
              <number>8</number>
             </property>
             <property name="rightMargin">
              <number>8</number>
             </property>
             <property name="bottomMargin">
              <number>8</number>
             </property>
             <property name="spacing">
              <number>6</number>
             </property>
             <item row="0" column="0">
              <widget class="QPushButton" name="motor5EnableBtn">
               <property name="minimumSize">
                <size>
                 <width>75</width>
                 <height>35</height>
                </size>
               </property>
               <property name="maximumSize">
                <size>
                 <width>85</width>
                 <height>40</height>
                </size>
               </property>
               <property name="text">
                <string>使能</string>
               </property>
               <property name="checkable">
                <bool>true</bool>
               </property>
              </widget>
             </item>
             <item row="0" column="1">
              <widget class="QPushButton" name="motor5StartBtn">
               <property name="minimumSize">
                <size>
                 <width>75</width>
                 <height>35</height>
                </size>
               </property>
               <property name="maximumSize">
                <size>
                 <width>85</width>
                 <height>40</height>
                </size>
               </property>
               <property name="text">
                <string>启动</string>
               </property>
              </widget>
             </item>
             <item row="1" column="0">
              <widget class="QPushButton" name="motor5StopBtn">
               <property name="minimumSize">
                <size>
                 <width>75</width>
                 <height>35</height>
                </size>
               </property>
               <property name="maximumSize">
                <size>
                 <width>85</width>
                 <height>40</height>
                </size>
               </property>
               <property name="text">
                <string>停止</string>
               </property>
              </widget>
             </item>
             <item row="1" column="1">
              <widget class="QPushButton" name="motor5SetSpeedBtn">
               <property name="minimumSize">
                <size>
                 <width>75</width>
                 <height>35</height>
                </size>
               </property>
               <property name="maximumSize">
                <size>
                 <width>85</width>
                 <height>40</height>
                </size>
               </property>
               <property name="text">
                <string>设置速度</string>
               </property>
              </widget>
             </item>
            </layout>
           </widget>
          </item>
         </layout>
        </widget>
       </item>
       <item row="1" column="2">
        <widget class="QGroupBox" name="motor6Group">
         <property name="minimumSize">
          <size>
           <width>380</width>
           <height>380</height>
          </size>
         </property>
         <property name="maximumSize">
          <size>
           <width>450</width>
           <height>450</height>
          </size>
         </property>
         <property name="title">
          <string>电机 6</string>
         </property>
         <layout class="QVBoxLayout" name="motor6Layout">
          <property name="spacing">
           <number>12</number>
          </property>
          <property name="leftMargin">
           <number>15</number>
          </property>
          <property name="topMargin">
           <number>15</number>
          </property>
          <property name="rightMargin">
           <number>15</number>
          </property>
          <property name="bottomMargin">
           <number>15</number>
          </property>
          <item>
           <widget class="QGroupBox" name="motor6StatusGroup">
            <property name="title">
             <string>状态信息</string>
            </property>
            <layout class="QGridLayout" name="motor6StatusLayout">
             <property name="leftMargin">
              <number>10</number>
             </property>
             <property name="topMargin">
              <number>10</number>
             </property>
             <property name="rightMargin">
              <number>10</number>
             </property>
             <property name="bottomMargin">
              <number>10</number>
             </property>
             <property name="spacing">
              <number>8</number>
             </property>
             <item row="0" column="0">
              <widget class="QLabel" name="motor6StatusLabel_1">
               <property name="text">
                <string>状态:</string>
               </property>
              </widget>
             </item>
             <item row="0" column="1">
              <widget class="QLabel" name="motor6StatusValue">
               <property name="minimumSize">
                <size>
                 <width>80</width>
                 <height>0</height>
                </size>
               </property>
               <property name="text">
                <string>未连接</string>
               </property>
              </widget>
             </item>
             <item row="1" column="0">
              <widget class="QLabel" name="motor6SpeedLabel">
               <property name="text">
                <string>速度:</string>
               </property>
              </widget>
             </item>
             <item row="1" column="1">
              <widget class="QLabel" name="motor6SpeedValue">
               <property name="minimumSize">
                <size>
                 <width>80</width>
                 <height>0</height>
                </size>
               </property>
               <property name="text">
                <string>0 RPM</string>
               </property>
              </widget>
             </item>
             <item row="2" column="0">
              <widget class="QLabel" name="motor6PositionLabel">
               <property name="text">
                <string>位置:</string>
               </property>
              </widget>
             </item>
             <item row="2" column="1">
              <widget class="QLabel" name="motor6PositionValue">
               <property name="minimumSize">
                <size>
                 <width>80</width>
                 <height>0</height>
                </size>
               </property>
               <property name="text">
                <string>0</string>
               </property>
              </widget>
             </item>
            </layout>
           </widget>
          </item>
          <item>
           <widget class="QGroupBox" name="motor6ControlGroup">
            <property name="title">
             <string>控制参数</string>
            </property>
            <layout class="QGridLayout" name="motor6ControlLayout">
             <property name="spacing">
              <number>5</number>
             </property>
             <item row="0" column="0">
              <widget class="QLabel" name="motor6ModeLabel">
               <property name="text">
                <string>模式:</string>
               </property>
              </widget>
             </item>
             <item row="0" column="1">
              <widget class="QComboBox" name="motor6ModeCombo">
               <item>
                <property name="text">
                 <string>速度控制</string>
                </property>
               </item>
               <item>
                <property name="text">
                 <string>位置控制</string>
                </property>
               </item>
              </widget>
             </item>
             <item row="1" column="0">
              <widget class="QLabel" name="motor6TargetSpeedLabel">
               <property name="text">
                <string>目标速度:</string>
               </property>
              </widget>
             </item>
             <item row="1" column="1">
              <widget class="QSpinBox" name="motor6TargetSpeedSpin">
               <property name="suffix">
                <string> RPM</string>
               </property>
               <property name="minimum">
                <number>0</number>
               </property>
               <property name="maximum">
                <number>3000</number>
               </property>
               <property name="value">
                <number>1000</number>
               </property>
              </widget>
             </item>
            </layout>
           </widget>
          </item>
          <item>
           <widget class="QGroupBox" name="motor6ButtonGroup">
            <property name="title">
             <string>控制操作</string>
            </property>
            <layout class="QGridLayout" name="motor6ButtonLayout">
             <property name="leftMargin">
              <number>8</number>
             </property>
             <property name="topMargin">
              <number>8</number>
             </property>
             <property name="rightMargin">
              <number>8</number>
             </property>
             <property name="bottomMargin">
              <number>8</number>
             </property>
             <property name="spacing">
              <number>6</number>
             </property>
             <item row="0" column="0">
              <widget class="QPushButton" name="motor6EnableBtn">
               <property name="minimumSize">
                <size>
                 <width>75</width>
                 <height>35</height>
                </size>
               </property>
               <property name="maximumSize">
                <size>
                 <width>85</width>
                 <height>40</height>
                </size>
               </property>
               <property name="text">
                <string>使能</string>
               </property>
               <property name="checkable">
                <bool>true</bool>
               </property>
              </widget>
             </item>
             <item row="0" column="1">
              <widget class="QPushButton" name="motor6StartBtn">
               <property name="minimumSize">
                <size>
                 <width>75</width>
                 <height>35</height>
                </size>
               </property>
               <property name="maximumSize">
                <size>
                 <width>85</width>
                 <height>40</height>
                </size>
               </property>
               <property name="text">
                <string>启动</string>
               </property>
              </widget>
             </item>
             <item row="1" column="0">
              <widget class="QPushButton" name="motor6StopBtn">
               <property name="minimumSize">
                <size>
                 <width>75</width>
                 <height>35</height>
                </size>
               </property>
               <property name="maximumSize">
                <size>
                 <width>85</width>
                 <height>40</height>
                </size>
               </property>
               <property name="text">
                <string>停止</string>
               </property>
              </widget>
             </item>
             <item row="1" column="1">
              <widget class="QPushButton" name="motor6SetSpeedBtn">
               <property name="minimumSize">
                <size>
                 <width>75</width>
                 <height>35</height>
                </size>
               </property>
               <property name="maximumSize">
                <size>
                 <width>85</width>
                 <height>40</height>
                </size>
               </property>
               <property name="text">
                <string>设置速度</string>
               </property>
              </widget>
             </item>
            </layout>
           </widget>
          </item>
         </layout>
        </widget>
       </item>
      </layout>
     </widget>
    </widget>
   </item>
  </layout>
 </widget>
 <resources/>
 <connections/>
</ui>
