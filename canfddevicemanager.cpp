/**
 * @file canfddevicemanager.cpp
 * @brief CANFD设备连接管理器实现文件
 * @details 统一管理CANFD设备的连接状态检测、数据源切换等功能
 * <AUTHOR>
 * @date 2025-01-31
 * @version 1.0
 */

#include "canfddevicemanager.h"
#include <QDateTime>
#include <QApplication>
#include <QDir>
#include <QMutexLocker>

// 静态成员初始化
CANFDDeviceManager* CANFDDeviceManager::m_instance = nullptr;
QMutex CANFDDeviceManager::m_mutex;

/**
 * @brief 获取单例实例
 */
CANFDDeviceManager* CANFDDeviceManager::getInstance()
{
    if (m_instance == nullptr) {
        QMutexLocker locker(&m_mutex);
        if (m_instance == nullptr) {
            m_instance = new CANFDDeviceManager();
        }
    }
    return m_instance;
}

/**
 * @brief 私有构造函数
 */
CANFDDeviceManager::CANFDDeviceManager(QObject *parent)
    : QObject(parent)
    , m_currentStatus(CANFDConnectionStatus::DISCONNECTED)
    , m_currentDataSource(DataSourceType::SIMULATION)
    , m_statusMonitorTimer(nullptr)
    , m_dataCheckTimer(nullptr)
    , m_zlgcanLib(nullptr)
{
    // 初始化函数指针
    ZCAN_OpenDevice = nullptr;
    ZCAN_CloseDevice = nullptr;
    ZCAN_InitCANFD = nullptr;
    ZCAN_StartCANFD = nullptr;
    ZCAN_ResetCANFD = nullptr;
    ZCAN_TransmitFD = nullptr;
    ZCAN_ReceiveFD = nullptr;
    
    // 创建定时器
    m_statusMonitorTimer = new QTimer(this);
    m_statusMonitorTimer->setInterval(5000); // 5秒检查一次状态
    connect(m_statusMonitorTimer, &QTimer::timeout, this, &CANFDDeviceManager::onStatusMonitorTimeout);
    
    m_dataCheckTimer = new QTimer(this);
    m_dataCheckTimer->setInterval(1000); // 1秒检查一次数据
    connect(m_dataCheckTimer, &QTimer::timeout, this, &CANFDDeviceManager::checkDataReception);
    
    // 初始化ZLGCAN库
    initializeZLGCAN();
    
    qDebug() << "CANFDDeviceManager 初始化完成";
}

/**
 * @brief 析构函数
 */
CANFDDeviceManager::~CANFDDeviceManager()
{
    stopStatusMonitoring();
    disconnectDevice();
    
    if (m_zlgcanLib) {
        m_zlgcanLib->unload();
        delete m_zlgcanLib;
    }
}

/**
 * @brief 初始化ZLGCAN库
 */
bool CANFDDeviceManager::initializeZLGCAN()
{
    // 加载ZLGCAN动态库
    m_zlgcanLib = new QLibrary("zlgcan");
    if (!m_zlgcanLib->load()) {
        // 尝试从zlgcan_x64目录加载
        QString libPath = QApplication::applicationDirPath() + "/zlgcan_x64/zlgcan.dll";
        m_zlgcanLib->setFileName(libPath);
        if (!m_zlgcanLib->load()) {
            qDebug() << "ZLGCAN库加载失败:" << m_zlgcanLib->errorString();
            return false;
        }
    }
    
    // 获取函数指针
    ZCAN_OpenDevice = (ZCAN_OpenDevice_Func)m_zlgcanLib->resolve("ZCAN_OpenDevice");
    ZCAN_CloseDevice = (ZCAN_CloseDevice_Func)m_zlgcanLib->resolve("ZCAN_CloseDevice");
    ZCAN_InitCANFD = (ZCAN_InitCANFD_Func)m_zlgcanLib->resolve("ZCAN_InitCAN");
    ZCAN_StartCANFD = (ZCAN_StartCANFD_Func)m_zlgcanLib->resolve("ZCAN_StartCAN");
    ZCAN_ResetCANFD = (ZCAN_ResetCANFD_Func)m_zlgcanLib->resolve("ZCAN_ResetCAN");
    ZCAN_TransmitFD = (ZCAN_TransmitFD_Func)m_zlgcanLib->resolve("ZCAN_TransmitFD");
    ZCAN_ReceiveFD = (ZCAN_ReceiveFD_Func)m_zlgcanLib->resolve("ZCAN_ReceiveFD");
    
    // 检查关键函数是否加载成功
    if (!ZCAN_OpenDevice || !ZCAN_CloseDevice || !ZCAN_TransmitFD || !ZCAN_ReceiveFD) {
        qDebug() << "ZLGCAN关键函数加载失败";
        return false;
    }
    
    qDebug() << "ZLGCAN库初始化成功";
    return true;
}

/**
 * @brief 检测设备连接状态（按需检测）
 */
CANFDConnectionStatus CANFDDeviceManager::detectDeviceStatus()
{
    qDebug() << "开始检测CANFD设备状态...";
    
    // 如果库未初始化，返回未连接
    if (!ZCAN_OpenDevice) {
        updateConnectionStatus(CANFDConnectionStatus::DISCONNECTED);
        return m_currentStatus;
    }
    
    // 如果设备已经打开，检查是否仍然有效
    if (m_deviceInfo.isOpened) {
        // 检查是否有数据接收
        if (hasRealData()) {
            updateConnectionStatus(CANFDConnectionStatus::ACTIVE);
        } else {
            updateConnectionStatus(CANFDConnectionStatus::CONNECTED);
        }
        return m_currentStatus;
    }
    
    // 尝试连接设备
    updateConnectionStatus(CANFDConnectionStatus::CONNECTING);
    
    if (connectDevice()) {
        if (hasRealData()) {
            updateConnectionStatus(CANFDConnectionStatus::ACTIVE);
        } else {
            updateConnectionStatus(CANFDConnectionStatus::CONNECTED);
        }
    } else {
        updateConnectionStatus(CANFDConnectionStatus::DISCONNECTED);
    }
    
    return m_currentStatus;
}

/**
 * @brief 尝试连接CANFD设备
 */
bool CANFDDeviceManager::connectDevice()
{
    if (!ZCAN_OpenDevice) {
        emit deviceError("ZLGCAN库未初始化");
        return false;
    }
    
    // 如果已经连接，先断开
    if (m_deviceInfo.isOpened) {
        disconnectDevice();
    }
    
    // 打开设备
    m_deviceInfo.deviceHandle = ZCAN_OpenDevice(ZCAN_USBCANFD_100U, 0, 0);
    if (m_deviceInfo.deviceHandle == INVALID_DEVICE_HANDLE) {
        emit deviceError("无法打开CANFD设备，请检查设备连接");
        return false;
    }
    
    // 配置CANFD通道
    if (!configureCANFDChannel()) {
        ZCAN_CloseDevice(m_deviceInfo.deviceHandle);
        m_deviceInfo.deviceHandle = INVALID_DEVICE_HANDLE;
        return false;
    }
    
    m_deviceInfo.isOpened = true;
    m_deviceInfo.lastDataTime = QDateTime::currentMSecsSinceEpoch();
    m_deviceInfo.dataCount = 0;
    
    qDebug() << "CANFD设备连接成功";
    return true;
}

/**
 * @brief 配置CANFD通道
 */
bool CANFDDeviceManager::configureCANFDChannel()
{
    if (!ZCAN_InitCANFD || !ZCAN_StartCANFD) {
        return false;
    }
    
    // 配置CANFD参数
    ZCAN_CHANNEL_INIT_CONFIG canfdConfig;
    memset(&canfdConfig, 0, sizeof(canfdConfig));
    
    canfdConfig.can_type = TYPE_CANFD;
    canfdConfig.canfd.mode = 0; // 正常模式
    canfdConfig.canfd.abit_timing = 0x00001C08; // 仲裁段波特率 500kbps
    canfdConfig.canfd.dbit_timing = 0x00000302; // 数据段波特率 2Mbps
    
    // 初始化通道
    m_deviceInfo.channelHandle = ZCAN_InitCANFD(m_deviceInfo.deviceHandle, 0, &canfdConfig);
    if (m_deviceInfo.channelHandle == INVALID_CHANNEL_HANDLE) {
        emit deviceError("CANFD通道初始化失败");
        return false;
    }
    
    // 启动通道
    if (ZCAN_StartCANFD(m_deviceInfo.channelHandle) != STATUS_OK) {
        emit deviceError("CANFD通道启动失败");
        return false;
    }
    
    return true;
}

/**
 * @brief 断开CANFD设备
 */
void CANFDDeviceManager::disconnectDevice()
{
    if (m_deviceInfo.isOpened && ZCAN_CloseDevice) {
        ZCAN_CloseDevice(m_deviceInfo.deviceHandle);
        m_deviceInfo.deviceHandle = INVALID_DEVICE_HANDLE;
        m_deviceInfo.channelHandle = INVALID_CHANNEL_HANDLE;
        m_deviceInfo.isOpened = false;
        qDebug() << "CANFD设备已断开";
    }
    
    updateConnectionStatus(CANFDConnectionStatus::DISCONNECTED);
}

/**
 * @brief 检查是否有真实数据
 */
bool CANFDDeviceManager::hasRealData() const
{
    qint64 currentTime = QDateTime::currentMSecsSinceEpoch();
    // 如果最近10秒内有数据接收，认为有真实数据
    return (currentTime - m_deviceInfo.lastDataTime) < 10000 && m_deviceInfo.dataCount > 0;
}

/**
 * @brief 更新连接状态
 */
void CANFDDeviceManager::updateConnectionStatus(CANFDConnectionStatus newStatus)
{
    if (m_currentStatus != newStatus) {
        m_currentStatus = newStatus;
        
        // 根据连接状态更新数据源类型
        if (newStatus == CANFDConnectionStatus::ACTIVE) {
            m_currentDataSource = DataSourceType::REAL_DATA;
        } else {
            m_currentDataSource = DataSourceType::SIMULATION;
        }
        
        emit connectionStatusChanged(newStatus);
        emit dataSourceChanged(m_currentDataSource);
        
        qDebug() << "连接状态更新:" << static_cast<int>(newStatus) 
                 << "数据源:" << static_cast<int>(m_currentDataSource);
    }
}

/**
 * @brief 启动轻量级状态监控
 */
void CANFDDeviceManager::startStatusMonitoring()
{
    if (m_statusMonitorTimer && !m_statusMonitorTimer->isActive()) {
        m_statusMonitorTimer->start();
        qDebug() << "状态监控已启动";
    }
    
    if (m_dataCheckTimer && !m_dataCheckTimer->isActive()) {
        m_dataCheckTimer->start();
    }
}

/**
 * @brief 停止状态监控
 */
void CANFDDeviceManager::stopStatusMonitoring()
{
    if (m_statusMonitorTimer) {
        m_statusMonitorTimer->stop();
    }
    
    if (m_dataCheckTimer) {
        m_dataCheckTimer->stop();
    }
    
    qDebug() << "状态监控已停止";
}

/**
 * @brief 轻量级状态监控槽函数
 */
void CANFDDeviceManager::onStatusMonitorTimeout()
{
    // 轻量级检测：只检查设备句柄是否有效
    if (m_deviceInfo.isOpened) {
        // 简单检测设备是否仍然连接
        if (m_deviceInfo.deviceHandle == INVALID_DEVICE_HANDLE) {
            updateConnectionStatus(CANFDConnectionStatus::DISCONNECTED);
        }
    }
}

/**
 * @brief 数据接收检查槽函数
 */
void CANFDDeviceManager::checkDataReception()
{
    if (m_deviceInfo.isOpened && ZCAN_ReceiveFD) {
        receiveCANFDData();
    }
}

/**
 * @brief 发送电机控制命令
 */
bool CANFDDeviceManager::sendMotorCommand(int motorId, quint8 command, quint16 targetSpeed)
{
    if (!m_deviceInfo.isOpened || !ZCAN_TransmitFD) {
        qDebug() << "设备未连接，无法发送命令";
        return false;
    }

    // 构造电机控制帧
    MotorControlFrame controlFrame;
    controlFrame.motor_id = static_cast<quint8>(motorId);
    controlFrame.command = command;
    controlFrame.target_speed = targetSpeed;
    controlFrame.checksum = 0; // 简化处理，暂不计算校验和

    // 构造CANFD发送帧
    ZCAN_TransmitFD_Data transmitData;
    memset(&transmitData, 0, sizeof(transmitData));

    transmitData.frame.can_id = CAN_ID_MOTOR_CMD_BASE + motorId;
    transmitData.frame.len = sizeof(MotorControlFrame);
    transmitData.frame.flags = 0; // 标准数据帧

    // 复制数据
    memcpy(transmitData.frame.data, &controlFrame, sizeof(MotorControlFrame));

    // 发送数据
    UINT result = ZCAN_TransmitFD(m_deviceInfo.channelHandle, &transmitData, 1);
    if (result != 1) {
        qDebug() << QString("电机%1命令发送失败，错误码: %2").arg(motorId).arg(result);
        return false;
    }

    qDebug() << QString("电机%1命令发送成功: 命令=0x%2, 速度=%3")
                .arg(motorId)
                .arg(command, 2, 16, QChar('0'))
                .arg(targetSpeed);
    return true;
}

/**
 * @brief 接收CANFD数据
 */
void CANFDDeviceManager::receiveCANFDData()
{
    const int MAX_RECEIVE_COUNT = 50; // 每次最多接收50帧
    ZCAN_ReceiveFD_Data receiveData[MAX_RECEIVE_COUNT];

    // 接收数据（非阻塞）
    UINT receivedCount = ZCAN_ReceiveFD(m_deviceInfo.channelHandle, receiveData, MAX_RECEIVE_COUNT, 0);

    if (receivedCount > 0) {
        // 更新数据接收时间和计数
        m_deviceInfo.lastDataTime = QDateTime::currentMSecsSinceEpoch();
        m_deviceInfo.dataCount += receivedCount;

        // 解析接收到的数据
        for (UINT i = 0; i < receivedCount; i++) {
            parseMotorStatusFrame(receiveData[i]);
        }

        // 如果当前状态不是ACTIVE，更新状态
        if (m_currentStatus != CANFDConnectionStatus::ACTIVE) {
            updateConnectionStatus(CANFDConnectionStatus::ACTIVE);
        }
    }
}

/**
 * @brief 解析电机状态帧
 */
bool CANFDDeviceManager::parseMotorStatusFrame(const ZCAN_ReceiveFD_Data& frame)
{
    // 检查CAN ID是否为电机状态帧
    if (frame.frame.can_id < CAN_ID_MOTOR_STATUS_BASE ||
        frame.frame.can_id > CAN_ID_MOTOR_STATUS_BASE + 6) {
        return false; // 不是电机状态帧
    }

    // 检查数据长度
    if (frame.frame.len < sizeof(MotorStatusFrame)) {
        qDebug() << "电机状态帧数据长度不足";
        return false;
    }

    // 解析电机ID
    int motorId = frame.frame.can_id - CAN_ID_MOTOR_STATUS_BASE;
    if (motorId < 1 || motorId > 6) {
        return false;
    }

    // 解析状态数据
    MotorStatusFrame statusFrame;
    memcpy(&statusFrame, frame.frame.data, sizeof(MotorStatusFrame));

    // 简化处理，暂不验证校验和
    // TODO: 添加校验和验证逻辑

    // 发送状态数据信号
    emit motorStatusReceived(motorId, statusFrame);

    qDebug() << QString("接收到电机%1状态: 速度=%2, 位置=%3, 状态=0x%4")
                .arg(motorId)
                .arg(statusFrame.current_speed)
                .arg(statusFrame.current_position)
                .arg(statusFrame.status_flags, 2, 16, QChar('0'));

    return true;
}
