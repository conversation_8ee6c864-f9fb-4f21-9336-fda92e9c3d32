/**
 * @file canfddevicemanager.h
 * @brief CANFD设备连接管理器头文件
 * @details 统一管理CANFD设备的连接状态检测、数据源切换等功能
 * <AUTHOR>
 * @date 2025-01-31
 * @version 1.0
 */

#ifndef CANFDDEVICEMANAGER_H
#define CANFDDEVICEMANAGER_H

#include <QObject>
#include <QTimer>
#include <QMutex>
#include <QDebug>
#include <QLibrary>
#include "include/zlgcan.h"
#include "motorprotocol.h"

/**
 * @brief CANFD连接状态枚举
 */
enum class CANFDConnectionStatus {
    DISCONNECTED = 0,    ///< 未连接
    CONNECTING = 1,      ///< 连接中  
    CONNECTED = 2,       ///< 已连接但无数据
    ACTIVE = 3          ///< 已连接且有数据
};

/**
 * @brief 数据源类型枚举
 */
enum class DataSourceType {
    SIMULATION = 0,     ///< 模拟数据
    REAL_DATA = 1,      ///< 真实CANFD数据
    MIXED = 2          ///< 混合模式（部分真实+部分模拟）
};

/**
 * @brief CANFD设备信息结构
 */
struct CANFDDeviceInfo {
    DEVICE_HANDLE deviceHandle;     ///< 设备句柄
    CHANNEL_HANDLE channelHandle;   ///< 通道句柄
    QString deviceName;             ///< 设备名称
    bool isOpened;                  ///< 是否已打开
    qint64 lastDataTime;           ///< 最后数据接收时间
    int dataCount;                  ///< 数据接收计数
    
    CANFDDeviceInfo() : deviceHandle(INVALID_DEVICE_HANDLE), 
                       channelHandle(INVALID_CHANNEL_HANDLE),
                       deviceName("ZLGCANFD100U"),
                       isOpened(false),
                       lastDataTime(0),
                       dataCount(0) {}
};

/**
 * @class CANFDDeviceManager
 * @brief CANFD设备连接管理器
 * @details 单例模式，统一管理CANFD设备的连接状态、数据接收等
 */
class CANFDDeviceManager : public QObject
{
    Q_OBJECT

public:
    /**
     * @brief 获取单例实例
     */
    static CANFDDeviceManager* getInstance();
    
    /**
     * @brief 析构函数
     */
    ~CANFDDeviceManager();

    /**
     * @brief 检测设备连接状态（按需检测）
     * @return 当前连接状态
     */
    CANFDConnectionStatus detectDeviceStatus();
    
    /**
     * @brief 获取当前连接状态
     * @return 当前连接状态
     */
    CANFDConnectionStatus getCurrentStatus() const { return m_currentStatus; }
    
    /**
     * @brief 获取当前数据源类型
     * @return 当前数据源类型
     */
    DataSourceType getCurrentDataSource() const { return m_currentDataSource; }
    
    /**
     * @brief 尝试连接CANFD设备
     * @return 连接是否成功
     */
    bool connectDevice();
    
    /**
     * @brief 断开CANFD设备
     */
    void disconnectDevice();
    
    /**
     * @brief 检查是否有真实数据
     * @return 是否有真实数据
     */
    bool hasRealData() const;
    
    /**
     * @brief 获取设备信息
     * @return 设备信息结构
     */
    const CANFDDeviceInfo& getDeviceInfo() const { return m_deviceInfo; }
    
    /**
     * @brief 发送电机控制命令
     * @param motorId 电机ID (1-6)
     * @param command 控制命令
     * @param targetSpeed 目标速度
     * @return 发送是否成功
     */
    bool sendMotorCommand(int motorId, quint8 command, quint16 targetSpeed = 0);
    
    /**
     * @brief 启动轻量级状态监控
     */
    void startStatusMonitoring();
    
    /**
     * @brief 停止状态监控
     */
    void stopStatusMonitoring();

signals:
    /**
     * @brief 连接状态改变信号
     * @param status 新的连接状态
     */
    void connectionStatusChanged(CANFDConnectionStatus status);
    
    /**
     * @brief 数据源改变信号
     * @param dataSource 新的数据源类型
     */
    void dataSourceChanged(DataSourceType dataSource);
    
    /**
     * @brief 接收到电机状态数据信号
     * @param motorId 电机ID
     * @param statusFrame 状态数据帧
     */
    void motorStatusReceived(int motorId, const MotorStatusFrame& statusFrame);
    
    /**
     * @brief 设备错误信号
     * @param errorMessage 错误信息
     */
    void deviceError(const QString& errorMessage);

private slots:
    /**
     * @brief 轻量级状态监控槽函数
     */
    void onStatusMonitorTimeout();
    
    /**
     * @brief 数据接收检查槽函数
     */
    void checkDataReception();

private:
    /**
     * @brief 私有构造函数（单例模式）
     */
    explicit CANFDDeviceManager(QObject *parent = nullptr);
    
    /**
     * @brief 初始化ZLGCAN库
     * @return 初始化是否成功
     */
    bool initializeZLGCAN();
    
    /**
     * @brief 配置CANFD通道
     * @return 配置是否成功
     */
    bool configureCANFDChannel();
    
    /**
     * @brief 接收CANFD数据
     */
    void receiveCANFDData();
    
    /**
     * @brief 解析电机状态帧
     * @param frame CANFD数据帧
     * @return 解析是否成功
     */
    bool parseMotorStatusFrame(const ZCAN_ReceiveFD_Data& frame);
    
    /**
     * @brief 更新连接状态
     * @param newStatus 新状态
     */
    void updateConnectionStatus(CANFDConnectionStatus newStatus);

private:
    static CANFDDeviceManager* m_instance;  ///< 单例实例
    static QMutex m_mutex;                  ///< 线程安全互斥锁
    
    CANFDConnectionStatus m_currentStatus;  ///< 当前连接状态
    DataSourceType m_currentDataSource;     ///< 当前数据源类型
    CANFDDeviceInfo m_deviceInfo;          ///< 设备信息
    
    QTimer* m_statusMonitorTimer;          ///< 状态监控定时器
    QTimer* m_dataCheckTimer;              ///< 数据检查定时器
    
    // ZLGCAN相关函数指针
    typedef DEVICE_HANDLE (*ZCAN_OpenDevice_Func)(UINT device_type, UINT device_index, UINT reserved);
    typedef UINT (*ZCAN_CloseDevice_Func)(DEVICE_HANDLE device_handle);
    typedef CHANNEL_HANDLE (*ZCAN_InitCANFD_Func)(DEVICE_HANDLE device_handle, UINT can_index, ZCAN_CHANNEL_INIT_CONFIG* pInitConfig);
    typedef UINT (*ZCAN_StartCANFD_Func)(CHANNEL_HANDLE channel_handle);
    typedef UINT (*ZCAN_ResetCANFD_Func)(CHANNEL_HANDLE channel_handle);
    typedef UINT (*ZCAN_TransmitFD_Func)(CHANNEL_HANDLE channel_handle, ZCAN_TransmitFD_Data* pTransmit, UINT len);
    typedef UINT (*ZCAN_ReceiveFD_Func)(CHANNEL_HANDLE channel_handle, ZCAN_ReceiveFD_Data* pReceive, UINT len, int wait_time);
    
    ZCAN_OpenDevice_Func ZCAN_OpenDevice;
    ZCAN_CloseDevice_Func ZCAN_CloseDevice;
    ZCAN_InitCANFD_Func ZCAN_InitCANFD;
    ZCAN_StartCANFD_Func ZCAN_StartCANFD;
    ZCAN_ResetCANFD_Func ZCAN_ResetCANFD;
    ZCAN_TransmitFD_Func ZCAN_TransmitFD;
    ZCAN_ReceiveFD_Func ZCAN_ReceiveFD;
    
    QLibrary* m_zlgcanLib;                 ///< ZLGCAN动态库
};

#endif // CANFDDEVICEMANAGER_H
