<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>CANForm</class>
 <widget class="QWidget" name="CANForm">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>800</width>
    <height>600</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>CAN通信</string>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout">
   <item>
    <layout class="QHBoxLayout" name="horizontalLayout">
     <item>
      <widget class="QPushButton" name="backButton">
       <property name="text">
        <string>返回</string>
       </property>
      </widget>
     </item>
     <item>
      <spacer name="horizontalSpacer">
       <property name="orientation">
        <enum>Qt::Horizontal</enum>
       </property>
       <property name="sizeHint" stdset="0">
        <size>
         <width>40</width>
         <height>20</height>
        </size>
       </property>
      </spacer>
     </item>
    </layout>
   </item>
   <item>
    <widget class="QGroupBox" name="deviceGroup">
     <property name="title">
      <string>设备控制</string>
     </property>
     <layout class="QHBoxLayout" name="horizontalLayout_2">
      <item>
       <widget class="QPushButton" name="openButton">
        <property name="text">
         <string>打开设备</string>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QPushButton" name="closeButton">
        <property name="text">
         <string>关闭设备</string>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QPushButton" name="refreshButton">
        <property name="text">
         <string>刷新设备</string>
        </property>
       </widget>
      </item>
     </layout>
    </widget>
   </item>
   <item>
    <widget class="QGroupBox" name="configGroup">
     <property name="title">
      <string>配置</string>
     </property>
     <layout class="QGridLayout" name="gridLayout">
      <item row="0" column="0">
       <widget class="QLabel" name="label">
        <property name="text">
         <string>协议：</string>
        </property>
       </widget>
      </item>
      <item row="0" column="1">
       <widget class="QComboBox" name="protocolComboBox"/>
      </item>
      <item row="0" column="2">
       <widget class="QLabel" name="label_2">
        <property name="text">
         <string>波特率：</string>
        </property>
       </widget>
      </item>
      <item row="0" column="3">
       <widget class="QComboBox" name="baudrateComboBox"/>
      </item>
      <item row="1" column="0" colspan="2">
       <widget class="QCheckBox" name="terminalResistanceCheckBox">
        <property name="text">
         <string>终端电阻</string>
        </property>
       </widget>
      </item>
     </layout>
    </widget>
   </item>
   <item>
    <widget class="QGroupBox" name="channelGroup">
     <property name="title">
      <string>通道控制</string>
     </property>
     <layout class="QHBoxLayout" name="horizontalLayout_3">
      <item>
       <widget class="QPushButton" name="startButton">
        <property name="text">
         <string>启动通道</string>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QPushButton" name="stopButton">
        <property name="text">
         <string>停止通道</string>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QPushButton" name="resetButton">
        <property name="text">
         <string>复位通道</string>
        </property>
       </widget>
      </item>
     </layout>
    </widget>
   </item>
   <item>
    <widget class="QGroupBox" name="heartbeatGroup">
     <property name="title">
      <string>心跳包控制</string>
     </property>
     <layout class="QHBoxLayout" name="horizontalLayout_4">
      <item>
       <widget class="QPushButton" name="startHeartbeatButton">
        <property name="text">
         <string>开始发送</string>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QPushButton" name="stopHeartbeatButton">
        <property name="text">
         <string>停止发送</string>
        </property>
       </widget>
      </item>
     </layout>
    </widget>
   </item>
   <item>
    <widget class="QSplitter" name="splitter">
     <property name="orientation">
      <enum>Qt::Vertical</enum>
     </property>
     <widget class="QGroupBox" name="sendGroup">
      <property name="title">
       <string>发送区</string>
      </property>
      <layout class="QVBoxLayout" name="verticalLayout_2">
       <item>
        <widget class="QTextEdit" name="sendTextEdit"/>
       </item>
       <item>
        <layout class="QHBoxLayout" name="horizontalLayout_5">
         <item>
          <widget class="QPushButton" name="sendButton">
           <property name="text">
            <string>发送</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QPushButton" name="clearSendButton">
           <property name="text">
            <string>清空</string>
           </property>
          </widget>
         </item>
        </layout>
       </item>
      </layout>
     </widget>
     <widget class="QGroupBox" name="receiveGroup">
      <property name="title">
       <string>接收区</string>
      </property>
      <layout class="QVBoxLayout" name="verticalLayout_3">
       <item>
        <widget class="QTextEdit" name="receiveTextEdit">
         <property name="readOnly">
          <bool>true</bool>
         </property>
        </widget>
       </item>
       <item>
        <layout class="QHBoxLayout" name="horizontalLayout_6">
         <item>
          <widget class="QPushButton" name="clearReceiveButton">
           <property name="text">
            <string>清空</string>
           </property>
          </widget>
         </item>
        </layout>
       </item>
      </layout>
     </widget>
    </widget>
   </item>
  </layout>
 </widget>
 <resources/>
 <connections/>
</ui> 