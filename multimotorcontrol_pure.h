/**
 * @file multimotorcontrol_pure.h
 * @brief 纯UI设计的多电机控制界面类头文件
 * @details 使用Qt Designer设计的纯UI界面，所有控件都在UI文件中定义，
 *          避免代码动态生成控件可能导致的崩溃问题
 * <AUTHOR>
 * @date 2025-01-28
 * @version 1.0
 */

#ifndef MULTIMOTORCONTROL_PURE_H
#define MULTIMOTORCONTROL_PURE_H

#include <QWidget>
#include <QDebug>
#include <QTimer>
#include "canfddevicemanager.h"
#include "deviceconnectiondialog.h"

QT_BEGIN_NAMESPACE
class QLabel;
class QPushButton;
class QComboBox;
class QSpinBox;
class QGroupBox;
QT_END_NAMESPACE

namespace Ui {
class MultiMotorControlPure;
}

/**
 * @brief 纯UI设计的多电机控制界面类
 * @details 这是一个完全基于UI文件设计的多电机控制界面，
 *          所有控件都在UI文件中可视化定义，避免代码动态生成
 */
class MultiMotorControlPure : public QWidget
{
    Q_OBJECT

public:
    /**
     * @brief 构造函数
     * @param parent 父窗口指针
     */
    explicit MultiMotorControlPure(QWidget *parent = nullptr);
    
    /**
     * @brief 析构函数
     */
    ~MultiMotorControlPure();

signals:
    /**
     * @brief 返回主界面信号
     */
    void backToMainInterface();
    
    /**
     * @brief 打开示波器信号
     */
    void openOscilloscope();

private slots:
    /**
     * @brief 返回按钮点击槽函数（Qt自动连接）
     * @details 响应用户点击返回按钮，发送返回主界面信号
     */
    void on_backBtn_clicked();

    // 电机1控制按钮槽函数（Qt自动连接）
    /**
     * @brief 电机1使能按钮点击槽函数
     */
    void on_motor1EnableBtn_clicked(bool checked);

    /**
     * @brief 电机1启动按钮点击槽函数
     */
    void on_motor1StartBtn_clicked();

    /**
     * @brief 电机1停止按钮点击槽函数
     */
    void on_motor1StopBtn_clicked();

    /**
     * @brief 电机1设置速度按钮点击槽函数
     */
    void on_motor1SetSpeedBtn_clicked();

    /**
     * @brief 状态更新定时器槽函数
     * @details 定期更新界面显示的状态信息
     */
    void updateStatus();

    // 批量控制按钮槽函数（Qt自动连接）
    /**
     * @brief 全部启动按钮点击槽函数
     */
    void on_startAllBtn_clicked();

    /**
     * @brief 全部停止按钮点击槽函数
     */
    void on_stopAllBtn_clicked();

    /**
     * @brief 全部使能按钮点击槽函数
     */
    void on_enableAllBtn_clicked();

    /**
     * @brief 全部失能按钮点击槽函数
     */
    void on_disableAllBtn_clicked();

    /**
     * @brief 紧急停止按钮点击槽函数
     */
    void on_emergencyStopBtn_clicked();

    /**
     * @brief 清除所有错误按钮点击槽函数
     */
    void on_clearAllErrorsBtn_clicked();

    /**
     * @brief 示波器按钮点击槽函数
     */
    void on_oscilloscopeBtn_clicked();

    // 电机2-6控制按钮槽函数（Qt自动连接）
    void on_motor2EnableBtn_clicked(bool checked);
    void on_motor2StartBtn_clicked();
    void on_motor2StopBtn_clicked();
    void on_motor2SetSpeedBtn_clicked();

    void on_motor3EnableBtn_clicked(bool checked);
    void on_motor3StartBtn_clicked();
    void on_motor3StopBtn_clicked();
    void on_motor3SetSpeedBtn_clicked();

    void on_motor4EnableBtn_clicked(bool checked);
    void on_motor4StartBtn_clicked();
    void on_motor4StopBtn_clicked();
    void on_motor4SetSpeedBtn_clicked();

    void on_motor5EnableBtn_clicked(bool checked);
    void on_motor5StartBtn_clicked();
    void on_motor5StopBtn_clicked();
    void on_motor5SetSpeedBtn_clicked();

    void on_motor6EnableBtn_clicked(bool checked);
    void on_motor6StartBtn_clicked();
    void on_motor6StopBtn_clicked();
    void on_motor6SetSpeedBtn_clicked();

private:
    /**
     * @brief 初始化界面
     * @details 设置初始状态，但不连接信号槽
     */
    void initializeUI();
    
    /**
     * @brief 设置样式
     * @details 应用额外的样式设置
     */
    void setupStyles();

    /**
     * @brief 发送电机控制命令
     * @param motorId 电机ID (1-6)
     * @param command 控制命令
     * @param targetSpeed 目标速度 (可选)
     */
    void sendMotorCommand(int motorId, quint8 command, quint16 targetSpeed = 0);

    /**
     * @brief 更新指定电机的状态显示
     * @param motorId 电机ID (1-6)
     */
    void updateMotorStatus(int motorId);

    /**
     * @brief 设置电机运行状态
     * @param motorId 电机ID (1-6)
     * @param running 运行状态
     */
    void setMotorRunning(int motorId, bool running);

    /**
     * @brief 检查电机是否已使能
     * @param motorId 电机ID (1-6)
     * @return 是否已使能
     */
    bool isMotorEnabled(int motorId);

    /**
     * @brief 执行所有电机的批量命令
     * @param command 控制命令
     */
    void executeAllMotorsCommand(quint8 command);

    /**
     * @brief 更新所有电机的状态显示
     */
    void updateAllMotorsStatus();

private:
    Ui::MultiMotorControlPure *ui;  ///< UI对象指针
    QTimer* m_statusTimer;          ///< 状态更新定时器

    // 电机状态数组 (索引0-5对应电机1-6)
    bool m_motorRunning[6];         ///< 6个电机的运行状态
    bool m_motorEnabled[6];         ///< 6个电机的使能状态
    int m_motorSpeed[6];            ///< 6个电机的当前速度 (RPM)
    int m_motorPosition[6];         ///< 6个电机的当前位置 (脉冲)
    int m_motorTargetSpeed[6];      ///< 6个电机的目标速度 (RPM)

    // 设备管理相关
    CANFDDeviceManager* m_deviceManager;    ///< CANFD设备管理器
    DataSourceType m_currentDataSource;     ///< 当前数据源类型
    bool m_useRealData;                     ///< 是否使用真实数据

public:
    /**
     * @brief 检测设备连接并显示选择对话框
     * @return 用户是否选择继续
     */
    bool checkDeviceConnectionAndPrompt();

private slots:

    /**
     * @brief 接收到电机状态数据槽函数
     * @param motorId 电机ID
     * @param statusFrame 状态数据帧
     */
    void onMotorStatusReceived(int motorId, const MotorStatusFrame& statusFrame);

    /**
     * @brief 设备连接状态改变槽函数
     * @param status 新的连接状态
     */
    void onConnectionStatusChanged(CANFDConnectionStatus status);

    /**
     * @brief 切换数据源
     * @param dataSource 新的数据源类型
     */
    void switchDataSource(DataSourceType dataSource);
};

#endif // MULTIMOTORCONTROL_PURE_H
