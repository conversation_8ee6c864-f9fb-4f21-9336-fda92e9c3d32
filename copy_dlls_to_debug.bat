@echo off
echo ========================================
echo    复制ZLGCAN库到可执行程序目录
echo ========================================
echo.

set SOURCE_DIR=zlgcan_x64
set TARGET_DIR=build\Desktop_Qt_6_6_3_MinGW_64_bit-Debug\debug

echo 源目录: %SOURCE_DIR%
echo 目标目录: %TARGET_DIR%
echo.

if not exist "%SOURCE_DIR%" (
    echo 错误: 源目录 %SOURCE_DIR% 不存在
    pause
    exit /b 1
)

if not exist "%TARGET_DIR%" (
    echo 错误: 目标目录 %TARGET_DIR% 不存在，请先编译程序
    pause
    exit /b 1
)

echo 复制主DLL文件...
copy "%SOURCE_DIR%\zlgcan.dll" "%TARGET_DIR%\" >nul
if errorlevel 1 (
    echo 错误: 无法复制 zlgcan.dll
    pause
    exit /b 1
)
echo ✓ 复制 zlgcan.dll 到程序目录

echo 复制kerneldlls目录...
if exist "%TARGET_DIR%\kerneldlls" (
    rmdir /s /q "%TARGET_DIR%\kerneldlls"
)
xcopy "%SOURCE_DIR%\kerneldlls" "%TARGET_DIR%\kerneldlls\" /E /I /Y >nul
if errorlevel 1 (
    echo 错误: 无法复制 kerneldlls 目录
    pause
    exit /b 1
)
echo ✓ 复制 kerneldlls 目录到程序目录

echo.
echo ========================================
echo           复制完成！
echo ========================================
echo.
echo 文件已复制到: %TARGET_DIR%
echo - zlgcan.dll (主库文件)
echo - kerneldlls\ (依赖库目录，包含所有必要的DLL)
echo.
echo 现在可以运行程序测试CANFD连接了！
echo.

:: 显示目标目录内容
echo 目标目录内容:
dir "%TARGET_DIR%" /B
echo.
pause
