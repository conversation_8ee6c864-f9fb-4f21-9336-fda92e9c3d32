# 六电机控制系统 CANFD 协议规范文档

## 📋 文档信息
- **版本**: V1.0
- **日期**: 2025-01-28
- **协议类型**: CANFD (CAN with Flexible Data-Rate)
- **适用范围**: 上位机与下位机通信
- **控制对象**: 6个电机的独立控制和监控

---

## 🎯 1. 协议概述

### 1.1 系统架构
```
上位机 ←→ CANFD总线 ←→ 下位机控制器 ←→ 6个电机驱动器
   ↓                      ↓              ↓
Qt应用程序              嵌入式控制器        伺服电机
(数据监控)             (实时控制)        (执行机构)
```

### 1.2 技术规格
- **总线类型**: CANFD (支持最大64字节数据帧)
- **波特率配置**: 
  - 仲裁段: 500Kbps (标准)
  - 数据段: 2Mbps (高速)
- **电机数量**: 6个独立控制
- **实时性要求**: 
  - 控制周期: ≤20ms
  - 状态反馈: ≤10ms
- **可靠性**: CRC16校验 + 序列号防重复

### 1.3 协议特点
- **高效传输**: CANFD大数据帧，减少总线占用
- **实时监控**: 支持示波器实时数据显示
- **安全可靠**: 多重校验和错误处理机制
- **扩展性强**: 预留扩展字段，支持功能升级

---

## 🆔 2. CAN ID 分配表

### 2.1 ID分配原则
- **11位标准ID**: 使用标准CAN ID格式
- **优先级设计**: ID越小优先级越高
- **功能分组**: 按功能类型分配ID段

### 2.2 ID分配表

| 功能类型 | ID范围 | 具体ID | 描述 | 优先级 |
|---------|--------|--------|------|--------|
| 系统广播 | 0x000-0x07F | 0x000 | 系统广播命令 | 最高 |
| 系统状态 | 0x080-0x0FF | 0x080 | 系统状态反馈 | 高 |
| 电机控制 | 0x100-0x17F | 0x101-0x106 | 电机1-6控制命令 | 中 |
| 电机状态 | 0x180-0x1FF | 0x181-0x186 | 电机1-6状态反馈 | 中 |
| 紧急停止 | 0x700-0x70F | 0x700 | 紧急停止命令 | 最高 |
| 心跳包 | 0x701-0x7FF | 0x701 | 系统心跳包 | 低 |

### 2.3 ID计算宏定义
```c
// 基础ID定义
#define CAN_ID_SYSTEM_BROADCAST    0x000
#define CAN_ID_SYSTEM_STATUS       0x080
#define CAN_ID_MOTOR_CMD_BASE      0x100
#define CAN_ID_MOTOR_STATUS_BASE   0x180
#define CAN_ID_EMERGENCY_STOP      0x700
#define CAN_ID_HEARTBEAT           0x701

// ID计算宏
#define GET_MOTOR_CMD_ID(motor_id)    (CAN_ID_MOTOR_CMD_BASE + motor_id)
#define GET_MOTOR_STATUS_ID(motor_id) (CAN_ID_MOTOR_STATUS_BASE + motor_id)
```

---

## 📦 3. 数据帧格式定义

### 3.1 数据结构对齐
```c
#pragma pack(1)  // 强制1字节对齐，确保数据结构一致性
```

### 3.2 帧类型枚举
```c
enum FrameType {
    FRAME_TYPE_CONTROL = 0x01,      // 控制命令帧
    FRAME_TYPE_STATUS = 0x02,       // 状态反馈帧
    FRAME_TYPE_BROADCAST = 0x03,    // 系统广播帧
    FRAME_TYPE_HEARTBEAT = 0x04     // 心跳帧
};
```

### 3.3 控制命令类型
```c
enum CommandType {
    CMD_STOP = 0x00,           // 停止电机
    CMD_START = 0x01,          // 启动电机
    CMD_RESET = 0x02,          // 复位电机控制器
    CMD_ENABLE = 0x03,         // 使能电机
    CMD_DISABLE = 0x04,        // 失能电机
    CMD_SET_SPEED = 0x10,      // 设置目标速度
    CMD_SET_POSITION = 0x11,   // 设置目标位置
    CMD_JOG_FORWARD = 0x20,    // 正向点动
    CMD_JOG_REVERSE = 0x21,    // 反向点动
    CMD_HOME = 0x30,           // 回零操作
    CMD_CLEAR_ERROR = 0x40     // 清除错误状态
};
```

### 3.4 控制模式枚举
```c
enum ControlMode {
    MODE_POSITION = 0x01,      // 位置控制模式
    MODE_SPEED = 0x02,         // 速度控制模式
    MODE_TORQUE = 0x03,        // 转矩控制模式
    MODE_PROFILE_POS = 0x04,   // 轮廓位置模式
    MODE_PROFILE_VEL = 0x05,   // 轮廓速度模式
    MODE_HOMING = 0x06         // 回零模式
};
```

---

## 🔧 4. 电机控制命令帧 (上位机→下位机)

### 4.1 数据结构定义
```c
typedef struct {
    // === 帧头信息 (4字节) ===
    uint8_t  frame_type;      // 帧类型: 0x01=控制命令
    uint8_t  motor_id;        // 电机ID: 1-6
    uint8_t  sequence;        // 序列号(防重复，循环0-255)
    uint8_t  priority;        // 优先级: 0=最高, 255=最低
    
    // === 控制参数 (12字节) ===
    uint8_t  command;         // 控制命令(见CommandType枚举)
    uint8_t  control_mode;    // 控制模式(见ControlMode枚举)
    uint16_t target_speed;    // 目标速度 (RPM, 0-65535)
    uint32_t target_position; // 目标位置 (脉冲数)
    uint16_t acceleration;    // 加速度 (RPM/s)
    uint16_t deceleration;    // 减速度 (RPM/s)
    
    // === 校验和时间戳 (4字节) ===
    uint16_t checksum;        // 校验和(CRC16)
    uint16_t timestamp;       // 时间戳(毫秒低16位)
} MotorControlFrame;
// 总长度: 20字节
```

### 4.2 字段详细说明

| 字段 | 类型 | 长度 | 取值范围 | 说明 |
|------|------|------|----------|------|
| frame_type | uint8_t | 1字节 | 0x01 | 固定值，标识控制命令帧 |
| motor_id | uint8_t | 1字节 | 1-6 | 目标电机ID |
| sequence | uint8_t | 1字节 | 0-255 | 序列号，防止重复执行 |
| priority | uint8_t | 1字节 | 0-255 | 命令优先级，0最高 |
| command | uint8_t | 1字节 | 见枚举 | 具体控制命令 |
| control_mode | uint8_t | 1字节 | 见枚举 | 控制模式 |
| target_speed | uint16_t | 2字节 | 0-65535 | 目标速度(RPM) |
| target_position | uint32_t | 4字节 | 0-4294967295 | 目标位置(脉冲) |
| acceleration | uint16_t | 2字节 | 0-65535 | 加速度(RPM/s) |
| deceleration | uint16_t | 2字节 | 0-65535 | 减速度(RPM/s) |
| checksum | uint16_t | 2字节 | 0-65535 | CRC16校验和 |
| timestamp | uint16_t | 2字节 | 0-65535 | 时间戳低16位 |

### 4.3 使用示例
```c
// 启动电机1，速度模式，目标速度1000RPM
MotorControlFrame cmd = {
    .frame_type = FRAME_TYPE_CONTROL,
    .motor_id = 1,
    .sequence = 0x12,
    .priority = 0,
    .command = CMD_START,
    .control_mode = MODE_SPEED,
    .target_speed = 1000,
    .target_position = 0,
    .acceleration = 500,
    .deceleration = 500,
    .checksum = 0,  // 需要计算
    .timestamp = 0  // 需要填充
};
```

---

## 📊 5. 电机状态反馈帧 (下位机→上位机)

### 5.1 数据结构定义
```c
typedef struct {
    // === 帧头信息 (4字节) ===
    uint8_t  frame_type;        // 帧类型: 0x02=状态反馈
    uint8_t  motor_id;          // 电机ID: 1-6
    uint8_t  sequence;          // 序列号(与控制命令对应)
    uint8_t  status_flags;      // 状态标志位(见StatusFlags)
    
    // === 实时数据 (24字节) - 给示波器显示 ===
    uint16_t current_speed;     // 当前速度 (RPM) → 示波器蓝色通道
    uint32_t current_position;  // 当前位置 (脉冲数)
    uint16_t motor_current;     // 电机电流 (mA) → 示波器红色通道
    uint16_t motor_voltage;     // 电机电压 (mV) → 示波器绿色通道
    uint16_t temperature;       // 温度 (0.1°C) → 示波器黄色通道
    uint16_t torque;            // 转矩 (0.1Nm)
    uint32_t encoder_position;  // 编码器位置 (脉冲)
    uint16_t following_error;   // 跟随误差 (脉冲)
    uint16_t load_ratio;        // 负载率 (0.1%)
    
    // === 错误和诊断信息 (8字节) ===
    uint16_t error_code;        // 错误代码(见ErrorCode枚举)
    uint16_t warning_code;      // 警告代码
    uint32_t diagnostic_info;   // 诊断信息
    
    // === 校验和时间戳 (4字节) ===
    uint16_t checksum;          // 校验和(CRC16)
    uint16_t timestamp;         // 时间戳(毫秒低16位)
} MotorStatusFrame;
// 总长度: 40字节
```

### 5.2 状态标志位定义
```c
enum StatusFlags {
    STATUS_ENABLED = 0x01,         // 电机使能状态
    STATUS_RUNNING = 0x02,         // 电机运行状态
    STATUS_ERROR = 0x04,           // 错误状态
    STATUS_WARNING = 0x08,         // 警告状态
    STATUS_HOMED = 0x10,           // 已完成回零
    STATUS_MOVING = 0x20,          // 正在运动
    STATUS_TARGET_REACHED = 0x40,  // 已到达目标位置
    STATUS_EMERGENCY = 0x80        // 紧急停止状态
};
```

### 5.3 错误代码定义
```c
enum ErrorCode {
    ERR_NO_ERROR = 0x0000,         // 无错误
    ERR_OVERCURRENT = 0x0001,      // 过流保护
    ERR_OVERVOLTAGE = 0x0002,      // 过压保护
    ERR_UNDERVOLTAGE = 0x0003,     // 欠压保护
    ERR_OVERTEMPERATURE = 0x0004,  // 过温保护
    ERR_ENCODER_FAULT = 0x0005,    // 编码器故障
    ERR_COMMUNICATION = 0x0006,    // 通信故障
    ERR_POSITION_LIMIT = 0x0007,   // 位置限制
    ERR_SPEED_LIMIT = 0x0008,      // 速度限制
    ERR_FOLLOWING_ERROR = 0x0009,  // 跟随误差过大
    ERR_EMERGENCY_STOP = 0x000A,   // 紧急停止
    ERR_SYSTEM_FAULT = 0x000B      // 系统故障
};
```

### 5.4 示波器数据映射
状态帧中的实时数据直接映射到上位机示波器的4个通道：

| 通道 | 颜色 | 数据源 | 单位 | 说明 |
|------|------|--------|------|------|
| 通道1 | 蓝色 | current_speed | RPM | 当前转速 |
| 通道2 | 红色 | motor_current | mA | 电机电流 |
| 通道3 | 绿色 | motor_voltage | mV | 电机电压 |
| 通道4 | 黄色 | temperature | 0.1°C | 电机温度 |

---

## 🔄 6. 通信流程

### 6.1 基本通信流程
```
上位机                           下位机
  |                               |
  |---> 控制命令帧 (20字节)        |
  |     CAN ID: 0x101-0x106       |
  |                               |---> 解析命令
  |                               |---> 执行控制
  |                               |---> 更新状态
  |                               |
  |<--- 状态反馈帧 (40字节)        |
  |     CAN ID: 0x181-0x186       |
  |                               |
```

### 6.2 心跳机制
```c
// 心跳帧结构 (8字节)
typedef struct {
    uint8_t  frame_type;      // 0x04 = 心跳帧
    uint8_t  system_status;   // 系统状态
    uint16_t alive_counter;   // 心跳计数器
    uint32_t timestamp;       // 完整时间戳
} HeartbeatFrame;
```

### 6.3 紧急停止机制
```c
// 紧急停止帧 (4字节)
typedef struct {
    uint8_t  frame_type;      // 0x05 = 紧急停止
    uint8_t  stop_reason;     // 停止原因
    uint16_t reserved;        // 保留字段
} EmergencyStopFrame;
```

---

## 🛡️ 7. 错误处理与可靠性

### 7.1 CRC16校验算法
```c
uint16_t calculate_crc16(const uint8_t* data, uint16_t length) {
    uint16_t crc = 0xFFFF;
    for (uint16_t i = 0; i < length; i++) {
        crc ^= data[i];
        for (uint8_t j = 0; j < 8; j++) {
            if (crc & 0x0001) {
                crc = (crc >> 1) ^ 0xA001;
            } else {
                crc = crc >> 1;
            }
        }
    }
    return crc;
}
```

### 7.2 序列号机制
- **防重复**: 每个命令帧包含序列号，下位机记录最后处理的序列号
- **循环使用**: 序列号0-255循环使用
- **超时处理**: 超过100ms未收到新命令，清除序列号记录

### 7.3 超时与重传
- **命令超时**: 上位机发送命令后50ms内未收到状态反馈，进行重传
- **最大重传**: 最多重传3次，失败后报告通信错误
- **状态超时**: 下位机100ms内未收到新命令，保持当前状态

---

## 📈 8. 性能参数

### 8.1 通信性能
- **波特率**: 仲裁段500Kbps，数据段2Mbps
- **帧间隔**: 最小1ms
- **响应时间**: 典型值5ms，最大20ms
- **吞吐量**: 约120帧/秒 (6电机同时控制)

### 8.2 实时性指标
- **控制延迟**: ≤20ms (命令发送到执行)
- **状态更新**: 10ms周期 (100Hz)
- **示波器刷新**: 50ms周期 (20Hz)
- **心跳周期**: 1000ms (1Hz)

### 8.3 可靠性指标
- **误码率**: <10^-6
- **丢包率**: <0.1%
- **重传成功率**: >99.9%
- **系统可用性**: >99.99%

---

## 🔧 9. 实现指南

### 9.1 上位机实现要点
```cpp
// Qt/C++ 实现示例
class CANFDProtocol {
private:
    QCanBusDevice* m_canDevice;
    QTimer* m_heartbeatTimer;
    QTimer* m_statusTimer;

public:
    // 发送控制命令
    bool sendControlCommand(uint8_t motorId, const MotorControlFrame& cmd);

    // 接收状态反馈
    void processStatusFrame(const QCanBusFrame& frame);

    // 心跳处理
    void sendHeartbeat();

    // 错误处理
    void handleError(uint8_t motorId, uint16_t errorCode);
};
```

### 9.2 下位机实现要点
```c
// 嵌入式C实现示例
typedef struct {
    uint8_t last_sequence[MOTOR_COUNT];
    uint32_t last_command_time[MOTOR_COUNT];
    MotorStatusFrame status[MOTOR_COUNT];
} ProtocolManager;

// CAN接收中断处理
void CAN_RX_IRQHandler(void) {
    CanRxMsg rxMsg;
    CAN_Receive(CAN1, CAN_FIFO0, &rxMsg);

    if (rxMsg.StdId >= CAN_ID_MOTOR_CMD_BASE &&
        rxMsg.StdId <= CAN_ID_MOTOR_CMD_BASE + MOTOR_COUNT) {
        processControlCommand(&rxMsg);
    }
}

// 处理控制命令
void processControlCommand(CanRxMsg* msg) {
    MotorControlFrame* cmd = (MotorControlFrame*)msg->Data;

    // 校验CRC
    if (!verifyCRC16(cmd)) return;

    // 检查序列号
    uint8_t motorId = cmd->motor_id;
    if (cmd->sequence == protocol.last_sequence[motorId-1]) return;

    // 执行命令
    executeMotorCommand(motorId, cmd);

    // 更新序列号
    protocol.last_sequence[motorId-1] = cmd->sequence;
}
```

---

## 🧪 10. 测试与调试

### 10.1 协议测试用例

#### 测试用例1：基本控制命令
```c
// 测试电机启动命令
MotorControlFrame testCmd = {
    .frame_type = FRAME_TYPE_CONTROL,
    .motor_id = 1,
    .sequence = 0x01,
    .priority = 0,
    .command = CMD_START,
    .control_mode = MODE_SPEED,
    .target_speed = 1000,
    .target_position = 0,
    .acceleration = 500,
    .deceleration = 500,
    .checksum = 0x1234,  // 实际需要计算
    .timestamp = 0x5678
};
```

#### 测试用例2：状态反馈验证
```c
// 验证状态反馈帧格式
MotorStatusFrame testStatus = {
    .frame_type = FRAME_TYPE_STATUS,
    .motor_id = 1,
    .sequence = 0x01,
    .status_flags = STATUS_ENABLED | STATUS_RUNNING,
    .current_speed = 1000,
    .current_position = 12345,
    .motor_current = 2500,  // 2.5A
    .motor_voltage = 24000, // 24V
    .temperature = 450,     // 45.0°C
    .torque = 150,          // 15.0Nm
    .encoder_position = 12345,
    .following_error = 5,
    .load_ratio = 750,      // 75.0%
    .error_code = ERR_NO_ERROR,
    .warning_code = 0,
    .diagnostic_info = 0,
    .checksum = 0xABCD,
    .timestamp = 0xEF01
};
```

### 10.2 调试工具

#### CAN总线分析器配置
```
波特率设置:
- 仲裁段: 500Kbps
- 数据段: 2Mbps
- 采样点: 87.5%

过滤器设置:
- 接收所有ID: 0x000-0x7FF
- 重点监控: 0x101-0x106, 0x181-0x186

触发条件:
- 错误帧检测
- 特定ID帧
- 数据长度异常
```

#### 协议解析脚本
```python
# Python协议解析示例
def parse_control_frame(data):
    if len(data) != 20:
        return None

    frame = {
        'frame_type': data[0],
        'motor_id': data[1],
        'sequence': data[2],
        'priority': data[3],
        'command': data[4],
        'control_mode': data[5],
        'target_speed': int.from_bytes(data[6:8], 'little'),
        'target_position': int.from_bytes(data[8:12], 'little'),
        'acceleration': int.from_bytes(data[12:14], 'little'),
        'deceleration': int.from_bytes(data[14:16], 'little'),
        'checksum': int.from_bytes(data[16:18], 'little'),
        'timestamp': int.from_bytes(data[18:20], 'little')
    }
    return frame
```

### 10.3 常见问题排查

#### Q1: 命令无响应
```
检查项目:
1. CAN ID是否正确 (0x101-0x106)
2. 数据长度是否为20字节
3. CRC校验是否正确
4. 电机ID是否在1-6范围内
5. 下位机是否正常运行
```

#### Q2: 状态数据异常
```
检查项目:
1. 状态帧ID是否正确 (0x181-0x186)
2. 数据长度是否为40字节
3. 状态标志位是否合理
4. 数值范围是否在预期内
5. 时间戳是否递增
```

#### Q3: 通信不稳定
```
检查项目:
1. 总线终端电阻 (120Ω)
2. 线缆质量和长度
3. 波特率配置是否一致
4. 电源和地线连接
5. 电磁干扰情况
```

---

## 📚 11. 附录

### 11.1 协议版本历史
- **V1.0** (2025-01-28): 初始版本，支持6电机基本控制

### 11.2 相关文档
- `motorprotocol.h` - C/C++协议头文件
- `下位机CANFD协议开发文档.md` - 下位机实现指南
- `CANFD硬件接口说明.pdf` - 硬件连接文档

### 11.3 技术支持
- **开发团队**: 电机控制系统开发组
- **联系方式**: <EMAIL>
- **更新频率**: 根据需求不定期更新

### 11.4 免责声明
本协议文档仅供开发参考，实际实现时请根据具体硬件平台和需求进行适配。开发团队不对因使用本协议而产生的任何问题承担责任。

---

**文档结束**

*本文档包含完整的CANFD协议规范，涵盖数据结构、通信流程、错误处理、测试方法等所有必要信息。建议开发人员仔细阅读并严格按照规范实现。*
```
