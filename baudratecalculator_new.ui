<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>BaudrateCalculator</class>
 <widget class="QDialog" name="BaudrateCalculator">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>1200</width>
    <height>1000</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Baud Calc - V1.0.25.50402</string>
  </property>
  <property name="styleSheet">
   <string>QDialog {
    background-color: #f0f0f0;
    font-family: 'Microsoft YaHei UI', 'Segoe UI', Arial, sans-serif;
}

QGroupBox {
    font-weight: bold;
    font-size: 12px;
    color: #333333;
    border: 2px solid #dc3545;
    border-radius: 8px;
    margin-top: 10px;
    padding-top: 10px;
    background-color: white;
}

QGroupBox::title {
    subcontrol-origin: margin;
    left: 10px;
    padding: 0 8px 0 8px;
    color: white;
    background-color: #dc3545;
    font-weight: bold;
    border-radius: 4px;
}

QLabel {
    color: #333333;
    font-size: 12px;
    font-weight: normal;
}

QComboBox, QSpinBox, QDoubleSpinBox {
    border: 1px solid #cccccc;
    border-radius: 3px;
    padding: 4px;
    font-size: 12px;
    background-color: white;
    min-height: 20px;
}

QTableWidget {
    border: 1px solid #cccccc;
    border-radius: 3px;
    background-color: white;
    gridline-color: #e0e0e0;
    font-size: 11px;
    selection-background-color: #0078d4;
}

QTableWidget::item {
    padding: 6px;
    border-bottom: 1px solid #e0e0e0;
}

QTableWidget::item:selected {
    background-color: #0078d4;
    color: white;
}

QHeaderView::section {
    background-color: #dc3545;
    color: white;
    padding: 8px;
    border: none;
    font-weight: bold;
    font-size: 11px;
}

QTextEdit {
    border: 1px solid #cccccc;
    border-radius: 3px;
    padding: 8px;
    font-family: 'Consolas', 'Courier New', monospace;
    font-size: 11px;
    background-color: white;
}

QPushButton {
    background-color: white;
    color: #dc3545;
    border: 2px solid #dc3545;
    border-radius: 6px;
    padding: 8px 16px;
    font-weight: bold;
    font-size: 12px;
    min-width: 80px;
    min-height: 30px;
}

QPushButton:hover {
    background-color: #dc3545;
    color: white;
}

QPushButton#quickConfigButton {
    background-color: #28a745;
    color: white;
    border: 2px solid #28a745;
}

QPushButton#quickConfigButton:hover {
    background-color: #218838;
    border-color: #218838;
}</string>
  </property>
  <layout class="QVBoxLayout" name="mainLayout">
   <property name="spacing">
    <number>10</number>
   </property>
   <property name="leftMargin">
    <number>15</number>
   </property>
   <property name="topMargin">
    <number>15</number>
   </property>
   <property name="rightMargin">
    <number>15</number>
   </property>
   <property name="bottomMargin">
    <number>15</number>
   </property>
   <item>
    <!-- 设备配置区域 -->
    <widget class="QGroupBox" name="deviceGroup">
     <property name="title">
      <string>设备配置</string>
     </property>
     <layout class="QHBoxLayout" name="deviceLayout">
      <item>
       <widget class="QLabel" name="deviceTypeLabel">
        <property name="text">
         <string>Dev Type:</string>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QComboBox" name="deviceTypeCombo">
        <item>
         <property name="text">
          <string>USBCANFD-100U</string>
         </property>
        </item>
       </widget>
      </item>
      <item>
       <widget class="QLabel" name="clockLabel">
        <property name="text">
         <string>Clock:</string>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QComboBox" name="clockCombo">
        <property name="currentText">
         <string>40</string>
        </property>
        <item>
         <property name="text">
          <string>40</string>
         </property>
        </item>
        <item>
         <property name="text">
          <string>80</string>
         </property>
        </item>
        <item>
         <property name="text">
          <string>60</string>
         </property>
        </item>
        <item>
         <property name="text">
          <string>20</string>
         </property>
        </item>
        <item>
         <property name="text">
          <string>24</string>
         </property>
        </item>
        <item>
         <property name="text">
          <string>16</string>
         </property>
        </item>
        <item>
         <property name="text">
          <string>8</string>
         </property>
        </item>
       </widget>
      </item>
      <item>
       <widget class="QLabel" name="mhzLabel">
        <property name="text">
         <string>MHz</string>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QLabel" name="titleLabel">
        <property name="text">
         <string>CAN (FD) baud recomand settings</string>
        </property>
        <property name="alignment">
         <set>Qt::AlignRight|Qt::AlignVCenter</set>
        </property>
       </widget>
      </item>
      <item>
       <spacer name="horizontalSpacer">
        <property name="orientation">
         <enum>Qt::Horizontal</enum>
        </property>
        <property name="sizeHint" stdset="0">
         <size>
          <width>40</width>
          <height>20</height>
         </size>
        </property>
       </spacer>
      </item>
     </layout>
    </widget>
   </item>

   <item>
    <!-- CANFD仲裁段配置 -->
    <widget class="QGroupBox" name="arbitrationGroup">
     <property name="title">
      <string>CANFD仲裁段配置</string>
     </property>
     <layout class="QVBoxLayout" name="arbitrationLayout">
      <item>
       <layout class="QHBoxLayout" name="arbitrationControlLayout">
        <item>
         <widget class="QLabel" name="baudLabel">
          <property name="text">
           <string>波特率 (Kbps):</string>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QComboBox" name="baudCombo">
          <property name="editable">
           <bool>true</bool>
          </property>
          <property name="currentText">
           <string>500</string>
          </property>
          <item>
           <property name="text">
            <string>1000</string>
           </property>
          </item>
          <item>
           <property name="text">
            <string>800</string>
           </property>
          </item>
          <item>
           <property name="text">
            <string>500</string>
           </property>
          </item>
          <item>
           <property name="text">
            <string>250</string>
           </property>
          </item>
          <item>
           <property name="text">
            <string>125</string>
           </property>
          </item>
          <item>
           <property name="text">
            <string>100</string>
           </property>
          </item>
          <item>
           <property name="text">
            <string>50</string>
           </property>
          </item>
          <item>
           <property name="text">
            <string>20</string>
           </property>
          </item>
          <item>
           <property name="text">
            <string>10</string>
           </property>
          </item>
          <item>
           <property name="text">
            <string>5</string>
           </property>
          </item>
         </widget>
        </item>
        <item>
         <widget class="QLabel" name="kbpsLabel">
          <property name="text">
           <string>Kbps</string>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QLabel" name="diffLabel">
          <property name="text">
           <string>Diff:</string>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QLineEdit" name="diffLineEdit">
          <property name="text">
           <string>0.05</string>
          </property>
          <property name="maxLength">
           <number>10</number>
          </property>
          <property name="alignment">
           <set>Qt::AlignCenter</set>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QLabel" name="percentLabel">
          <property name="text">
           <string>%</string>
          </property>
         </widget>
        </item>
        <item>
         <spacer name="horizontalSpacer_2">
          <property name="orientation">
           <enum>Qt::Horizontal</enum>
          </property>
          <property name="sizeHint" stdset="0">
           <size>
            <width>40</width>
            <height>20</height>
           </size>
          </property>
         </spacer>
        </item>
       </layout>
      </item>
      <item>
       <layout class="QHBoxLayout" name="sjwLayout">
        <item>
         <widget class="QLabel" name="sjwLabel">
          <property name="text">
           <string>SJW:</string>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QComboBox" name="sjwCombo">
          <item>
           <property name="text">
            <string>0</string>
           </property>
          </item>
          <item>
           <property name="text">
            <string>1</string>
           </property>
          </item>
          <item>
           <property name="text">
            <string>2</string>
           </property>
          </item>
          <item>
           <property name="text">
            <string>3</string>
           </property>
          </item>
          <item>
           <property name="text">
            <string>4</string>
           </property>
          </item>
          <item>
           <property name="text">
            <string>5</string>
           </property>
          </item>
          <item>
           <property name="text">
            <string>6</string>
           </property>
          </item>
          <item>
           <property name="text">
            <string>7</string>
           </property>
          </item>
         </widget>
        </item>
        <item>
         <widget class="QLabel" name="plusOneLabel">
          <property name="text">
           <string>+1</string>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QCheckBox" name="tseg2CheckBox">
          <property name="text">
           <string>tseg2 >= sjw</string>
          </property>
          <property name="checked">
           <bool>true</bool>
          </property>
         </widget>
        </item>
        <item>
         <spacer name="horizontalSpacer_3">
          <property name="orientation">
           <enum>Qt::Horizontal</enum>
          </property>
          <property name="sizeHint" stdset="0">
           <size>
            <width>40</width>
            <height>20</height>
           </size>
          </property>
         </spacer>
        </item>
       </layout>
      </item>
      <item>
       <!-- 仲裁段表格索引输入框 -->
       <layout class="QHBoxLayout" name="arbitrationIndexLayout">
        <item>
         <widget class="QLabel" name="arbitrationIndexLabel">
          <property name="text">
           <string>快速查找:</string>
          </property>
          <property name="minimumWidth">
           <number>80</number>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QLabel" name="brpLabel1">
          <property name="text">
           <string>BRP:</string>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QLineEdit" name="arbitrationBrpLineEdit">
          <property name="maximumWidth">
           <number>60</number>
          </property>
          <property name="placeholderText">
           <string>输入BRP</string>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QLabel" name="tseg1Label1">
          <property name="text">
           <string>TSEG1:</string>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QLineEdit" name="arbitrationTseg1LineEdit">
          <property name="maximumWidth">
           <number>60</number>
          </property>
          <property name="placeholderText">
           <string>输入TSEG1</string>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QLabel" name="tseg2Label1">
          <property name="text">
           <string>TSEG2:</string>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QLineEdit" name="arbitrationTseg2LineEdit">
          <property name="maximumWidth">
           <number>60</number>
          </property>
          <property name="placeholderText">
           <string>输入TSEG2</string>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QPushButton" name="arbitrationClearButton">
          <property name="text">
           <string>清除</string>
          </property>
          <property name="maximumWidth">
           <number>60</number>
          </property>
         </widget>
        </item>
        <item>
         <spacer name="arbitrationIndexSpacer">
          <property name="orientation">
           <enum>Qt::Horizontal</enum>
          </property>
          <property name="sizeHint" stdset="0">
           <size>
            <width>40</width>
            <height>20</height>
           </size>
          </property>
         </spacer>
        </item>
       </layout>
      </item>
      <item>
       <widget class="QTableWidget" name="arbitrationTable">
        <property name="minimumHeight">
         <number>200</number>
        </property>
        <property name="maximumHeight">
         <number>200</number>
        </property>
        <property name="alternatingRowColors">
         <bool>true</bool>
        </property>
        <property name="selectionBehavior">
         <enum>QAbstractItemView::SelectRows</enum>
        </property>
        <column>
         <property name="text">
          <string>Value</string>
         </property>
        </column>
        <column>
         <property name="text">
          <string>BRP</string>
         </property>
        </column>
        <column>
         <property name="text">
          <string>TSEG1</string>
         </property>
        </column>
        <column>
         <property name="text">
          <string>TSEG2</string>
         </property>
        </column>
        <column>
         <property name="text">
          <string>SMP</string>
         </property>
        </column>
        <column>
         <property name="text">
          <string>Baud</string>
         </property>
        </column>
        <column>
         <property name="text">
          <string>Diff</string>
         </property>
        </column>
       </widget>
      </item>
     </layout>
    </widget>
   </item>
   <item>
    <!-- CANFD数据段配置 -->
    <widget class="QGroupBox" name="dataGroup">
     <property name="title">
      <string>CANFD数据段配置</string>
     </property>
     <layout class="QVBoxLayout" name="dataLayout">
      <item>
       <layout class="QHBoxLayout" name="dataControlLayout">
        <item>
         <widget class="QLabel" name="dataBaudLabel">
          <property name="text">
           <string>波特率 (Kbps):</string>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QComboBox" name="dataBaudCombo">
          <property name="editable">
           <bool>true</bool>
          </property>
          <property name="currentText">
           <string>4000</string>
          </property>
          <item>
           <property name="text">
            <string>10000</string>
           </property>
          </item>
          <item>
           <property name="text">
            <string>8000</string>
           </property>
          </item>
          <item>
           <property name="text">
            <string>6000</string>
           </property>
          </item>
          <item>
           <property name="text">
            <string>5000</string>
           </property>
          </item>
          <item>
           <property name="text">
            <string>4000</string>
           </property>
          </item>
          <item>
           <property name="text">
            <string>3000</string>
           </property>
          </item>
          <item>
           <property name="text">
            <string>2000</string>
           </property>
          </item>
          <item>
           <property name="text">
            <string>1500</string>
           </property>
          </item>
          <item>
           <property name="text">
            <string>1000</string>
           </property>
          </item>
          <item>
           <property name="text">
            <string>800</string>
           </property>
          </item>
          <item>
           <property name="text">
            <string>500</string>
           </property>
          </item>
         </widget>
        </item>
        <item>
         <widget class="QLabel" name="dataKbpsLabel">
          <property name="text">
           <string>Kbps</string>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QLabel" name="dataDiffLabel">
          <property name="text">
           <string>Diff:</string>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QLineEdit" name="dataDiffLineEdit">
          <property name="text">
           <string>0.05</string>
          </property>
          <property name="maxLength">
           <number>10</number>
          </property>
          <property name="alignment">
           <set>Qt::AlignCenter</set>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QLabel" name="dataPercentLabel">
          <property name="text">
           <string>%</string>
          </property>
         </widget>
        </item>
        <item>
         <spacer name="horizontalSpacer_4">
          <property name="orientation">
           <enum>Qt::Horizontal</enum>
          </property>
          <property name="sizeHint" stdset="0">
           <size>
            <width>40</width>
            <height>20</height>
           </size>
          </property>
         </spacer>
        </item>
       </layout>
      </item>
      <item>
       <layout class="QHBoxLayout" name="dataSjwLayout">
        <item>
         <widget class="QLabel" name="dataSjwLabel">
          <property name="text">
           <string>SJW:</string>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QComboBox" name="dataSjwCombo">
          <item>
           <property name="text">
            <string>0</string>
           </property>
          </item>
          <item>
           <property name="text">
            <string>1</string>
           </property>
          </item>
          <item>
           <property name="text">
            <string>2</string>
           </property>
          </item>
          <item>
           <property name="text">
            <string>3</string>
           </property>
          </item>
          <item>
           <property name="text">
            <string>4</string>
           </property>
          </item>
          <item>
           <property name="text">
            <string>5</string>
           </property>
          </item>
          <item>
           <property name="text">
            <string>6</string>
           </property>
          </item>
          <item>
           <property name="text">
            <string>7</string>
           </property>
          </item>
         </widget>
        </item>
        <item>
         <widget class="QLabel" name="dataPlusOneLabel">
          <property name="text">
           <string>+1</string>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QCheckBox" name="dataTseg2CheckBox">
          <property name="text">
           <string>tseg2 >= sjw</string>
          </property>
          <property name="checked">
           <bool>true</bool>
          </property>
         </widget>
        </item>
        <item>
         <spacer name="horizontalSpacer_5">
          <property name="orientation">
           <enum>Qt::Horizontal</enum>
          </property>
          <property name="sizeHint" stdset="0">
           <size>
            <width>40</width>
            <height>20</height>
           </size>
          </property>
         </spacer>
        </item>
       </layout>
      </item>
      <item>
       <!-- 数据段表格索引输入框 -->
       <layout class="QHBoxLayout" name="dataIndexLayout">
        <item>
         <widget class="QLabel" name="dataIndexLabel">
          <property name="text">
           <string>快速查找:</string>
          </property>
          <property name="minimumWidth">
           <number>80</number>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QLabel" name="brpLabel2">
          <property name="text">
           <string>BRP:</string>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QLineEdit" name="dataBrpLineEdit">
          <property name="maximumWidth">
           <number>60</number>
          </property>
          <property name="placeholderText">
           <string>输入BRP</string>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QLabel" name="tseg1Label2">
          <property name="text">
           <string>TSEG1:</string>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QLineEdit" name="dataTseg1LineEdit">
          <property name="maximumWidth">
           <number>60</number>
          </property>
          <property name="placeholderText">
           <string>输入TSEG1</string>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QLabel" name="tseg2Label2">
          <property name="text">
           <string>TSEG2:</string>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QLineEdit" name="dataTseg2LineEdit">
          <property name="maximumWidth">
           <number>60</number>
          </property>
          <property name="placeholderText">
           <string>输入TSEG2</string>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QPushButton" name="dataClearButton">
          <property name="text">
           <string>清除</string>
          </property>
          <property name="maximumWidth">
           <number>60</number>
          </property>
         </widget>
        </item>
        <item>
         <spacer name="dataIndexSpacer">
          <property name="orientation">
           <enum>Qt::Horizontal</enum>
          </property>
          <property name="sizeHint" stdset="0">
           <size>
            <width>40</width>
            <height>20</height>
           </size>
          </property>
         </spacer>
        </item>
       </layout>
      </item>
      <item>
       <widget class="QTableWidget" name="dataTable">
        <property name="minimumHeight">
         <number>200</number>
        </property>
        <property name="maximumHeight">
         <number>200</number>
        </property>
        <property name="alternatingRowColors">
         <bool>true</bool>
        </property>
        <property name="selectionBehavior">
         <enum>QAbstractItemView::SelectRows</enum>
        </property>
        <column>
         <property name="text">
          <string>Value</string>
         </property>
        </column>
        <column>
         <property name="text">
          <string>BRP</string>
         </property>
        </column>
        <column>
         <property name="text">
          <string>TSEG1</string>
         </property>
        </column>
        <column>
         <property name="text">
          <string>TSEG2</string>
         </property>
        </column>
        <column>
         <property name="text">
          <string>SMP</string>
         </property>
        </column>
        <column>
         <property name="text">
          <string>Baud</string>
         </property>
        </column>
        <column>
         <property name="text">
          <string>Diff</string>
         </property>
        </column>
       </widget>
      </item>
     </layout>
    </widget>
   </item>
   <item>
    <!-- 配置结果区域 -->
    <widget class="QGroupBox" name="resultGroup">
     <property name="title">
      <string>配置结果</string>
     </property>
     <layout class="QVBoxLayout" name="resultLayout">
      <item>
       <layout class="QHBoxLayout" name="resultInputLayout">
        <item>
         <widget class="QLineEdit" name="resultLineEdit">
          <property name="readOnly">
           <bool>true</bool>
          </property>
          <property name="text">
           <string>500Kbps(75%),4.0Mbps(80%),(40,04C00001,00400002)</string>
          </property>
          <property name="styleSheet">
           <string>QLineEdit {
    font-family: 'Consolas', 'Courier New', monospace;
    font-size: 12px;
    padding: 8px;
    background-color: white;
    border: 1px solid #cccccc;
    border-radius: 3px;
}</string>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QPushButton" name="copyResultButton">
          <property name="text">
           <string>Copy</string>
          </property>
          <property name="minimumSize">
           <size>
            <width>80</width>
            <height>30</height>
           </size>
          </property>
         </widget>
        </item>
       </layout>
      </item>
      <item>
       <widget class="QLabel" name="tipsLabel">
        <property name="text">
         <string>Tips:CANFD ABIT BRP shoud not greater than DBIT BRP;Arbitration Field baud&lt;=500kbps,recommend smp87.5%.Data Field baud&gt;800kbps,recommend smp75%.</string>
        </property>
        <property name="wordWrap">
         <bool>true</bool>
        </property>
        <property name="styleSheet">
         <string>color: #666666; font-size: 10px;</string>
        </property>
       </widget>
      </item>
     </layout>
    </widget>
   </item>
   <item>
    <!-- 底部按钮区域 -->
    <layout class="QHBoxLayout" name="buttonLayout">
     <item>
      <spacer name="horizontalSpacer_6">
       <property name="orientation">
        <enum>Qt::Horizontal</enum>
       </property>
       <property name="sizeHint" stdset="0">
        <size>
         <width>40</width>
         <height>20</height>
        </size>
       </property>
      </spacer>
     </item>
     <item>
      <widget class="QPushButton" name="returnButton">
       <property name="text">
        <string>返回</string>
       </property>
      </widget>
     </item>
    </layout>
   </item>
  </layout>
 </widget>
 <resources/>
 <connections/>
</ui>
