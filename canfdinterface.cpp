/**
 * @file canfdinterface.cpp
 * @brief CANFD通信接口类实现文件
 * @details 实现了CANFDInterface类的所有功能，包括CANFD协议的设备连接管理、
 *          长短帧交替发送、高速数据传输、BRS和ESI标志控制、频率调节、
 *          日志记录等完整的CANFD通信功能。
 * <AUTHOR>
 * @date 2025-07-02
 * @version 1.0
 */

#include "canfdinterface.h"      // CANFD接口类声明
#include "ui_canfdinterface.h"   // Qt Designer生成的UI类
#include <QDebug>                // Qt调试输出类
#include <QRegularExpression>    // Qt正则表达式类
#include <QTimer>                // Qt定时器类
#include <QMessageBox>           // Qt消息框类
#include <QFileDialog>           // Qt文件对话框类
#include <QTextStream>           // Qt文本流类
#include "messagebox_utils.h"    // 消息框工具类

/**
 * @brief CANFDInterface构造函数实现
 * @param parent 父窗口指针
 * @details 初始化CANFD通信接口的所有组件和设置：
 *          1. 初始化成员变量和状态
 *          2. 设置UI界面
 *          3. 建立信号槽连接
 *          4. 创建和配置定时器
 *          5. 更新控件状态
 */
CANFDInterface::CANFDInterface(QWidget *parent)
    : BaseCANInterface(parent)                          // 调用基类构造函数
    , ui(new Ui::CANFDInterface)                        // 创建UI实例
{
    qDebug() << "CANFDInterface: 开始构造函数";

    try {
        // 设置UI界面，加载由Qt Designer设计的界面布局
        ui->setupUi(this);
        qDebug() << "CANFDInterface: UI设置完成";

        // 设置信号槽连接
        setupConnections();
        qDebug() << "CANFDInterface: 连接设置完成";

        // 更新控件状态
        updateControlState();
        qDebug() << "CANFDInterface: 控制状态更新完成";

        // === 设置文本区域字体 ===
        // 确保发送和接收文本区域使用合适的字体大小
        QFont textFont("Consolas", 14);
        ui->sendTextEdit->setFont(textFont);
        ui->receiveTextEdit->setFont(textFont);
        qDebug() << "CANFDInterface: 文本区域字体设置完成";

        // === 定时器已在基类中初始化，这里只需要连接特定的槽函数 ===
        // 现在定时器指针应该正确指向基类的定时器了
        if (continuousSendTimer) {
            qDebug() << "CANFDInterface: 连接连续发送定时器信号";
            connect(continuousSendTimer, &QTimer::timeout, this, &CANFDInterface::onContinuousSendTimer);
            qDebug() << "CANFDInterface: 连续发送定时器信号连接成功";
        } else {
            qDebug() << "CANFDInterface: 错误 - continuousSendTimer仍为空，这不应该发生";
        }

        qDebug() << "CANFDInterface: 构造函数完成";
    } catch (...) {
        qDebug() << "CANFDInterface: 构造函数中发生异常";
    }
}

/**
 * @brief CANFDInterface析构函数实现
 * @details 清理资源和关闭连接：
 *          - 检查连接状态并停止通信
 *          - 释放UI资源
 */
CANFDInterface::~CANFDInterface()
{
    // 如果设备已连接，先停止通信
    if (isConnected) {
        stopCommunication();
    }

    // 释放UI资源
    delete ui;
}

/**
 * @brief 建立信号槽连接函数实现
 * @details 建立UI控件与槽函数之间的信号槽连接：
 *          1. 设备连接控制按钮连接
 *          2. 数据发送相关按钮连接
 *          3. 频率控制相关控件连接
 *          4. CANFD模式选择连接
 *          5. 异常处理和调试输出
 */
void CANFDInterface::setupConnections()
{
    qDebug() << "CANFDInterface: 开始设置连接";

    try {
        // === 设备连接控制按钮连接 ===
        connect(ui->connectButton, &QPushButton::clicked, this, &CANFDInterface::onConnectDevice);
        connect(ui->disconnectButton, &QPushButton::clicked, this, &CANFDInterface::onDisconnectDevice);

        // === 数据发送相关按钮连接 ===
        connect(ui->sendButton, &QPushButton::clicked, this, &CANFDInterface::onSendData);
        connect(ui->continuousSendButton, &QPushButton::clicked, this, &CANFDInterface::onContinuousSend);
        connect(ui->heartbeatButton, &QPushButton::clicked, this, &CANFDInterface::onSendHeartbeat);

        // === 界面操作按钮连接 ===
        connect(ui->clearSendButton, &QPushButton::clicked, this, &CANFDInterface::onClearSendArea);
        connect(ui->clearReceiveButton, &QPushButton::clicked, this, &CANFDInterface::onClearReceiveArea);
        connect(ui->saveDataButton, &QPushButton::clicked, this, &CANFDInterface::onSaveData);
        connect(ui->backButton, &QPushButton::clicked, this, &CANFDInterface::onBackClicked);

        // === 频率控制SpinBox连接 ===
        connect(ui->sendFrequencySpinBox, QOverload<int>::of(&QSpinBox::valueChanged),
                this, &CANFDInterface::onSendFrequencyChanged);
        connect(ui->receiveFrequencySpinBox, QOverload<int>::of(&QSpinBox::valueChanged),
                this, &CANFDInterface::onReceiveFrequencyChanged);

        // === 频率预设下拉框连接 ===
        connect(ui->sendFrequencyPresetCombo, &QComboBox::currentTextChanged,
                this, &CANFDInterface::onSendFrequencyPresetChanged);
        connect(ui->receiveFrequencyPresetCombo, &QComboBox::currentTextChanged,
                this, &CANFDInterface::onReceiveFrequencyPresetChanged);

        // === CANFD模式选择连接 ===
        connect(ui->fdModeComboBox, QOverload<int>::of(&QComboBox::currentIndexChanged),
                this, &CANFDInterface::onFdModeChanged);

        // 注意：定时器的信号槽连接在构造函数中单独处理
        qDebug() << "CANFDInterface: 连接设置完成";
    } catch (...) {
        qDebug() << "CANFDInterface: 连接设置时发生异常";
    }
}

/**
 * @brief 设置配置数据函数实现
 * @param config CAN配置数据结构
 * @details 保存从配置对话框传递过来的CAN配置参数
 */
void CANFDInterface::setConfigData(const CANConfigData &config)
{
    configData = config;                                          // 保存配置数据
}

/**
 * @brief 更新控件状态函数实现
 * @details 根据设备连接状态更新UI控件的启用/禁用状态：
 *          1. 连接/断开按钮状态控制
 *          2. 数据发送相关按钮状态控制
 *          3. 心跳功能按钮状态和文本更新
 */
void CANFDInterface::updateControlState()
{
    // === 设备连接控制按钮状态 ===
    ui->connectButton->setEnabled(!isConnected);                 // 未连接时启用连接按钮
    ui->disconnectButton->setEnabled(isConnected);               // 已连接时启用断开按钮

    // === 数据发送相关按钮状态 ===
    ui->sendButton->setEnabled(isConnected);                     // 连接后才能发送数据
    ui->continuousSendButton->setEnabled(isConnected);           // 连接后才能连续发送

    // === 心跳按钮状态和样式更新 ===
    if (isHeartbeatRunning) {
        ui->heartbeatButton->setText("停止心跳包发送");           // 心跳运行时显示停止文本
        ui->heartbeatButton->setStyleSheet("QPushButton { background-color: #ff6b6b; color: white; font-weight: bold; padding: 8px; }");
    } else {
        ui->heartbeatButton->setText("发送心跳包");               // 心跳停止时显示发送文本
        ui->heartbeatButton->setStyleSheet("QPushButton { background-color: #FF9800; color: white; font-weight: bold; padding: 8px; }");
    }
    ui->heartbeatButton->setEnabled(isConnected);                // 连接后才能使用心跳功能

    // === 连接状态标签更新 ===
    if (isConnected) {
        ui->statusLabel->setText("已连接");                      // 显示已连接状态
        ui->statusLabel->setStyleSheet("color: green; font-weight: bold;");
    } else {
        ui->statusLabel->setText("未连接");                      // 显示未连接状态
        ui->statusLabel->setStyleSheet("color: red; font-weight: bold;");
    }
}

/**
 * @brief 连接设备槽函数实现
 * @details 执行CANFD设备连接操作：
 *          1. 检查ZLGCAN库是否加载
 *          2. 尝试打开ZLGCAN设备
 *          3. 初始化CANFD通道
 *          4. 启动通信服务
 *          5. 更新UI状态和显示连接结果
 */
void CANFDInterface::onConnectDevice()
{
    // === 第一步：检查并尝试加载ZLGCAN库 ===
    if (!zlgcanLib.isLoaded()) {
        // 尝试重新加载库
        if (!loadZLGCANLibrary()) {
            // 库加载失败，询问用户是否使用模拟模式
            QString message = "无法加载ZLGCAN库，无法连接真实设备！\n\n"
                             "可能原因：\n"
                             "• zlgcan.dll文件缺失或损坏\n"
                             "• 设备驱动未正确安装\n"
                             "• 系统缺少必要的运行库\n\n"
                             "是否使用模拟模式进行测试？";

            int ret = MessageBoxUtils::question(this, "设备连接", message);

            if (ret == QMessageBox::No) {
                appendLog("CANFD设备连接取消：用户选择不使用模拟模式", false);
                return;
            }

            // 用户选择使用模拟模式
            setupSimulationMode();
            isConnected = true;
            updateControlState();
            startCommunication();

            appendLog("CANFD设备连接成功 (模拟模式)", false);
            MessageBoxUtils::information(this, "提示", "CANFD设备连接成功！(模拟模式)");
            return;
        }
    }

    // === 第二步：获取ZLGCAN API函数指针 ===
    typedef DEVICE_HANDLE (*ZCAN_OpenDevice_Func)(UINT, UINT, UINT);
    typedef CHANNEL_HANDLE (*ZCAN_InitCAN_Func)(DEVICE_HANDLE, UINT, ZCAN_CHANNEL_INIT_CONFIG*);
    typedef UINT (*ZCAN_StartCAN_Func)(CHANNEL_HANDLE);

    ZCAN_OpenDevice_Func ZCAN_OpenDevice = (ZCAN_OpenDevice_Func)zlgcanLib.resolve("ZCAN_OpenDevice");
    ZCAN_InitCAN_Func ZCAN_InitCAN = (ZCAN_InitCAN_Func)zlgcanLib.resolve("ZCAN_InitCAN");
    ZCAN_StartCAN_Func ZCAN_StartCAN = (ZCAN_StartCAN_Func)zlgcanLib.resolve("ZCAN_StartCAN");

    if (!ZCAN_OpenDevice || !ZCAN_InitCAN || !ZCAN_StartCAN) {
        MessageBoxUtils::warning(this, "错误", "ZLGCAN API函数不完整，无法连接设备！");
        appendLog("CANFD设备连接失败：API函数不完整", false);
        return;
    }

    // === 第三步：打开ZLGCAN设备 ===
    deviceHandle = ZCAN_OpenDevice(ZCAN_USBCANFD_100U, 0, 0);  // 打开USBCANFD-100U设备
    if (deviceHandle == INVALID_DEVICE_HANDLE) {
        MessageBoxUtils::warning(this, "错误", "无法打开CANFD设备！\n请检查：\n1. 设备是否正确连接\n2. 驱动是否正确安装\n3. 设备是否被其他程序占用");
        appendLog("CANFD设备连接失败：无法打开设备", false);
        return;
    }

    // === 第四步：初始化CANFD通道 ===
    ZCAN_CHANNEL_INIT_CONFIG canfdConfig;
    memset(&canfdConfig, 0, sizeof(canfdConfig));

    // 设置CANFD配置参数（与官方上位机保持一致）
    canfdConfig.can_type = TYPE_CANFD;                    // 设置为CANFD类型
    canfdConfig.canfd.mode = 0;                           // 正常模式

    // 使用用户配置的CANFD波特率配置
    UINT arbitrationConfig = 0;
    UINT dataConfig = 0;

    // 检查是否使用自定义配置
    if (!configData.customBaud.isEmpty() && !configData.customBaud.contains("无自定义") && !configData.customBaud.contains("未设置")) {
        qDebug() << "使用自定义波特率配置:" << configData.customBaud;
        arbitrationConfig = getArbitrationBaudConfig(configData.customBaud);
        dataConfig = getDataBaudConfig(configData.customBaud);

        // 在状态栏显示当前使用的自定义配置
        ui->statusLabel->setText(QString("当前配置: %1").arg(configData.customBaud));
        ui->statusLabel->setStyleSheet("QLabel { color: #27ae60; font-weight: bold; }");
    } else {
        qDebug() << "使用标准波特率配置 - 仲裁段:" << configData.arbitrationBaud << "数据段:" << configData.dataBaud;
        arbitrationConfig = getArbitrationBaudConfig(configData.arbitrationBaud);
        dataConfig = getDataBaudConfig(configData.dataBaud);

        // 在状态栏显示当前使用的标准配置
        ui->statusLabel->setText(QString("当前配置: %1, %2").arg(configData.arbitrationBaud).arg(configData.dataBaud));
        ui->statusLabel->setStyleSheet("QLabel { color: #2980b9; font-weight: bold; }");
    }

    canfdConfig.canfd.abit_timing = arbitrationConfig;  // 仲裁域波特率
    canfdConfig.canfd.dbit_timing = dataConfig;         // 数据域波特率

    qDebug() << "CANFD配置参数 - abit_timing:" << QString("0x%1").arg(arbitrationConfig, 8, 16, QChar('0'))
             << "dbit_timing:" << QString("0x%1").arg(dataConfig, 8, 16, QChar('0'));

    // 接收滤波器配置（接收所有帧）
    canfdConfig.canfd.acc_code = 0x00000000;              // 验收代码（接收所有）
    canfdConfig.canfd.acc_mask = 0x00000000;              // 验收屏蔽码（不屏蔽任何帧）
    canfdConfig.canfd.filter = 0;                         // 禁用滤波器（接收所有帧）
    canfdConfig.canfd.brp = 0;                            // 波特率预分频器

    channelHandle = ZCAN_InitCAN(deviceHandle, 0, &canfdConfig);  // 初始化通道0
    if (channelHandle == INVALID_CHANNEL_HANDLE) {
        MessageBoxUtils::warning(this, "错误", "无法初始化CANFD通道！");
        appendLog("CANFD设备连接失败：无法初始化通道", false);

        // 关闭设备
        typedef UINT (*ZCAN_CloseDevice_Func)(DEVICE_HANDLE);
        ZCAN_CloseDevice_Func ZCAN_CloseDevice = (ZCAN_CloseDevice_Func)zlgcanLib.resolve("ZCAN_CloseDevice");
        if (ZCAN_CloseDevice) {
            ZCAN_CloseDevice(deviceHandle);
        }
        deviceHandle = INVALID_DEVICE_HANDLE;
        return;
    }

    // === 第五步：启动CAN通道 ===
    if (ZCAN_StartCAN(channelHandle) != STATUS_OK) {
        MessageBoxUtils::warning(this, "错误", "无法启动CANFD通道！");
        appendLog("CANFD设备连接失败：无法启动通道", false);

        // 清理资源
        typedef UINT (*ZCAN_CloseDevice_Func)(DEVICE_HANDLE);
        ZCAN_CloseDevice_Func ZCAN_CloseDevice = (ZCAN_CloseDevice_Func)zlgcanLib.resolve("ZCAN_CloseDevice");
        if (ZCAN_CloseDevice) {
            ZCAN_CloseDevice(deviceHandle);
        }
        deviceHandle = INVALID_DEVICE_HANDLE;
        channelHandle = INVALID_CHANNEL_HANDLE;
        return;
    }

    // === 第六步：连接成功，更新状态 ===
    isConnected = true;                                          // 设置连接状态为已连接
    updateControlState();                                        // 更新按钮和控件状态
    startCommunication();                                        // 启动接收定时器等通信服务

    // === 第七步：记录连接信息 ===
    appendLog("CANFD设备连接成功", false);
    appendLog(QString("设备类型: USBCANFD-100U"), false);
    appendLog(QString("仲裁域波特率: %1").arg(configData.arbitrationBaud), false);
    appendLog(QString("数据域波特率: %1").arg(configData.dataBaud), false);
    appendLog(QString("工作模式: %1").arg(configData.workMode), false);
    appendLog(QString("滤波器: 禁用 (接收所有帧)"), false);

    // === 第八步：检查初始通道状态 ===
    typedef UINT (*ZCAN_ReadChannelStatus_Func)(CHANNEL_HANDLE, ZCAN_CHANNEL_STATUS*);
    ZCAN_ReadChannelStatus_Func ZCAN_ReadChannelStatus = (ZCAN_ReadChannelStatus_Func)zlgcanLib.resolve("ZCAN_ReadChannelStatus");

    if (ZCAN_ReadChannelStatus) {
        ZCAN_CHANNEL_STATUS initialStatus;
        memset(&initialStatus, 0, sizeof(initialStatus));

        if (ZCAN_ReadChannelStatus(channelHandle, &initialStatus) == STATUS_OK) {
            appendLog(QString("通道初始状态: 模式=0x%1, 状态=0x%2")
                     .arg(initialStatus.regMode, 2, 16, QChar('0'))
                     .arg(initialStatus.regStatus, 2, 16, QChar('0')), false);
        }
    }

    // === 第八步：显示连接成功提示 ===
    MessageBoxUtils::information(this, "提示", "CANFD设备连接成功！");
}

/**
 * @brief 断开设备槽函数实现
 * @details 执行CANFD设备断开操作：
 *          1. 停止心跳包发送（如果正在运行）
 *          2. 停止通信服务
 *          3. 关闭ZLGCAN设备和通道
 *          4. 设置连接状态为未连接
 *          5. 更新UI控件状态并记录断开信息
 */
void CANFDInterface::onDisconnectDevice()
{
    // === 第一步：停止心跳包发送 ===
    if (isHeartbeatRunning) {
        onSendHeartbeat();                                       // 调用心跳函数停止心跳包发送
    }

    // === 第二步：停止通信服务 ===
    stopCommunication();                                         // 停止接收定时器等通信服务

    // === 第三步：关闭ZLGCAN设备和通道 ===
    if (zlgcanLib.isLoaded()) {
        // 获取ZLGCAN API函数指针
        typedef UINT (*ZCAN_ResetCAN_Func)(CHANNEL_HANDLE);
        typedef UINT (*ZCAN_CloseDevice_Func)(DEVICE_HANDLE);

        ZCAN_ResetCAN_Func ZCAN_ResetCAN = (ZCAN_ResetCAN_Func)zlgcanLib.resolve("ZCAN_ResetCAN");
        ZCAN_CloseDevice_Func ZCAN_CloseDevice = (ZCAN_CloseDevice_Func)zlgcanLib.resolve("ZCAN_CloseDevice");

        // 重置CAN通道
        if (ZCAN_ResetCAN && channelHandle != INVALID_CHANNEL_HANDLE) {
            ZCAN_ResetCAN(channelHandle);
            channelHandle = INVALID_CHANNEL_HANDLE;
        }

        // 关闭设备
        if (ZCAN_CloseDevice && deviceHandle != INVALID_DEVICE_HANDLE) {
            ZCAN_CloseDevice(deviceHandle);
            deviceHandle = INVALID_DEVICE_HANDLE;
        }
    }

    // === 第四步：设置连接状态 ===
    isConnected = false;                                         // 设置连接状态为未连接

    // === 第五步：更新UI控件状态 ===
    updateControlState();                                        // 更新按钮和控件状态

    // === 第六步：记录断开信息 ===
    appendLog("CANFD设备已断开连接", false);
    MessageBoxUtils::information(this, "提示", "CANFD设备已断开连接！");
}

/**
 * @brief 发送数据槽函数实现
 * @details 执行CANFD数据发送操作：
 *          1. 检查设备连接状态
 *          2. 获取和验证用户输入的数据
 *          3. 处理数据格式和长度
 *          4. 发送数据并记录日志
 */
void CANFDInterface::onSendData()
{
    // === 第一步：检查设备连接状态 ===
    if (!isConnected) {
        MessageBoxUtils::warning(this, "警告", "请先连接设备！");
        return;
    }

    // === 第二步：获取用户输入数据 ===
    QString canId = ui->canIdEdit->text().trimmed();             // 获取CAN ID
    QString data = ui->sendDataEdit->text().trimmed();           // 获取发送数据

    // === 第三步：验证CAN ID ===
    if (canId.isEmpty()) {
        MessageBoxUtils::warning(this, "警告", "请输入CAN ID！");
        return;
    }

    if (data.isEmpty()) {
        MessageBoxUtils::warning(this, "警告", "请输入发送数据！");
        return;
    }
    
    // 验证数据长度（CANFD最多64字节）
    QStringList dataBytes = data.split(' ', Qt::SkipEmptyParts);
    if (dataBytes.size() > 64) {
        MessageBoxUtils::warning(this, "警告", "CANFD协议最多支持64字节数据！");
        return;
    }
    
    // === 第四步：准备CANFD发送数据 ===
    bool ok;
    quint32 canIdValue = canId.toUInt(&ok, 16);
    if (!ok) {
        MessageBoxUtils::warning(this, "警告", "CAN ID格式错误！请输入十六进制数值。");
        return;
    }

    // 获取FD模式信息
    QString fdMode = ui->fdModeComboBox->currentText();
    bool brsEnabled = ui->brsCheckBox->isChecked();
    bool esiEnabled = ui->esiCheckBox->isChecked();

    // === 第五步：真实发送CANFD数据 ===
    if (zlgcanLib.isLoaded() && channelHandle != INVALID_CHANNEL_HANDLE) {
        // 获取ZLGCAN发送函数
        typedef UINT (*ZCAN_TransmitFD_Func)(CHANNEL_HANDLE, ZCAN_TransmitFD_Data*, UINT);
        ZCAN_TransmitFD_Func ZCAN_TransmitFD = (ZCAN_TransmitFD_Func)zlgcanLib.resolve("ZCAN_TransmitFD");

        if (ZCAN_TransmitFD) {
            ZCAN_TransmitFD_Data fdMsg;
            memset(&fdMsg, 0, sizeof(fdMsg));

            // 设置CAN ID和帧格式
            fdMsg.frame.can_id = canIdValue;
            fdMsg.frame.len = dataBytes.size();           // CANFD使用len字段，不是can_dlc

            // 设置CANFD标志（使用flags字段，不是flag）
            fdMsg.frame.flags = 0;                        // 初始化标志位
            if (fdMode.contains("FD")) {
                // CANFD帧标志通过EDL位自动设置，这里设置BRS和ESI
                if (brsEnabled) {
                    fdMsg.frame.flags |= CANFD_BRS;       // BRS位速率切换
                }
                if (esiEnabled) {
                    fdMsg.frame.flags |= CANFD_ESI;       // ESI错误状态指示
                }
            }

            // 填充数据
            for (int i = 0; i < dataBytes.size() && i < 64; i++) {
                bool byteOk;
                fdMsg.frame.data[i] = dataBytes[i].toUInt(&byteOk, 16);
                if (!byteOk) {
                    MessageBoxUtils::warning(this, "警告", QString("数据字节格式错误: %1").arg(dataBytes[i]));
                    return;
                }
            }

            // 发送数据
            UINT result = ZCAN_TransmitFD(channelHandle, &fdMsg, 1);
            if (result == 1) {
                // 发送成功
                sendCount++;
                QString logText = QString("[%1] ID:0x%2 数据:%3 (%4字节) [发送-%5%6%7]")
                                 .arg(QTime::currentTime().toString("HH:mm:ss.zzz"))
                                 .arg(canIdValue, 0, 16, QChar('0')).toUpper()
                                 .arg(data.toUpper())
                                 .arg(dataBytes.size())
                                 .arg(fdMode)
                                 .arg(brsEnabled ? " BRS" : "")
                                 .arg(esiEnabled ? " ESI" : "");

                appendLog(logText, false);
            } else {
                appendLog("CANFD数据发送失败", false);
                MessageBoxUtils::warning(this, "错误", "CANFD数据发送失败！");
            }
        } else {
            appendLog("CANFD发送函数未找到", false);
        }
    } else {
        appendLog("设备未连接或通道无效", false);
    }
}

void CANFDInterface::onSendHeartbeat()
{
    if (!isConnected) {
        MessageBoxUtils::warning(this, "警告", "请先连接设备！");
        return;
    }
    
    if (isHeartbeatRunning) {
        // 停止心跳包
        if (heartbeatTimer->isActive()) {
            heartbeatTimer->stop();
        }
        isHeartbeatRunning = false;
        updateControlState();
        appendLog("停止发送心跳包", false);
    } else {
        // 开始心跳包
        isHeartbeatRunning = true;
        heartbeatTimer->setInterval(ui->sendFrequencySpinBox->value());
        heartbeatTimer->start();
        updateControlState();
        appendLog("开始发送心跳包", false);
    }
}

void CANFDInterface::onContinuousSend()
{
    if (!isConnected) {
        MessageBoxUtils::warning(this, "警告", "请先连接设备！");
        return;
    }

    if (isContinuousSending) {
        // 停止连续发送
        if (continuousSendTimer->isActive()) {
            continuousSendTimer->stop();
        }
        isContinuousSending = false;
        ui->continuousSendButton->setText("连续发送");
        ui->continuousSendButton->setStyleSheet("QPushButton { background-color: #4CAF50; color: white; font-weight: bold; padding: 8px; }");
        appendLog("停止连续发送", false);
    } else {
        // 开始连续发送
        isContinuousSending = true;
        ui->continuousSendButton->setText("停止发送");
        ui->continuousSendButton->setStyleSheet("QPushButton { background-color: #f44336; color: white; font-weight: bold; padding: 8px; }");

        // 设置发送间隔
        int interval = ui->sendFrequencySpinBox->value();
        continuousSendTimer->setInterval(interval);
        continuousSendTimer->start();

        appendLog(QString("开始连续发送，间隔: %1ms").arg(interval), false);
    }
}

void CANFDInterface::onContinuousSendTimer()
{
    if (isContinuousSending && isConnected) {
        // 执行发送数据的逻辑（复用onSendData的逻辑）
        QString canId = ui->canIdEdit->text().trimmed();
        QString data = ui->sendDataEdit->text().trimmed();

        if (canId.isEmpty()) {
            canId = "123"; // 默认ID
        }

        if (data.isEmpty()) {
            data = "00 11 22 33 44 55 66 77"; // 默认数据
        }

        // 验证数据格式
        QStringList dataList = data.split(' ', Qt::SkipEmptyParts);
        QByteArray dataBytes;

        for (const QString &byte : dataList) {
            bool ok;
            quint8 value = byte.toUInt(&ok, 16);
            if (!ok) {
                return; // 数据格式错误，跳过本次发送
            }
            dataBytes.append(value);
        }

        if (dataBytes.size() > 64) { // CANFD最大64字节
            return; // CANFD数据长度超限，跳过本次发送
        }

        // 模拟发送数据
        sendCount++;
        QString logText = QString("[%1] ID:%2 数据:%3 (%4字节) [连续]")
                         .arg(QTime::currentTime().toString("HH:mm:ss.zzz"))
                         .arg(canId.toUpper())
                         .arg(data.toUpper())
                         .arg(dataBytes.size());

        appendLog(logText, false);
    }
}

void CANFDInterface::onClearSendArea()
{
    ui->sendTextEdit->clear();
    sendCount = 0;
}

void CANFDInterface::onClearReceiveArea()
{
    ui->receiveTextEdit->clear();
    receiveCount = 0;
}

void CANFDInterface::onSaveData()
{
    QString fileName = QFileDialog::getSaveFileName(this, 
                                                   "保存CANFD通信数据", 
                                                   QString("CANFD_Data_%1.txt")
                                                   .arg(QDateTime::currentDateTime().toString("yyyyMMdd_HHmmss")),
                                                   "文本文件 (*.txt)");
    
    if (!fileName.isEmpty()) {
        QFile file(fileName);
        if (file.open(QIODevice::WriteOnly | QIODevice::Text)) {
            QTextStream out(&file);
            out << "=== CANFD通信数据记录 ===\n";
            out << QString("保存时间: %1\n").arg(QDateTime::currentDateTime().toString("yyyy-MM-dd HH:mm:ss"));
            out << QString("发送计数: %1\n").arg(sendCount);
            out << QString("接收计数: %1\n\n").arg(receiveCount);
            
            out << "=== 发送数据 ===\n";
            out << ui->sendTextEdit->toPlainText() << "\n\n";
            
            out << "=== 接收数据 ===\n";
            out << ui->receiveTextEdit->toPlainText() << "\n";
            
            MessageBoxUtils::information(this, "提示", "数据保存成功！");
        } else {
            MessageBoxUtils::warning(this, "错误", "无法保存文件！");
        }
    }
}

void CANFDInterface::onBackClicked()
{
    if (isConnected) {
        onDisconnectDevice();
    }
    emit backToProtocolSelect();
}

void CANFDInterface::onHeartbeatTimer()
{
    QString heartbeatData = "FF FF FF FF AA AA AA AA";
    QString canId = ui->canIdEdit->text().isEmpty() ? "123" : ui->canIdEdit->text();
    QString fdMode = ui->fdModeComboBox->currentText();
    
    sendCount++;
    QString logText = QString("[%1] ID:%2 数据:%3 [%4] (心跳包)")
                     .arg(QTime::currentTime().toString("HH:mm:ss.zzz"))
                     .arg(canId.toUpper())
                     .arg(heartbeatData)
                     .arg(fdMode);
    
    appendLog(logText, false);
}

void CANFDInterface::onReceiveTimer()
{
    // === 第一步：检查设备连接状态 ===
    if (!isConnected) {
        return;                                                   // 设备未连接，直接返回
    }

    // === 第二步：尝试从真实设备接收数据 ===
    if (zlgcanLib.isLoaded() && channelHandle != INVALID_CHANNEL_HANDLE) {
        // 获取ZLGCAN API函数指针
        typedef UINT (*ZCAN_ReceiveFD_Func)(CHANNEL_HANDLE, ZCAN_ReceiveFD_Data*, UINT, int);
        typedef UINT (*ZCAN_GetReceiveNum_Func)(CHANNEL_HANDLE, BYTE);
        typedef UINT (*ZCAN_ReadChannelStatus_Func)(CHANNEL_HANDLE, ZCAN_CHANNEL_STATUS*);

        ZCAN_ReceiveFD_Func ZCAN_ReceiveFD = (ZCAN_ReceiveFD_Func)zlgcanLib.resolve("ZCAN_ReceiveFD");
        ZCAN_GetReceiveNum_Func ZCAN_GetReceiveNum = (ZCAN_GetReceiveNum_Func)zlgcanLib.resolve("ZCAN_GetReceiveNum");
        ZCAN_ReadChannelStatus_Func ZCAN_ReadChannelStatus = (ZCAN_ReadChannelStatus_Func)zlgcanLib.resolve("ZCAN_ReadChannelStatus");

        // === 检查设备连接状态（降低检测频率避免干扰通信）===
        static int deviceCheckCounter = 0;
        deviceCheckCounter++;

        if (deviceCheckCounter >= 100 && ZCAN_ReadChannelStatus) { // 每10秒检查一次设备状态
            deviceCheckCounter = 0;
            ZCAN_CHANNEL_STATUS channelStatus;
            memset(&channelStatus, 0, sizeof(channelStatus));

            UINT statusResult = ZCAN_ReadChannelStatus(channelHandle, &channelStatus);
            if (statusResult != STATUS_OK) {
                // 检查是否真的是设备断开（而不是通信错误）
                static int errorCount = 0;
                errorCount++;

                if (errorCount >= 3) { // 连续3次错误才认为设备断开
                    appendLog("检测到设备连接异常，自动断开连接", false);
                    MessageBoxUtils::warning(this, "设备异常", "检测到CANFD设备连接异常！\n设备可能已被拔出或出现故障。");
                    onDisconnectDevice();  // 自动断开连接
                    return;
                } else {
                    appendLog(QString("设备状态检查警告 (%1/3)").arg(errorCount), false);
                }
            } else {
                static int errorCount = 0; // 重置错误计数
                errorCount = 0;
            }
        }

        if (ZCAN_ReceiveFD && ZCAN_GetReceiveNum) {
            // === 检查接收缓冲区中的数据数量 ===
            UINT receiveNum = ZCAN_GetReceiveNum(channelHandle, TYPE_CANFD);

            if (receiveNum > 0) {
                // === 接收数据帧 ===
                ZCAN_ReceiveFD_Data fdMsgs[10];                   // 一次最多接收10帧数据
                UINT actualReceived = ZCAN_ReceiveFD(channelHandle, fdMsgs, qMin(receiveNum, 10U), 0);

                // === 处理每一帧接收到的数据 ===
                for (UINT i = 0; i < actualReceived; i++) {
                    const ZCAN_ReceiveFD_Data& msg = fdMsgs[i];

                    // 格式化CAN ID（十六进制显示）
                    QString canId = QString("0x%1").arg(msg.frame.can_id, 0, 16).toUpper();

                    // 格式化数据字节（十六进制显示，空格分隔）
                    QString dataStr;
                    for (int j = 0; j < msg.frame.len && j < 64; j++) {
                        if (j > 0) dataStr += " ";
                        dataStr += QString("%1").arg(msg.frame.data[j], 2, 16, QChar('0')).toUpper();
                    }

                    // 判断是CANFD还是标准CAN
                    QString frameType;
                    if (msg.frame.len > 8) {                      // CANFD帧长度大于8字节
                        frameType = "CANFD";
                    } else {
                        frameType = "CAN";
                    }

                    // 创建接收日志文本
                    QString logText = QString("[%1] ID:%2 数据:%3 (%4字节) [接收-%5]")
                                     .arg(QTime::currentTime().toString("HH:mm:ss.zzz"))  // 时间戳
                                     .arg(canId)                                           // CAN ID
                                     .arg(dataStr)                                         // 数据内容
                                     .arg(msg.frame.len)                                   // 数据长度
                                     .arg(frameType);                                      // 帧类型

                    appendLog(logText, true);                     // 记录接收日志（true表示接收数据）
                    receiveCount++;                               // 增加接收计数
                }
            }
        }
    } else {
        // === 第三步：模拟接收数据（用于测试和演示） ===
        static int frameCounter = 0;                              // 静态帧计数器
        static int dataCounter = 0;                               // 静态数据计数器

        frameCounter++;
        dataCounter++;

        // 交替发送CANFD和CAN数据（1:1比例）
        if (frameCounter % 2 == 1) {
            // 发送64字节的CANFD数据
            QString simulatedData = QString("CA FD 56 78 00 00 01 %1 08 09 0A 0B 0C 0D 0E 0F "
                                           "10 11 12 13 14 15 16 17 18 19 1A 1B 1C 1D 1E 1F "
                                           "20 21 22 23 24 25 26 27 28 29 2A 2B 2C 2D 2E 2F "
                                           "30 31 32 33 34 35 36 37 38 39 3A 3B 3C 3D 3E %2")
                                   .arg(dataCounter % 256, 2, 16, QChar('0')).toUpper()
                                   .arg((dataCounter + 1) % 256, 2, 16, QChar('0')).toUpper();
            QString canId = "0x124";

            QString logText = QString("[%1] ID:%2 数据:%3 (64字节) [接收-CANFD]")
                             .arg(QTime::currentTime().toString("HH:mm:ss.zzz"))
                             .arg(canId)
                             .arg(simulatedData);

            appendLog(logText, true);                             // true表示接收数据
            receiveCount++;
        } else {
            // 发送8字节的标准CAN数据
            QString simulatedData = QString("12 34 56 78 00 00 01 %1")
                                   .arg(dataCounter % 256, 2, 16, QChar('0')).toUpper();
            QString canId = "0x123";

            QString logText = QString("[%1] ID:%2 数据:%3 (8字节) [接收-CAN]")
                             .arg(QTime::currentTime().toString("HH:mm:ss.zzz"))
                             .arg(canId)
                             .arg(simulatedData);

            appendLog(logText, true);                             // true表示接收数据
            receiveCount++;
        }
    }
}

int CANFDInterface::extractFrequencyValue(const QString &text)
{
    // 从文本中提取数值，例如 "1000 ms" -> 1000
    QRegularExpression regex(R"((\d+)\s*ms)");
    QRegularExpressionMatch match = regex.match(text);
    if (match.hasMatch()) {
        return match.captured(1).toInt();
    }

    // 如果没有匹配到，尝试直接转换为整数
    bool ok;
    int value = text.toInt(&ok);
    if (ok && value > 0) {
        return value;
    }

    // 默认返回1000ms
    return 1000;
}

void CANFDInterface::onSendFrequencyChanged()
{
    int frequency = ui->sendFrequencySpinBox->value();

    if (isHeartbeatRunning) {
        heartbeatTimer->setInterval(frequency);
    }

    if (isContinuousSending) {
        continuousSendTimer->setInterval(frequency);
    }
}

void CANFDInterface::onReceiveFrequencyChanged()
{
    int newInterval = ui->receiveFrequencySpinBox->value();
    receiveTimer->setInterval(newInterval);

    QString logText = QString("接收频率已调整为: %1ms").arg(newInterval);
    appendLog(logText, false);

    qDebug() << "CANFD接收频率调整为:" << newInterval << "ms";
}

void CANFDInterface::onSendFrequencyPresetChanged()
{
    QString selectedText = ui->sendFrequencyPresetCombo->currentText();

    // 如果选择的是"常用频率"，则不做任何操作
    if (selectedText == "常用频率") {
        return;
    }

    // 从选择的文本中提取频率值并设置到SpinBox
    int frequency = extractFrequencyValue(selectedText);
    if (frequency > 0) {
        ui->sendFrequencySpinBox->setValue(frequency);

        QString logText = QString("发送频率已设置为: %1ms").arg(frequency);
        appendLog(logText, false);

        qDebug() << "CANFD发送频率预设选择:" << frequency << "ms";
    }
}

void CANFDInterface::onReceiveFrequencyPresetChanged()
{
    QString selectedText = ui->receiveFrequencyPresetCombo->currentText();

    // 如果选择的是"常用频率"，则不做任何操作
    if (selectedText == "常用频率") {
        return;
    }

    // 从选择的文本中提取频率值并设置到SpinBox
    int frequency = extractFrequencyValue(selectedText);
    if (frequency > 0) {
        ui->receiveFrequencySpinBox->setValue(frequency);

        QString logText = QString("接收频率已设置为: %1ms").arg(frequency);
        appendLog(logText, false);

        qDebug() << "CANFD接收频率预设选择:" << frequency << "ms";
    }
}

void CANFDInterface::onFdModeChanged()
{
    // 根据FD模式调整界面
    int currentIndex = ui->fdModeComboBox->currentIndex();
    
    // 标准CAN模式时禁用BRS和ESI
    bool isFdMode = (currentIndex > 0);
    ui->brsCheckBox->setEnabled(isFdMode);
    ui->esiCheckBox->setEnabled(isFdMode);
    
    if (!isFdMode) {
        ui->brsCheckBox->setChecked(false);
        ui->esiCheckBox->setChecked(false);
    }
}

// appendLog和startCommunication函数已在BaseCANInterface基类中实现

// stopCommunication函数已在BaseCANInterface基类中实现

// loadZLGCANLibrary函数已在BaseCANInterface基类中实现

/**
 * @brief 获取仲裁域波特率配置值函数实现
 * @param baudText 波特率文本（如"500kbps 87.5%"）
 * @return UINT 对应的硬件配置值
 * @details 将用户选择的仲裁域波特率转换为ZLGCAN硬件配置值，支持自定义配置
 */
UINT CANFDInterface::getArbitrationBaudConfig(const QString &baudText)
{
    qDebug() << "解析仲裁域波特率配置:" << baudText;

    // 处理新的官方格式配置：500Kbps(75%),4.0Mbps(80%),(40,04C00001,00400002)
    if (baudText.contains("Kbps(") && baudText.contains("),(")) {
        // 解析格式：500Kbps(75%),4.0Mbps(80%),(40,04C00001,00400002)
        QStringList mainParts = baudText.split(",(");
        if (mainParts.size() >= 2) {
            QString arbPart = mainParts[0]; // "500Kbps(75%),4.0Mbps(80%)"
            QString configPart = mainParts[1]; // "40,04C00001,00400002)"

            // 提取仲裁段配置值
            QStringList configValues = configPart.replace(")", "").split(",");
            if (configValues.size() >= 2) {
                QString arbConfigHex = configValues[1].trimmed(); // "04C00001"
                bool ok;
                UINT arbConfig = arbConfigHex.toUInt(&ok, 16);
                if (ok) {
                    qDebug() << "从官方格式提取仲裁段配置: 0x" << QString::number(arbConfig, 16).toUpper();
                    return arbConfig;
                }
            }
        }
    }

    // 处理旧的自定义配置格式：自定义1 (500kbps 75.0%, 2.0Mbps 75.0%)
    if (baudText.contains("自定义") && baudText.contains("(") && baudText.contains(")")) {
        // 提取括号内的配置信息
        int startPos = baudText.indexOf("(") + 1;
        int endPos = baudText.indexOf(")");
        QString configStr = baudText.mid(startPos, endPos - startPos);

        // 分割仲裁段和数据段配置
        QStringList parts = configStr.split(",");
        if (!parts.isEmpty()) {
            QString arbConfig = parts[0].trimmed();
            qDebug() << "提取的仲裁段配置:" << arbConfig;

            // 解析仲裁段配置
            if (arbConfig.contains("500kbps") && arbConfig.contains("75")) {
                qDebug() << "应用自定义500kbps 75%配置: 0x40001C00";
                return 0x40001C00;  // 500kbps 75% - 自定义配置
            }
        }
    }

    // 标准预设配置
    if (baudText.contains("250kbps")) {
        if (baudText.contains("87.5%")) {
            return 0x00001E00;  // 250kbps 87.5%
        } else if (baudText.contains("75%")) {
            return 0x40001E00;  // 250kbps 75%
        }
    } else if (baudText.contains("500kbps")) {
        if (baudText.contains("87.5%")) {
            return 0x00001C00;  // 500kbps 87.5%
        } else if (baudText.contains("75%")) {
            qDebug() << "应用500kbps 75%配置: 0x40001C00";
            return 0x40001C00;  // 500kbps 75%
        }
    } else if (baudText.contains("1Mbps") || baudText.contains("1000kbps")) {
        if (baudText.contains("80%")) {
            return 0x00000E00;  // 1Mbps 80%
        } else if (baudText.contains("75%")) {
            return 0x40000E00;  // 1Mbps 75%
        }
    }

    // 默认返回500kbps 87.5%配置
    qDebug() << "使用默认500kbps 87.5%配置: 0x00001C00";
    return 0x00001C00;
}

/**
 * @brief 获取数据域波特率配置值函数实现
 * @param baudText 波特率文本（如"1Mbps 87.5%"）或自定义配置文本
 * @return UINT 对应的硬件配置值
 * @details 将用户选择的数据域波特率转换为ZLGCAN硬件配置值，支持自定义配置
 */
UINT CANFDInterface::getDataBaudConfig(const QString &baudText)
{
    qDebug() << "解析数据域波特率配置:" << baudText;

    // 处理新的官方格式配置：500Kbps(75%),4.0Mbps(80%),(40,04C00001,00400002)
    if (baudText.contains("Kbps(") && baudText.contains("),(")) {
        // 解析格式：500Kbps(75%),4.0Mbps(80%),(40,04C00001,00400002)
        QStringList mainParts = baudText.split(",(");
        if (mainParts.size() >= 2) {
            QString configPart = mainParts[1]; // "40,04C00001,00400002)"

            // 提取数据段配置值
            QStringList configValues = configPart.replace(")", "").split(",");
            if (configValues.size() >= 3) {
                QString dataConfigHex = configValues[2].trimmed(); // "00400002"
                bool ok;
                UINT dataConfig = dataConfigHex.toUInt(&ok, 16);
                if (ok) {
                    qDebug() << "从官方格式提取数据段配置: 0x" << QString::number(dataConfig, 16).toUpper();
                    return dataConfig;
                }
            }
        }
    }

    // 处理旧的自定义配置格式：自定义1 (500kbps 75.0%, 2.0Mbps 75.0%)
    if (baudText.contains("自定义") && baudText.contains("(") && baudText.contains(")")) {
        // 提取括号内的配置信息
        int startPos = baudText.indexOf("(") + 1;
        int endPos = baudText.indexOf(")");
        QString configStr = baudText.mid(startPos, endPos - startPos);

        // 分割仲裁段和数据段配置
        QStringList parts = configStr.split(",");
        if (parts.size() >= 2) {
            QString dataConfig = parts[1].trimmed();
            qDebug() << "提取的数据段配置:" << dataConfig;

            // 解析数据段配置
            if (dataConfig.contains("2.0Mbps") && dataConfig.contains("75")) {
                qDebug() << "应用自定义2.0Mbps 75%配置: 0x0002040D";
                return 0x0002040D;  // 2.0Mbps 75% - 根据您的目标配置
            }
        }
    }

    // 标准预设配置
    if (baudText.contains("1Mbps")) {
        if (baudText.contains("87.5%")) {
            return 0x00000E00;  // 1Mbps 87.5%
        } else if (baudText.contains("80%")) {
            return 0x00000F00;  // 1Mbps 80%
        } else if (baudText.contains("75%")) {
            return 0x40000E00;  // 1Mbps 75%
        }
    } else if (baudText.contains("2Mbps") || baudText.contains("2.0Mbps")) {
        if (baudText.contains("87.5%")) {
            return 0x00000D00;  // 2Mbps 87.5%
        } else if (baudText.contains("80%")) {
            return 0x00000C00;  // 2Mbps 80%
        } else if (baudText.contains("75%")) {
            qDebug() << "应用2.0Mbps 75%配置: 0x0002040D";
            return 0x0002040D;  // 2.0Mbps 75% - 根据您的目标配置
        }
    } else if (baudText.contains("4Mbps")) {
        return 0x00000A00;  // 4Mbps 75%
    }

    // 默认返回2Mbps 80%配置
    qDebug() << "使用默认2Mbps 80%配置: 0x00000C00";
    return 0x00000C00;
}

/**
 * @brief 获取目标文本编辑器
 * @param isReceived 是否为接收数据
 * @return 对应的文本编辑器指针
 */
QTextEdit* CANFDInterface::getTargetTextEdit(bool isReceived)
{
    return isReceived ? ui->receiveTextEdit : ui->sendTextEdit;
}

/**
 * @brief 尝试连接真实设备
 * @return 是否连接成功
 */
bool CANFDInterface::tryConnectRealDevice()
{
    // 这里实现真实设备连接逻辑
    // 暂时返回false，使用模拟模式
    return false;
}

// updateControlState函数已在文件前面定义

/**
 * @brief 启动通信（重写基类方法）
 */
void CANFDInterface::startCommunication()
{
    BaseCANInterface::startCommunication();
    onFdModeChanged(); // 初始化FD模式状态
}

/**
 * @brief 停止通信（重写基类方法）
 */
void CANFDInterface::stopCommunication()
{
    BaseCANInterface::stopCommunication();
}

/**
 * @brief 添加日志（重写基类方法）
 */
void CANFDInterface::appendLog(const QString &text, bool isReceived)
{
    BaseCANInterface::appendLog(text, isReceived);
}
