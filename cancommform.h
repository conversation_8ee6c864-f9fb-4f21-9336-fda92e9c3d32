/**
 * @file cancommform.h
 * @brief CAN通信表单类头文件
 * @details 定义了CAN通信表单类CANCommForm，提供CAN协议通信的用户界面和功能，
 *          包括设备连接、数据收发、心跳功能、数据保存等。该类使用代码方式
 *          创建UI界面，不依赖Qt Designer的.ui文件。
 * <AUTHOR>
 * @date 2025-07-02
 * @version 1.0
 */

#ifndef CANCOMMFORM_H
#define CANCOMMFORM_H

#include <QWidget>          // Qt窗口基类
#include <QVBoxLayout>      // Qt垂直布局管理器
#include <QHBoxLayout>      // Qt水平布局管理器
#include <QGridLayout>      // Qt网格布局管理器
#include <QGroupBox>        // Qt分组框控件
#include <QLabel>           // Qt标签控件
#include <QLineEdit>        // Qt单行文本编辑控件
#include <QTextEdit>        // Qt多行文本编辑控件
#include <QPushButton>      // Qt按钮控件
#include <QComboBox>        // Qt下拉框控件
#include <QSpinBox>         // Qt数值输入框控件
#include <QCheckBox>        // Qt复选框控件
#include <QTimer>           // Qt定时器类
#include <QTime>            // Qt时间类
#include <QFileDialog>      // Qt文件对话框类
#include <QMessageBox>      // Qt消息框类
#include <QSplitter>        // Qt分割器控件
#include <QTextCursor>      // Qt文本光标类
#include "canconfig.h"      // CAN配置数据结构

// Qt命名空间标记
QT_BEGIN_NAMESPACE
QT_END_NAMESPACE

/**
 * @class CANCommForm
 * @brief CAN通信表单类
 * @details 继承自QWidget，提供CAN协议通信的完整用户界面和功能。
 *          该类使用代码方式创建UI界面，包括设备连接管理、数据收发、
 *          心跳功能、频率控制、数据保存等功能。
 */
class CANCommForm : public QWidget
{
    Q_OBJECT  // Qt元对象系统宏，支持信号槽机制

public:
    /**
     * @brief 构造函数
     * @param parent 父窗口指针，默认为nullptr
     * @details 初始化CAN通信表单，创建UI界面和设置各种功能
     */
    CANCommForm(QWidget *parent = nullptr);

    /**
     * @brief 析构函数
     * @details 清理资源，断开设备连接，释放内存
     */
    ~CANCommForm();

    /**
     * @brief 设置配置数据
     * @param config CAN配置数据结构
     * @details 应用用户配置的CAN参数到通信表单
     */
    void setConfigData(const CANConfigData &config);

signals:
    /**
     * @brief 返回协议选择界面信号
     * @details 当用户点击返回按钮时发出此信号
     */
    void backToProtocolSelect();

private slots:
    /**
     * @brief 连接设备槽函数
     * @details 响应连接按钮点击，初始化并连接CAN设备
     */
    void onConnectDevice();

    /**
     * @brief 断开设备槽函数
     * @details 响应断开按钮点击，断开CAN设备连接
     */
    void onDisconnectDevice();

    /**
     * @brief 发送数据槽函数
     * @details 响应发送按钮点击，发送单次CAN数据帧
     */
    void onSendData();

    /**
     * @brief 发送心跳槽函数
     * @details 响应心跳按钮点击，启动心跳发送
     */
    void onSendHeartbeat();

    /**
     * @brief 停止心跳槽函数
     * @details 停止心跳数据包发送
     */
    void onStopHeartbeat();

    /**
     * @brief 清空发送区域槽函数
     * @details 响应清空发送按钮点击，清空发送数据显示区域
     */
    void onClearSendArea();

    /**
     * @brief 清空接收区域槽函数
     * @details 响应清空接收按钮点击，清空接收数据显示区域
     */
    void onClearReceiveArea();

    /**
     * @brief 保存数据槽函数
     * @details 响应保存按钮点击，将接收数据保存到文件
     */
    void onSaveData();

    /**
     * @brief 返回按钮点击槽函数
     * @details 响应返回按钮点击，发出返回信号
     */
    void onBackClicked();

    /**
     * @brief 心跳定时器槽函数
     * @details 心跳定时器超时时调用，发送心跳数据帧
     */
    void onHeartbeatTimer();

    /**
     * @brief 接收定时器槽函数
     * @details 接收定时器超时时调用，检查并处理接收到的数据
     */
    void onReceiveTimer();

    /**
     * @brief 发送频率改变槽函数
     * @details 响应发送频率SpinBox值变化，更新发送频率
     */
    void onSendFrequencyChanged();

private:
    /**
     * @brief 设置用户界面
     * @details 创建并布局所有UI控件
     */
    void setupUI();

    /**
     * @brief 设置信号槽连接
     * @details 连接UI控件的信号到相应的槽函数
     */
    void setupConnections();

    /**
     * @brief 更新控件状态
     * @details 根据连接状态更新UI控件的启用/禁用状态
     */
    void updateControlState();

    /**
     * @brief 添加日志记录
     * @param text 日志文本内容
     * @param isReceived 是否为接收数据（true为接收，false为发送）
     * @details 在相应的文本区域添加带时间戳的日志记录
     */
    void appendLog(const QString &text, bool isReceived);

    /**
     * @brief 启动通信
     * @details 启动接收定时器，开始数据通信
     */
    void startCommunication();

    /**
     * @brief 停止通信
     * @details 停止所有定时器，结束数据通信
     */
    void stopCommunication();

    // === UI控件成员变量 ===
    // 主要布局管理器
    /**
     * @brief 主垂直布局
     * @details 整个窗口的主要布局管理器
     */
    QVBoxLayout *mainLayout;

    /**
     * @brief 顶部水平布局
     * @details 控制区域的布局管理器
     */
    QHBoxLayout *topLayout;

    /**
     * @brief 底部水平布局
     * @details 按钮区域的布局管理器
     */
    QHBoxLayout *bottomLayout;

    // === 顶部控制区控件 ===
    /**
     * @brief 控制分组框
     * @details 包含所有控制控件的分组框
     */
    QGroupBox *controlGroup;

    /**
     * @brief 控制网格布局
     * @details 控制区域内的网格布局管理器
     */
    QGridLayout *controlLayout;

    /**
     * @brief 协议标签
     * @details 显示当前协议类型的标签
     */
    QLabel *protocolLabel;

    /**
     * @brief 状态标签
     * @details 显示设备连接状态的标签
     */
    QLabel *statusLabel;

    /**
     * @brief CAN ID输入框
     * @details 用于输入CAN帧ID的文本框
     */
    QLineEdit *canIdEdit;

    /**
     * @brief 发送数据输入框
     * @details 用于输入发送数据的文本框
     */
    QLineEdit *sendDataEdit;

    /**
     * @brief 发送频率输入框
     * @details 用于设置发送频率的数值输入框
     */
    QSpinBox *sendFrequencySpinBox;

    /**
     * @brief 连接按钮
     * @details 用于连接CAN设备的按钮
     */
    QPushButton *connectButton;

    /**
     * @brief 断开按钮
     * @details 用于断开CAN设备连接的按钮
     */
    QPushButton *disconnectButton;

    /**
     * @brief 发送按钮
     * @details 用于发送单次数据的按钮
     */
    QPushButton *sendButton;

    /**
     * @brief 心跳按钮
     * @details 用于启动/停止心跳发送的按钮 
     */
    QPushButton *heartbeatButton;

    /**
     * @brief 返回按钮
     * @details 用于返回协议选择界面的按钮
     */
    QPushButton *backButton;

    // === 数据显示区控件 ===
    /**
     * @brief 数据分割器
     * @details 分割发送和接收数据显示区域的控件    
     */
    QSplitter *dataSplitter;

    /**
     * @brief 发送数据分组框
     * @details 包含发送数据显示区域的分组框
     */
    QGroupBox *sendGroup;

    /**
     * @brief 接收数据分组框
     * @details 包含接收数据显示区域的分组框
     */
    QGroupBox *receiveGroup;

    /**
     * @brief 发送数据文本编辑器
     * @details 显示发送数据日志的多行文本编辑器
     */
    QTextEdit *sendTextEdit;

    /**
     * @brief 接收数据文本编辑器
     * @details 显示接收数据日志的多行文本编辑器
     */
    QTextEdit *receiveTextEdit;

    // === 底部按钮区控件 ===
    /**
     * @brief 清空发送按钮
     * @details 用于清空发送数据显示区域的按钮
     */ 
    QPushButton *clearSendButton;

    /**
     * @brief 清空接收按钮
     * @details 用于清空接收数据显示区域的按钮
     */
    QPushButton *clearReceiveButton;

    /**
     * @brief 保存数据按钮
     * @details 用于保存接收数据到文件的按钮
     */
    QPushButton *saveDataButton;

    // === 定时器成员变量 ===
    /**
     * @brief 心跳定时器
     * @details 用于定时发送心跳数据帧
     */
    QTimer *heartbeatTimer;

    /**
     * @brief 接收定时器
     * @details 用于定时检查和处理接收到的数据
     */
    QTimer *receiveTimer;

    /**
     * @brief 发送定时器
     * @details 用于定时发送数据
     */
    QTimer *sendTimer;

    // === 状态变量成员 ===
    /**
     * @brief 设备连接状态标志
     * @details true表示设备已连接，false表示设备未连接
     */
    bool isConnected;

    /**
     * @brief 心跳运行状态标志
     * @details true表示心跳功能正在运行，false表示心跳功能已停止
     */
    bool isHeartbeatRunning;

    /**
     * @brief CAN配置数据
     * @details 存储用户配置的CAN通信参数，包括波特率、工作模式等
     */
    CANConfigData configData;

    // === 统计信息成员 ===
    /**
     * @brief 发送数据帧计数器
     * @details 记录已发送的CAN数据帧数量
     */
    int sendCount;

    /**
     * @brief 接收数据帧计数器
     * @details 记录已接收的CAN数据帧数量
     */
    int receiveCount;
};

#endif // CANCOMMFORM_H
