<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>CANConfigNew</class>
 <widget class="QDialog" name="CANConfigNew">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>800</width>
    <height>700</height>
   </rect>
  </property>
  <property name="minimumSize">
   <size>
    <width>800</width>
    <height>700</height>
   </size>
  </property>
  <property name="windowTitle">
   <string>CAN/CANFD配置 - 采样点设置</string>
  </property>
  <property name="styleSheet">
   <string>QDialog {
    background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                               stop: 0 #f8f9fa, stop: 1 #e9ecef);
    color: #333333;
    font-family: 'Microsoft YaHei UI', 'Segoe UI', Arial, sans-serif;
}

QGroupBox {
    font-weight: bold;
    font-size: 14px;
    border: 2px solid #dc3545;
    border-radius: 8px;
    margin-top: 1ex;
    padding-top: 15px;
    padding-left: 15px;
    padding-right: 15px;
    padding-bottom: 10px;
    background-color: rgba(255, 255, 255, 0.9);
    margin-bottom: 10px;
}

QGroupBox::title {
    subcontrol-origin: margin;
    left: 10px;
    padding: 5px 15px 5px 15px;
    background: #dc3545;
    border-radius: 5px;
    color: white;
    font-size: 14px;
    font-weight: bold;
}

QLabel {
    color: #333333;
    font-size: 13px;
    font-weight: 500;
    padding: 5px;
    min-height: 25px;
}

QComboBox {
    border: 1px solid #ced4da;
    border-radius: 4px;
    padding: 8px 12px;
    background-color: #ffffff;
    color: #333333;
    font-size: 13px;
    min-height: 25px;
    selection-background-color: #dc3545;
}

QComboBox:hover {
    border-color: #dc3545;
}

QComboBox:focus {
    border: 2px solid #dc3545;
    outline: none;
}

QComboBox:focus:hover {
    border: 2px solid #dc3545;
}

QComboBox::drop-down {
    border: none;
    width: 20px;
}

QComboBox::down-arrow {
    image: none;
    border-left: 4px solid transparent;
    border-right: 4px solid transparent;
    border-top: 6px solid #666666;
}

QComboBox QAbstractItemView {
    border: 1px solid #ced4da;
    background-color: #ffffff;
    selection-background-color: #dc3545;
    selection-color: #ffffff;
}

QCheckBox {
    color: #333333;
    font-size: 13px;
    spacing: 5px;
}

QCheckBox::indicator {
    width: 16px;
    height: 16px;
    border: 1px solid #ced4da;
    border-radius: 3px;
    background-color: #ffffff;
}

QCheckBox::indicator:checked {
    background-color: #dc3545;
    border-color: #dc3545;
}

QPushButton {
    background-color: #ffffff;
    color: #dc3545;
    border: 1px solid #dc3545;
    padding: 8px 16px;
    font-size: 13px;
    font-weight: 500;
    border-radius: 4px;
    min-width: 80px;
    min-height: 32px;
}

QPushButton:hover {
    background-color: #dc3545;
    color: white;
}

QPushButton:pressed {
    background-color: #c82333;
    color: white;
}</string>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout">
   <property name="spacing">
    <number>15</number>
   </property>
   <property name="leftMargin">
    <number>20</number>
   </property>
   <property name="topMargin">
    <number>20</number>
   </property>
   <property name="rightMargin">
    <number>20</number>
   </property>
   <property name="bottomMargin">
    <number>20</number>
   </property>
   <item>
    <widget class="QGroupBox" name="protocolGroup">
     <property name="title">
      <string>协议配置</string>
     </property>
     <layout class="QGridLayout" name="protocolGridLayout">
      <property name="horizontalSpacing">
       <number>15</number>
      </property>
      <property name="verticalSpacing">
       <number>10</number>
      </property>
      <item row="0" column="0">
       <widget class="QLabel" name="protocolLabel">
        <property name="text">
         <string>协议类型:</string>
        </property>
       </widget>
      </item>
      <item row="0" column="1">
       <widget class="QComboBox" name="protocolComboBox">
        <item>
         <property name="text">
          <string>CAN</string>
         </property>
        </item>
        <item>
         <property name="text">
          <string>CANFD</string>
         </property>
        </item>
       </widget>
      </item>
      <item row="0" column="2">
       <widget class="QLabel" name="deviceIndexLabel">
        <property name="text">
         <string>设备索引:</string>
        </property>
       </widget>
      </item>
      <item row="0" column="3">
       <widget class="QComboBox" name="deviceIndexComboBox">
        <item>
         <property name="text">
          <string>0</string>
         </property>
        </item>
        <item>
         <property name="text">
          <string>1</string>
         </property>
        </item>
        <item>
         <property name="text">
          <string>2</string>
         </property>
        </item>
        <item>
         <property name="text">
          <string>3</string>
         </property>
        </item>
       </widget>
      </item>
      <item row="1" column="0">
       <widget class="QLabel" name="channelLabel">
        <property name="text">
         <string>通道:</string>
        </property>
       </widget>
      </item>
      <item row="1" column="1">
       <widget class="QComboBox" name="channelComboBox">
        <item>
         <property name="text">
          <string>0</string>
         </property>
        </item>
        <item>
         <property name="text">
          <string>1</string>
         </property>
        </item>
       </widget>
      </item>
     </layout>
    </widget>
   </item>
   <item>
    <widget class="QGroupBox" name="baudrateGroup">
     <property name="title">
      <string>波特率配置</string>
     </property>
     <layout class="QGridLayout" name="baudrateGridLayout">
      <property name="horizontalSpacing">
       <number>15</number>
      </property>
      <property name="verticalSpacing">
       <number>10</number>
      </property>
      <item row="0" column="0">
       <widget class="QLabel" name="standardTypeLabel">
        <property name="text">
         <string>标准类型:</string>
        </property>
       </widget>
      </item>
      <item row="0" column="1">
       <widget class="QComboBox" name="standardTypeComboBox">
        <item>
         <property name="text">
          <string>标准</string>
         </property>
        </item>
        <item>
         <property name="text">
          <string>加速</string>
         </property>
        </item>
       </widget>
      </item>
      <item row="1" column="0">
       <widget class="QLabel" name="arbitrationBaudLabel">
        <property name="text">
         <string>仲裁段:</string>
        </property>
       </widget>
      </item>
      <item row="1" column="1">
       <widget class="QComboBox" name="arbitrationBaudComboBox">
        <property name="currentText">
         <string>500kbps 87.5%</string>
        </property>
        <item>
         <property name="text">
          <string>250kbps 87.5%</string>
         </property>
        </item>
        <item>
         <property name="text">
          <string>500kbps 87.5%</string>
         </property>
        </item>
        <item>
         <property name="text">
          <string>1Mbps 80%</string>
         </property>
        </item>
       </widget>
      </item>
      <item row="1" column="2">
       <widget class="QLabel" name="dataBaudLabel">
        <property name="text">
         <string>数据段:</string>
        </property>
       </widget>
      </item>
      <item row="1" column="3">
       <widget class="QComboBox" name="dataBaudComboBox">
        <property name="currentText">
         <string>2Mbps 80%</string>
        </property>
        <item>
         <property name="text">
          <string>1Mbps 87.5%</string>
         </property>
        </item>
        <item>
         <property name="text">
          <string>2Mbps 80%</string>
         </property>
        </item>
        <item>
         <property name="text">
          <string>4Mbps 75%</string>
         </property>
        </item>
        <item>
         <property name="text">
          <string>5Mbps 75%</string>
         </property>
        </item>
       </widget>
      </item>
      <item row="2" column="0">
       <widget class="QLabel" name="customBaudLabel">
        <property name="text">
         <string>自定义:</string>
        </property>
       </widget>
      </item>
      <item row="2" column="1">
       <widget class="QLineEdit" name="customBaudLineEdit">
        <property name="placeholderText">
         <string>输入自定义配置，如：500Kbps(75%),4.0Mbps(80%),(40,04C00001,00400002)</string>
        </property>
       </widget>
      </item>
     </layout>
    </widget>
   </item>
   <item>
    <widget class="QGroupBox" name="workModeGroup">
     <property name="title">
      <string>工作模式配置</string>
     </property>
     <layout class="QGridLayout" name="workModeGridLayout">
      <property name="horizontalSpacing">
       <number>15</number>
      </property>
      <property name="verticalSpacing">
       <number>10</number>
      </property>
      <item row="0" column="0">
       <widget class="QLabel" name="workModeLabel">
        <property name="text">
         <string>工作模式:</string>
        </property>
       </widget>
      </item>
      <item row="0" column="1">
       <widget class="QComboBox" name="workModeComboBox">
        <item>
         <property name="text">
          <string>正常模式</string>
         </property>
        </item>
        <item>
         <property name="text">
          <string>只听模式</string>
         </property>
        </item>
        <item>
         <property name="text">
          <string>自测模式</string>
         </property>
        </item>
        <item>
         <property name="text">
          <string>单次模式</string>
         </property>
        </item>
       </widget>
      </item>
      <item row="0" column="2">
       <widget class="QLabel" name="resistanceLabel">
        <property name="text">
         <string>终端电阻:</string>
        </property>
       </widget>
      </item>
      <item row="0" column="3">
       <widget class="QComboBox" name="resistanceComboBox">
        <item>
         <property name="text">
          <string>禁用</string>
         </property>
        </item>
        <item>
         <property name="text">
          <string>使能</string>
         </property>
        </item>
       </widget>
      </item>
     </layout>
    </widget>
   </item>
   <item>
    <widget class="QGroupBox" name="filterGroup">
     <property name="title">
      <string>过滤器配置</string>
     </property>
     <layout class="QGridLayout" name="filterGridLayout">
      <property name="horizontalSpacing">
       <number>15</number>
      </property>
      <property name="verticalSpacing">
       <number>10</number>
      </property>
      <item row="0" column="0">
       <widget class="QLabel" name="filterModeLabel">
        <property name="text">
         <string>过滤模式:</string>
        </property>
       </widget>
      </item>
      <item row="0" column="1">
       <widget class="QComboBox" name="filterModeComboBox">
        <item>
         <property name="text">
          <string>双过滤器模式</string>
         </property>
        </item>
        <item>
         <property name="text">
          <string>单过滤器模式</string>
         </property>
        </item>
       </widget>
      </item>
      <item row="0" column="2">
       <widget class="QLabel" name="filterTypeLabel">
        <property name="text">
         <string>过滤类型:</string>
        </property>
       </widget>
      </item>
      <item row="0" column="3">
       <widget class="QComboBox" name="filterTypeComboBox">
        <item>
         <property name="text">
          <string>标准帧</string>
         </property>
        </item>
        <item>
         <property name="text">
          <string>扩展帧</string>
         </property>
        </item>
        <item>
         <property name="text">
          <string>标准+扩展帧</string>
         </property>
        </item>
       </widget>
      </item>
     </layout>
    </widget>
   </item>
   <item>
    <widget class="QGroupBox" name="advancedGroup">
     <property name="title">
      <string>高级配置</string>
     </property>
     <layout class="QGridLayout" name="advancedGridLayout">
      <property name="horizontalSpacing">
       <number>15</number>
      </property>
      <property name="verticalSpacing">
       <number>10</number>
      </property>
      <item row="0" column="0">
       <widget class="QLabel" name="busUtilizationLabel">
        <property name="text">
         <string>总线利用率:</string>
        </property>
       </widget>
      </item>
      <item row="0" column="1">
       <widget class="QComboBox" name="busUtilizationComboBox">
        <item>
         <property name="text">
          <string>禁用</string>
         </property>
        </item>
        <item>
         <property name="text">
          <string>使能</string>
         </property>
        </item>
       </widget>
      </item>
      <item row="0" column="2">
       <widget class="QCheckBox" name="clearBufferCheckBox">
        <property name="text">
         <string>清除缓冲区</string>
        </property>
       </widget>
      </item>
     </layout>
    </widget>
   </item>
   <item>
    <spacer name="verticalSpacer">
     <property name="orientation">
      <enum>Qt::Vertical</enum>
     </property>
     <property name="sizeHint" stdset="0">
      <size>
       <width>20</width>
       <height>20</height>
      </size>
     </property>
    </spacer>
   </item>
   <item>
    <layout class="QHBoxLayout" name="buttonLayout">
     <property name="spacing">
      <number>15</number>
     </property>
     <item>
      <widget class="QPushButton" name="baudrateCalcButton">
       <property name="text">
        <string>波特率计算器</string>
       </property>
       <property name="toolTip">
        <string>打开波特率计算器，精确配置采样点参数</string>
       </property>
      </widget>
     </item>
     <item>
      <spacer name="horizontalSpacer">
       <property name="orientation">
        <enum>Qt::Horizontal</enum>
       </property>
       <property name="sizeHint" stdset="0">
        <size>
         <width>40</width>
         <height>20</height>
        </size>
       </property>
      </spacer>
     </item>
     <item>
      <widget class="QPushButton" name="confirmButton">
       <property name="text">
        <string>确定</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QPushButton" name="cancelButton">
       <property name="text">
        <string>取消</string>
       </property>
      </widget>
     </item>
    </layout>
   </item>
  </layout>
 </widget>
 <resources/>
 <connections/>
</ui>
