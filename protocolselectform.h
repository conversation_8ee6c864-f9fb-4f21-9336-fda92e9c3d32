/**
 * @file protocolselectform.h
 * @brief 协议选择窗口类头文件
 * @details 定义了协议选择窗口类ProtocolSelectForm，提供用户选择通信协议的界面。
 *          支持Modbus和CAN/CANFD两种协议类型的选择，并管理相应的子窗口显示。
 * <AUTHOR>
 * @date 2025-07-02
 * @version 1.0
 */

#ifndef PROTOCOLSELECTFORM_H
#define PROTOCOLSELECTFORM_H

#include <QWidget>              // Qt窗口基类
#include "modbusform.h"         // Modbus协议窗口类
#include "canform.h"            // CAN协议窗口类
#include "canconfig.h"          // CAN配置对话框类（旧版本）
#include "canconfig_new.h"      // CAN配置对话框类（UI版本）
#include "caninterface.h"       // CAN通信接口类
#include "canfdinterface.h"     // CANFD通信接口类

// Qt命名空间
namespace Ui {
    class ProtocolSelectForm;   // UI类前向声明
}

/**
 * @class ProtocolSelectForm
 * @brief 协议选择窗口类
 * @details 继承自QWidget，提供协议选择的用户界面。
 *          该类管理不同通信协议的选择和相应子窗口的创建：
 *          1. Modbus协议选择和窗口管理
 *          2. CAN/CANFD协议选择和窗口管理
 *          3. 协议配置对话框的显示和处理
 *          4. 各个子窗口之间的切换和通信
 */
class ProtocolSelectForm : public QWidget
{
    Q_OBJECT  // Qt元对象系统宏，支持信号槽机制

public:
    /**
     * @brief 构造函数
     * @param parent 父窗口指针，默认为nullptr
     * @details 初始化协议选择窗口，设置UI界面，初始化子窗口指针
     */
    explicit ProtocolSelectForm(QWidget *parent = nullptr);

    /**
     * @brief 析构函数
     * @details 清理资源，释放所有子窗口实例和UI对象
     */
    ~ProtocolSelectForm();

signals:
    /**
     * @brief 返回主界面信号
     * @details 当用户选择返回主界面时发出此信号，通知主窗口进行界面切换
     */
    void backToMain();

private slots:
    /**
     * @brief Modbus协议按钮点击槽函数
     * @details 响应用户点击Modbus协议按钮，创建并显示Modbus协议窗口
     */
    void on_modbusButton_clicked();

    /**
     * @brief CAN/CANFD协议按钮点击槽函数
     * @details 响应用户点击CAN/CANFD协议按钮，显示CAN配置对话框
     */
    void on_canButton_clicked();

    /**
     * @brief 返回按钮点击槽函数
     * @details 响应用户点击返回按钮，发出返回主界面信号
     */
    void on_backButton_clicked();

    /**
     * @brief Modbus窗口返回槽函数
     * @details 处理从Modbus窗口返回到协议选择界面的操作
     */
    void onModbusBack();

    /**
     * @brief CAN窗口返回槽函数
     * @details 处理从CAN窗口返回到协议选择界面的操作
     */
    void onCANBack();

    /**
     * @brief CAN配置确认槽函数
     * @param config CAN配置数据结构
     * @param protocol 协议类型字符串（"CAN"或"CANFD"）
     * @details 处理CAN配置对话框确认后的操作，根据协议类型创建相应的通信接口
     */
    void onConfigConfirmed(const CANConfigData &config, const QString &protocol);

private:
    // === 私有成员变量 ===
    /**
     * @brief UI界面指针
     * @details 指向由Qt Designer生成的UI类实例，包含所有界面控件
     */
    Ui::ProtocolSelectForm *ui;

    /**
     * @brief Modbus协议窗口指针
     * @details 指向Modbus协议通信窗口实例，用于Modbus协议的数据交互
     */
    ModbusForm *modbusForm;

    /**
     * @brief CAN协议窗口指针
     * @details 指向CAN协议通信窗口实例，用于CAN协议的数据交互
     */
    CANForm *canForm;

    /**
     * @brief CAN配置对话框指针（UI版本）
     * @details 指向CAN配置对话框实例，用于CAN/CANFD协议参数的设置
     */
    CANConfigNew *canConfigDialog;

    /**
     * @brief CAN通信接口指针
     * @details 指向CAN通信接口实例，处理CAN协议的具体通信功能
     */
    CANInterface *canInterface;

    /**
     * @brief CANFD通信接口指针
     * @details 指向CANFD通信接口实例，处理CANFD协议的具体通信功能
     */
    CANFDInterface *canfdInterface;
};

#endif // PROTOCOLSELECTFORM_H