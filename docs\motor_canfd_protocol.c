/**
 * @file motor_canfd_protocol.c
 * @brief 六电机控制系统CANFD协议实现文件 (下位机专用)
 * @details 实现了CANFD协议的核心功能，包括CRC校验、数据处理等
 * @version V1.0
 * @date 2025-01-28
 * <AUTHOR>
 */

#include "motor_canfd_protocol.h"
#include <string.h>

// ================================
// 私有函数声明
// ================================

static bool is_valid_motor_id(uint8_t motor_id);
static uint32_t get_system_tick(void);

// ================================
// CRC16校验实现
// ================================

/**
 * @brief 计算CRC16校验和
 * @param data 数据指针
 * @param length 数据长度
 * @return CRC16校验和
 */
uint16_t calculate_crc16(const uint8_t* data, uint16_t length)
{
    uint16_t crc = 0xFFFF;
    
    for (uint16_t i = 0; i < length; i++) {
        crc ^= data[i];
        for (uint8_t j = 0; j < 8; j++) {
            if (crc & 0x0001) {
                crc = (crc >> 1) ^ 0xA001;
            } else {
                crc = crc >> 1;
            }
        }
    }
    
    return crc;
}

/**
 * @brief 验证CRC16校验和
 * @param frame 数据帧指针
 * @param length 数据长度
 * @return 校验是否通过
 */
bool verify_crc16(const void* frame, uint16_t length)
{
    if (frame == NULL || length < 2) {
        return false;
    }
    
    const uint8_t* data = (const uint8_t*)frame;
    uint16_t received_crc = *(uint16_t*)(data + length - 4); // 校验和在倒数第4-3字节
    uint16_t calculated_crc = calculate_crc16(data, length - 4);
    
    return (received_crc == calculated_crc);
}

/**
 * @brief 填充控制帧校验和
 * @param frame 控制帧指针
 */
void fill_control_frame_checksum(MotorControlFrame* frame)
{
    if (frame == NULL) return;
    
    // 先清零校验和字段
    frame->checksum = 0;
    
    // 计算校验和(不包括校验和和时间戳字段)
    frame->checksum = calculate_crc16((uint8_t*)frame, sizeof(MotorControlFrame) - 4);
}

/**
 * @brief 填充状态帧校验和
 * @param frame 状态帧指针
 */
void fill_status_frame_checksum(MotorStatusFrame* frame)
{
    if (frame == NULL) return;
    
    // 先清零校验和字段
    frame->checksum = 0;
    
    // 计算校验和(不包括校验和和时间戳字段)
    frame->checksum = calculate_crc16((uint8_t*)frame, sizeof(MotorStatusFrame) - 4);
}

// ================================
// 时间戳处理
// ================================

/**
 * @brief 获取当前时间戳
 * @return 当前时间戳(毫秒)
 */
uint32_t get_current_timestamp(void)
{
    // 这里需要根据具体的硬件平台实现
    // 示例：使用系统滴答计数器
    return get_system_tick();
}

// ================================
// 协议管理器实现
// ================================

/**
 * @brief 初始化协议管理器
 * @param manager 协议管理器指针
 */
void init_protocol_manager(ProtocolManager* manager)
{
    if (manager == NULL) return;
    
    // 清零所有数据
    memset(manager, 0, sizeof(ProtocolManager));
    
    // 初始化系统启动时间
    manager->system_start_time = get_current_timestamp();
    
    // 初始化所有电机状态
    for (uint8_t i = 0; i < MOTOR_COUNT; i++) {
        manager->status[i].frame_type = FRAME_TYPE_STATUS;
        manager->status[i].motor_id = i + 1;
        manager->status[i].sequence = 0;
        manager->status[i].status_flags = 0;
        manager->status[i].error_code = ERR_NO_ERROR;
    }
}

/**
 * @brief 处理控制命令
 * @param manager 协议管理器指针
 * @param frame 控制命令帧指针
 * @return 处理是否成功
 */
bool process_control_command(ProtocolManager* manager, const MotorControlFrame* frame)
{
    if (manager == NULL || frame == NULL) {
        return false;
    }
    
    // 验证帧类型
    if (frame->frame_type != FRAME_TYPE_CONTROL) {
        return false;
    }
    
    // 验证电机ID
    if (!is_valid_motor_id(frame->motor_id)) {
        return false;
    }
    
    // 验证CRC校验和
    if (!verify_crc16(frame, sizeof(MotorControlFrame))) {
        return false;
    }
    
    uint8_t motor_index = frame->motor_id - 1;
    
    // 检查序列号，防止重复执行
    if (frame->sequence == manager->last_sequence[motor_index]) {
        return false; // 重复命令，忽略
    }
    
    // 更新最后处理的序列号和时间
    manager->last_sequence[motor_index] = frame->sequence;
    manager->last_command_time[motor_index] = get_current_timestamp();
    
    // 根据命令类型执行相应操作
    switch (frame->command) {
        case CMD_ENABLE:
            manager->status[motor_index].status_flags |= STATUS_ENABLED;
            break;
            
        case CMD_DISABLE:
            manager->status[motor_index].status_flags &= ~STATUS_ENABLED;
            manager->status[motor_index].status_flags &= ~STATUS_RUNNING;
            break;
            
        case CMD_START:
            if (manager->status[motor_index].status_flags & STATUS_ENABLED) {
                manager->status[motor_index].status_flags |= STATUS_RUNNING;
                manager->status[motor_index].status_flags |= STATUS_MOVING;
            }
            break;
            
        case CMD_STOP:
            manager->status[motor_index].status_flags &= ~STATUS_RUNNING;
            manager->status[motor_index].status_flags &= ~STATUS_MOVING;
            break;
            
        case CMD_RESET:
            // 复位电机状态
            manager->status[motor_index].status_flags = 0;
            manager->status[motor_index].error_code = ERR_NO_ERROR;
            manager->status[motor_index].warning_code = 0;
            break;
            
        case CMD_CLEAR_ERROR:
            manager->status[motor_index].status_flags &= ~STATUS_ERROR;
            manager->status[motor_index].error_code = ERR_NO_ERROR;
            break;
            
        case CMD_HOME:
            if (manager->status[motor_index].status_flags & STATUS_ENABLED) {
                manager->status[motor_index].status_flags |= STATUS_MOVING;
                // 这里应该启动回零程序
            }
            break;
            
        default:
            return false; // 未知命令
    }
    
    // 更新状态帧的序列号
    manager->status[motor_index].sequence = frame->sequence;
    manager->status[motor_index].timestamp = get_current_timestamp() & 0xFFFF;
    
    return true;
}

/**
 * @brief 更新电机状态
 * @param manager 协议管理器指针
 * @param motor_id 电机ID (1-6)
 * @param status 状态数据指针
 */
void update_motor_status(ProtocolManager* manager, uint8_t motor_id, const MotorStatusFrame* status)
{
    if (manager == NULL || status == NULL || !is_valid_motor_id(motor_id)) {
        return;
    }
    
    uint8_t motor_index = motor_id - 1;
    
    // 复制状态数据
    memcpy(&manager->status[motor_index], status, sizeof(MotorStatusFrame));
    
    // 确保基本字段正确
    manager->status[motor_index].frame_type = FRAME_TYPE_STATUS;
    manager->status[motor_index].motor_id = motor_id;
    manager->status[motor_index].timestamp = get_current_timestamp() & 0xFFFF;
    
    // 填充校验和
    fill_status_frame_checksum(&manager->status[motor_index]);
}

/**
 * @brief 获取电机状态
 * @param manager 协议管理器指针
 * @param motor_id 电机ID (1-6)
 * @return 状态数据指针
 */
const MotorStatusFrame* get_motor_status(const ProtocolManager* manager, uint8_t motor_id)
{
    if (manager == NULL || !is_valid_motor_id(motor_id)) {
        return NULL;
    }
    
    return &manager->status[motor_id - 1];
}

/**
 * @brief 检查通信超时
 * @param manager 协议管理器指针
 * @param timeout_ms 超时时间(毫秒)
 * @return 超时的电机掩码
 */
uint8_t check_communication_timeout(const ProtocolManager* manager, uint32_t timeout_ms)
{
    if (manager == NULL) {
        return 0;
    }
    
    uint8_t timeout_mask = 0;
    uint32_t current_time = get_current_timestamp();
    
    for (uint8_t i = 0; i < MOTOR_COUNT; i++) {
        if (manager->last_command_time[i] > 0) { // 曾经收到过命令
            uint32_t elapsed = current_time - manager->last_command_time[i];
            if (elapsed > timeout_ms) {
                timeout_mask |= (1 << i);
            }
        }
    }
    
    return timeout_mask;
}

// ================================
// 私有函数实现
// ================================

/**
 * @brief 检查电机ID是否有效
 * @param motor_id 电机ID
 * @return 是否有效
 */
static bool is_valid_motor_id(uint8_t motor_id)
{
    return (motor_id >= 1 && motor_id <= MOTOR_COUNT);
}

/**
 * @brief 获取系统滴答计数
 * @return 系统滴答计数(毫秒)
 * @note 这个函数需要根据具体的硬件平台实现
 */
static uint32_t get_system_tick(void)
{
    // 示例实现，需要根据实际硬件平台修改
    // 例如：STM32可以使用HAL_GetTick()
    // 例如：FreeRTOS可以使用xTaskGetTickCount()
    
    #ifdef STM32_HAL
        return HAL_GetTick();
    #elif defined(FREERTOS)
        return xTaskGetTickCount() * portTICK_PERIOD_MS;
    #else
        // 默认实现，需要用户自己实现
        extern uint32_t system_tick_ms;
        return system_tick_ms;
    #endif
}
