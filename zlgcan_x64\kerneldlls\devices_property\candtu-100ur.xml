<?xml version="1.0"?>
<info locale="device_locale_strings.xml">
	<device>
		<value>0</value>
		<meta>
			<visible>false</visible>
			<type>options.int32</type>
			<desc>设备索引</desc>
			<options>
				<option type="int32" value="0" desc="0"></option>
				<option type="int32" value="1" desc="1"></option>
				<option type="int32" value="2" desc="2"></option>
				<option type="int32" value="3" desc="3"></option>
				<option type="int32" value="4" desc="4"></option>
				<option type="int32" value="5" desc="5"></option>
				<option type="int32" value="6" desc="6"></option>
				<option type="int32" value="7" desc="7"></option>
			</options>
		</meta>
	</device>
	<channel>
		<value>0</value>
		<meta>
			<visible>false</visible>
			<type>options.int32</type>
			<desc>通道号</desc>
			<options>
				<option type="int32" value="0" desc="Channel 0"></option>
			</options>
		</meta>
		<channel_0 stream="channel_0" case="parent-value=0">
			<baud_rate flag="0x0038" at_initcan="post">
				<value>1000000</value>
				<meta>
					<type>options.int32</type>
					<desc>波特率</desc>
					<options>
						<option type="int32" value="1000000" desc="1000kbps"></option>
						<option type="int32" value="800000" desc="800kbps"></option>
						<option type="int32" value="500000" desc="500kbps"></option>
						<option type="int32" value="250000" desc="250kbps"></option>
						<option type="int32" value="125000" desc="125kbps"></option>
						<option type="int32" value="100000" desc="100kbps"></option>
						<option type="int32" value="50000" desc="50kbps"></option>
						<option type="int32" value="20000" desc="20kbps"></option>
						<option type="int32" value="10000" desc="10kbps"></option>
						<option type="int32" value="5000" desc="5kbps"></option>
						<option type="int32" value="0" desc="custom"></option>
					</options>
				</meta>
			</baud_rate>
			<baud_rate_custom flag="0x0044" at_initcan="post">
				<value>1.0Mbps(75%),(3,1,0,0,2)</value>
				<meta>
					<visible>$/info/channel/channel_0/baud_rate == 10</visible>
					<type>string</type>
					<desc>自定义波特率</desc>
				</meta>
			</baud_rate_custom>
			<internal_resistance flag="0x0041" at_initcan="post">
				<value>1</value>
				<meta>
					<type>options.int32</type>
					<desc>内置120欧电阻</desc>
					<options>
						<option type="int32" value="1" desc="use"></option>
						<option type="int32" value="0" desc="no"></option>
					</options>
				</meta>
			</internal_resistance>
			<initenal_resistance flag="0x0041">
				<value>1</value>
				<meta>
					<type>options.int32</type>
					<desc>内置120欧电阻</desc>
					<options>
						<option type="int32" value="1" desc="use"></option>
						<option type="int32" value="0" desc="no"></option>
					</options>
				</meta>
			</initenal_resistance>
			<work_mode flag="0x0042" at_initcan="post">
				<value>1</value>
				<meta>
					<type>options.int32</type>
					<desc>工作模式</desc>
					<options>
						<option type="int32" value="1" desc="normal_mode"></option>
						<option type="int32" value="0" desc="readonly_mode"></option>
					</options>
				</meta>
			</work_mode>
			<acc_code flag="0x0039" hex="1" at_initcan="post">
				<value>0x10</value>
				<meta>
					<type>uint32</type>
					<desc>验收码</desc>
					<visible>false</visible>
				</meta>
			</acc_code>
			<acc_mask flag="0x0040" hex="1" at_initcan="post">
				<value>0x1000</value>
				<meta>
					<type>uint32</type>
					<desc>屏蔽码</desc>
					<visible>false</visible>
				</meta>
			</acc_mask>
			<filter_batch flag="0x0045" at_initcan="post">
				<value></value>
				<meta>
					<type>string</type>
					<visible>false</visible>
					<desc></desc>
				</meta>
			</filter_batch>
		</channel_0>
	</channel>
</info>
