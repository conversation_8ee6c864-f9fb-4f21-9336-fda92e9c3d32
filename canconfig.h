/**
 * @file canconfig.h
 * @brief CAN配置对话框类头文件
 * @details 定义了CAN配置对话框类CANConfig和相关的配置数据结构。
 *          提供CAN/CANFD协议参数配置的用户界面，包括波特率、工作模式、
 *          终端电阻等各种配置选项。
 * <AUTHOR>
 * @date 2025-07-02
 * @version 1.0
 */

#ifndef CANCONFIG_H
#define CANCONFIG_H

#include <QDialog>          // Qt对话框基类
#include <QComboBox>        // Qt下拉选择框控件
#include <QCheckBox>        // Qt复选框控件
#include <QPushButton>      // Qt按钮控件
#include <QVBoxLayout>      // Qt垂直布局管理器
#include <QHBoxLayout>      // Qt水平布局管理器
#include <QGridLayout>      // Qt网格布局管理器
#include <QLabel>           // Qt标签控件
#include <QGroupBox>        // Qt分组框控件
#include <QMessageBox>      // Qt消息框类
#include "baudratecalculator_new.h"  // 波特率计算器

// Qt命名空间标记
QT_BEGIN_NAMESPACE
QT_END_NAMESPACE

/**
 * @struct CANConfigData
 * @brief CAN配置数据结构
 * @details 存储CAN/CANFD协议的所有配置参数，用于在不同组件间传递配置信息
 */
struct CANConfigData {
    QString protocol;           ///< 协议类型：CAN或CANFD
    QString canfdStandard;     ///< CANFD标准：CAN FD或CAN FD ISO
    QString standardType;      ///< 标准类型：星或其他
    QString arbitrationBaud;   ///< 仲裁域波特率：如500Kbps、1Mbps等
    QString dataBaud;          ///< 数据域波特率：如2Mbps、5Mbps等
    QString customBaud;        ///< 自定义波特率：用户自定义的波特率值
    QString workMode;          ///< 工作模式：正常模式、只听模式等
    bool terminalResistance;   ///< 终端电阻：是否启用120欧姆终端电阻
    bool busUtilization;       ///< 上报总线利用率：是否启用总线利用率统计
    bool clearBuffer;          ///< 清空缓冲区：是否在启动时清空接收缓冲区
};

/**
 * @class CANConfig
 * @brief CAN配置对话框类
 * @details 继承自QDialog，提供CAN/CANFD协议参数配置的用户界面。
 *          该类管理所有配置选项的显示和用户交互，包括协议类型选择、
 *          波特率设置、工作模式配置等功能。
 */
class CANConfig : public QDialog
{
    Q_OBJECT  // Qt元对象系统宏，支持信号槽机制

public:
    /**
     * @brief 构造函数
     * @param parent 父窗口指针，默认为nullptr
     * @details 初始化CAN配置对话框，创建UI界面和设置信号槽连接
     */
    CANConfig(QWidget *parent = nullptr);

    /**
     * @brief 析构函数
     * @details 清理资源，释放UI控件占用的内存
     */
    ~CANConfig();

    /**
     * @brief 获取配置数据
     * @return CANConfigData 当前配置的所有参数数据
     * @details 从UI控件中读取用户设置的所有配置参数并返回
     */
    CANConfigData getConfigData() const;

    /**
     * @brief 设置配置数据
     * @param data 要设置的配置数据
     * @details 将配置数据应用到UI控件，更新界面显示
     */
    void setConfigData(const CANConfigData &data);

private slots:
    /**
     * @brief 协议类型改变槽函数
     * @details 响应用户更改协议类型选择，动态调整界面显示
     */
    void onProtocolChanged();

    /**
     * @brief 确认按钮点击槽函数
     * @details 响应用户点击确认按钮，验证配置并接受对话框
     */
    void onConfirmClicked();

    /**
     * @brief 取消按钮点击槽函数
     * @details 响应用户点击取消按钮，拒绝对话框并关闭
     */
    void onCancelClicked();

    /**
     * @brief 波特率计算器按钮点击槽函数
     * @details 打开波特率计算器对话框，配置采样点参数
     */
    void onBaudrateCalculatorClicked();

private:
    /**
     * @brief 设置用户界面
     * @details 创建并布局所有UI控件，设置控件的初始状态
     */
    void setupUI();

    /**
     * @brief 设置信号槽连接
     * @details 连接UI控件的信号到相应的槽函数
     */
    void setupConnections();

    /**
     * @brief 根据协议类型更新界面
     * @details 根据当前选择的协议类型动态显示或隐藏相关控件
     */
    void updateUIForProtocol();

    // === UI控件成员变量 ===
    /**
     * @brief 协议类型选择下拉框
     * @details 用于选择CAN或CANFD协议类型
     */
    QComboBox *protocolComboBox;

    /**
     * @brief CANFD标准选择下拉框
     * @details 用于选择CAN FD或CAN FD ISO标准
     */
    QComboBox *canfdStandardComboBox;

    /**
     * @brief 标准类型选择下拉框
     * @details 用于选择标准类型（星或其他）
     */
    QComboBox *standardTypeComboBox;

    /**
     * @brief 仲裁域波特率选择下拉框
     * @details 用于选择CAN仲裁域的通信波特率
     */
    QComboBox *arbitrationBaudComboBox;

    /**
     * @brief 数据域波特率选择下拉框
     * @details 用于选择CANFD数据域的通信波特率
     */
    QComboBox *dataBaudComboBox;

    /**
     * @brief 自定义波特率选择下拉框
     * @details 用于设置用户自定义的波特率值
     */
    QComboBox *customBaudComboBox;

    /**
     * @brief 工作模式选择下拉框
     * @details 用于选择CAN控制器的工作模式
     */
    QComboBox *workModeComboBox;

    /**
     * @brief 终端电阻选择下拉框
     * @details 用于选择是否启用120欧姆终端电阻
     */
    QComboBox *terminalResistanceComboBox;

    /**
     * @brief 总线利用率选择下拉框
     * @details 用于选择是否上报总线利用率统计信息
     */
    QComboBox *busUtilizationComboBox;

    /**
     * @brief 清空缓冲区复选框
     * @details 用于选择是否在启动时清空接收缓冲区
     */
    QCheckBox *clearBufferCheckBox;

    /**
     * @brief 确认按钮
     * @details 用户点击确认配置并关闭对话框
     */
    QPushButton *confirmButton;

    /**
     * @brief 取消按钮
     * @details 用户点击取消配置并关闭对话框
     */
    QPushButton *cancelButton;

    // === 布局管理器成员变量 ===
    /**
     * @brief 主布局管理器
     * @details 管理整个对话框的垂直布局
     *
     */
    QVBoxLayout *mainLayout;

    /**
     * @brief 配置项网格布局管理器
     * @details 管理配置控件的网格布局
     */
    QGridLayout *configLayout;

    /**
     * @brief 按钮水平布局管理器
     * @details 管理确认和取消按钮的水平布局
     */
    QHBoxLayout *buttonLayout;

    // === 数据成员变量 ===
    /**
     * @brief 配置数据存储
     * @details 存储当前对话框中的所有配置参数
     */
    CANConfigData configData;
};

#endif // CANCONFIG_H
