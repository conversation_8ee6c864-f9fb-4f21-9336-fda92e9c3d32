/**
 * @file canconfig_new.cpp
 * @brief CAN/CANFD配置对话框实现文件 - UI版本
 * @details 实现基于Qt Designer UI的CAN和CANFD协议配置界面
 * <AUTHOR>
 * @date 2024-12-19
 * @version 2.0
 */

#include "canconfig_new.h"
#include "ui_canconfig_new.h"
#include <QDebug>

/**
 * @brief 构造函数
 * @param parent 父窗口指针
 * @details 初始化UI界面并设置信号槽连接
 */
CANConfigNew::CANConfigNew(QWidget *parent)
    : QDialog(parent)
    , ui(new Ui::CANConfigNew)
{
    ui->setupUi(this);
    
    // 设置窗口属性
    setMinimumSize(700, 800);
    resize(700, 800);
    setModal(true);
    
    // 设置信号槽连接
    setupConnections();

    // 初始化界面状态
    updateUIForProtocol();

    // 初始化自定义配置状态
    onCustomConfigTextChanged();
    
    qDebug() << "CAN配置对话框(UI版本)已初始化";
}

/**
 * @brief 自定义配置文本变化槽函数
 * @details 当用户输入自定义配置时，禁用或启用仲裁段和数据段下拉框，并设置视觉效果
 */
void CANConfigNew::onCustomConfigTextChanged()
{
    QString currentText = ui->customBaudLineEdit->text().trimmed();

    if (currentText.isEmpty()) {
        // 没有输入自定义配置，启用仲裁段和数据段下拉框
        ui->arbitrationBaudComboBox->setEnabled(true);
        ui->dataBaudComboBox->setEnabled(true);

        // 恢复正常样式
        ui->arbitrationBaudComboBox->setStyleSheet("");
        ui->dataBaudComboBox->setStyleSheet("");

        qDebug() << "启用标准波特率配置";
    } else {
        // 输入了自定义配置，禁用仲裁段和数据段下拉框
        ui->arbitrationBaudComboBox->setEnabled(false);
        ui->dataBaudComboBox->setEnabled(false);

        // 设置禁用状态的灰色样式
        QString disabledStyle = "QComboBox {"
                               "background-color: #f0f0f0;"
                               "color: #888888;"
                               "border: 1px solid #cccccc;"
                               "}"
                               "QComboBox::drop-down {"
                               "background-color: #f0f0f0;"
                               "border: none;"
                               "}"
                               "QComboBox::down-arrow {"
                               "image: none;"
                               "border: none;"
                               "}";

        ui->arbitrationBaudComboBox->setStyleSheet(disabledStyle);
        ui->dataBaudComboBox->setStyleSheet(disabledStyle);

        qDebug() << "使用自定义配置，禁用标准配置:" << currentText;
    }
}

/**
 * @brief 析构函数
 */
CANConfigNew::~CANConfigNew()
{
    delete ui;
}

/**
 * @brief 设置信号槽连接
 * @details 连接UI控件的信号到相应的槽函数
 */
void CANConfigNew::setupConnections()
{
    // 协议类型改变信号
    connect(ui->protocolComboBox, &QComboBox::currentTextChanged, 
            this, &CANConfigNew::onProtocolChanged);
    
    // 波特率计算器按钮
    connect(ui->baudrateCalcButton, &QPushButton::clicked,
            this, &CANConfigNew::onBaudrateCalculatorClicked);

    // 自定义配置文本变化
    connect(ui->customBaudLineEdit, &QLineEdit::textChanged,
            this, &CANConfigNew::onCustomConfigTextChanged);

    // 确定和取消按钮
    connect(ui->confirmButton, &QPushButton::clicked,
            this, &CANConfigNew::onOkClicked);
    connect(ui->cancelButton, &QPushButton::clicked,
            this, &CANConfigNew::onCancelClicked);
}

/**
 * @brief 协议类型改变槽函数
 * @details 根据协议类型动态调整界面显示
 */
void CANConfigNew::onProtocolChanged()
{
    updateUIForProtocol();
    qDebug() << "协议类型已切换至:" < ui->protocolComboBox->currentText();
}

/**
 * @brief 根据协议类型更新界面
 * @details 根据当前选择的协议类型动态显示或隐藏相关控件
 */
void CANConfigNew::updateUIForProtocol()
{
    QString protocol = ui->protocolComboBox->currentText();
    bool isCANFD = (protocol == "CANFD");

    // 数据域波特率仅在CANFD模式下可用
    ui->dataBaudLabel->setVisible(isCANFD);
    ui->dataBaudComboBox->setVisible(isCANFD);

    // 自定义配置仅在CANFD模式下可用
    ui->customBaudLabel->setVisible(isCANFD);
    ui->customBaudLineEdit->setVisible(isCANFD);

    // 根据协议类型调整窗口高度
    if (isCANFD) {
        setMinimumSize(900, 950);
        resize(900, 950);
    } else {
        setMinimumSize(900, 900);
        resize(900, 900);
    }

    qDebug() << "界面已更新为" << protocol << "模式";
}

/**
 * @brief 波特率计算器按钮点击槽函数
 * @details 打开波特率计算器对话框，允许用户精确配置采样点参数
 */
void CANConfigNew::onBaudrateCalculatorClicked()
{
    qDebug() << "打开波特率计算器";

    // 创建波特率计算器对话框
    BaudrateCalculator calculator(this);

    // 显示对话框并等待用户操作
    int result = calculator.exec();

    // 波特率计算器现在只是一个查看和计算工具，不直接应用配置
    // 用户需要通过"导入配置"按钮来导入配置
    qDebug() << "波特率计算器对话框已关闭，结果:" << (result == QDialog::Accepted ? "确定" : "取消");

    if (result == QDialog::Accepted) {
        // 用户点击了应用配置，获取计算结果
        BaudrateResult arbResult = calculator.getArbitrationResult();
        BaudrateResult dataResult = calculator.getDataResult();
        
        // 更新仲裁域波特率下拉框
        QString arbText = QString("%1kbps %2%")
                         .arg(arbResult.actualBaud / 1000)
                         .arg(arbResult.samplePoint, 0, 'f', 1);
        
        // 检查是否已存在该选项，如果不存在则添加
        int arbIndex = ui->arbitrationBaudComboBox->findText(arbText);
        if (arbIndex == -1) {
            ui->arbitrationBaudComboBox->addItem(arbText);
            ui->arbitrationBaudComboBox->setCurrentText(arbText);
        } else {
            ui->arbitrationBaudComboBox->setCurrentIndex(arbIndex);
        }
        
        // 如果是CANFD模式，更新数据域波特率
        if (ui->protocolComboBox->currentText() == "CANFD") {
            QString dataText = QString("%1Mbps %2%")
                              .arg(dataResult.actualBaud / 1000000.0, 0, 'f', 1)
                              .arg(dataResult.samplePoint, 0, 'f', 1);
            
            int dataIndex = ui->dataBaudComboBox->findText(dataText);
            if (dataIndex == -1) {
                ui->dataBaudComboBox->addItem(dataText);
                ui->dataBaudComboBox->setCurrentText(dataText);
            } else {
                ui->dataBaudComboBox->setCurrentIndex(dataIndex);
            }
        }
        
        // 显示配置成功消息
        QMessageBox msgBox(this);
        msgBox.setWindowTitle("配置成功");
        msgBox.setIcon(QMessageBox::Information);
        msgBox.setText(QString("波特率配置已更新：\n"
                              "仲裁段: %1\n"
                              "数据段: %2")
                       .arg(arbText)
                       .arg(ui->protocolComboBox->currentText() == "CANFD" ?
                            QString("%1Mbps %2%").arg(dataResult.actualBaud / 1000000.0, 0, 'f', 1)
                                                 .arg(dataResult.samplePoint, 0, 'f', 1) :
                            "N/A"));
        msgBox.setStandardButtons(QMessageBox::Ok);

        // 设置红白风格样式
        msgBox.setStyleSheet(
            "QMessageBox {"
            "    background-color: #ffffff;"
            "    color: #333333;"
            "    font-family: 'Microsoft YaHei UI', 'Segoe UI', Arial, sans-serif;"
            "    font-size: 14px;"
            "    border: 2px solid #dc3545;"
            "    border-radius: 8px;"
            "    max-width: 350px;"
            "    min-height: 120px;"
            "}"
            "QMessageBox QLabel {"
            "    color: #333333;"
            "    font-weight: 500;"
            "    font-size: 14px;"
            "    padding: 10px;"
            "    max-width: 300px;"
            "    qproperty-wordWrap: true;"
            "}"
            "QMessageBox QPushButton {"
            "    background-color: #ffffff;"
            "    color: #dc3545;"
            "    border: 2px solid #dc3545;"
            "    border-radius: 6px;"
            "    padding: 8px 20px;"
            "    font-weight: bold;"
            "    font-size: 14px;"
            "    min-width: 80px;"
            "    min-height: 35px;"
            "}"
            "QMessageBox QPushButton:hover {"
            "    background-color: #dc3545;"
            "    color: white;"
            "}"
            "QMessageBox QPushButton:pressed {"
            "    background-color: #c82333;"
            "    color: white;"
            "}"
        );

        msgBox.exec();
        
        qDebug() << "波特率配置已更新 - 仲裁段:" << arbText;
    }
}

/**
 * @brief 确定按钮点击槽函数
 * @details 验证配置并接受对话框
 */
void CANConfigNew::onOkClicked()
{
    if (validateConfig()) {
        qDebug() << "配置验证通过，应用配置";
        accept();
    }
}

/**
 * @brief 取消按钮点击槽函数
 * @details 拒绝对话框并关闭
 */
void CANConfigNew::onCancelClicked()
{
    qDebug() << "用户取消配置";
    reject();
}

/**
 * @brief 验证配置参数
 * @return 配置是否有效
 * @details 检查用户输入的配置参数是否合理
 */
bool CANConfigNew::validateConfig()
{
    // 检查基本配置
    if (ui->protocolComboBox->currentText().isEmpty()) {
        showStyledWarning("配置错误", "请选择协议类型");
        return false;
    }

    if (ui->arbitrationBaudComboBox->currentText().isEmpty()) {
        showStyledWarning("配置错误", "请选择仲裁域波特率");
        return false;
    }

    // 如果是CANFD模式，检查数据域波特率
    if (ui->protocolComboBox->currentText() == "CANFD" &&
        ui->dataBaudComboBox->currentText().isEmpty()) {
        showStyledWarning("配置错误", "CANFD模式下请选择数据域波特率");
        return false;
    }
    
    return true;
}



/**
 * @brief 显示统一样式的警告消息框
 * @param title 消息框标题
 * @param text 消息框内容
 * @details 显示红白风格的警告消息框
 */
void CANConfigNew::showStyledWarning(const QString &title, const QString &text)
{
    QMessageBox msgBox(this);
    msgBox.setWindowTitle(title);
    msgBox.setIcon(QMessageBox::Warning);
    msgBox.setText(text);
    msgBox.setStandardButtons(QMessageBox::Ok);

    // 设置红白风格样式
    msgBox.setStyleSheet(
        "QMessageBox {"
        "    background-color: #ffffff;"
        "    color: #333333;"
        "    font-family: 'Microsoft YaHei UI', 'Segoe UI', Arial, sans-serif;"
        "    font-size: 14px;"
        "    border: 2px solid #dc3545;"
        "    border-radius: 8px;"
        "    max-width: 350px;"
        "    min-height: 120px;"
        "}"
        "QMessageBox QLabel {"
        "    color: #333333;"
        "    font-weight: 500;"
        "    font-size: 14px;"
        "    padding: 10px;"
        "    max-width: 300px;"
        "    qproperty-wordWrap: true;"
        "}"
        "QMessageBox QPushButton {"
        "    background-color: #ffffff;"
        "    color: #dc3545;"
        "    border: 2px solid #dc3545;"
        "    border-radius: 6px;"
        "    padding: 8px 20px;"
        "    font-weight: bold;"
        "    font-size: 14px;"
        "    min-width: 80px;"
        "    min-height: 35px;"
        "}"
        "QMessageBox QPushButton:hover {"
        "    background-color: #dc3545;"
        "    color: white;"
        "}"
        "QMessageBox QPushButton:pressed {"
        "    background-color: #c82333;"
        "    color: white;"
        "}"
    );

    msgBox.exec();
}

/**
 * @brief 获取配置数据
 * @return 当前配置的所有参数
 * @details 从UI控件中读取用户设置的配置参数
 */
CANConfigData CANConfigNew::getConfigData() const
{
    CANConfigData data;

    // 基本协议配置
    data.protocol = ui->protocolComboBox->currentText();

    // 波特率配置 
    data.canfdStandard = "CAN FD";  // 默认标准
    data.standardType = ui->standardTypeComboBox->currentText();
    data.arbitrationBaud = ui->arbitrationBaudComboBox->currentText(); 
    data.dataBaud = ui->dataBaudComboBox->currentText();
    data.customBaud = ui->customBaudLineEdit->text().trimmed();

    // 工作模式配置
    data.workMode = ui->workModeComboBox->currentText();
    data.terminalResistance = (ui->resistanceComboBox->currentText() == "使能");

    // 高级配置
    data.busUtilization = (ui->busUtilizationComboBox->currentText() == "使能");
    data.clearBuffer = ui->clearBufferCheckBox->isChecked();

    qDebug() << "获取配置数据 - 协议:" << data.protocol
             << "仲裁段波特率:" << data.arbitrationBaud
             << "数据段波特率:" << data.dataBaud;

    return data;
}

/**
 * @brief 设置配置数据
 * @param data 要设置的配置数据
 * @details 将配置数据应用到UI控件中
 */
void CANConfigNew::setConfigData(const CANConfigData &data)
{
    // 基本协议配置
    ui->protocolComboBox->setCurrentText(data.protocol);

    // 波特率配置
    ui->standardTypeComboBox->setCurrentText(data.standardType);
    ui->arbitrationBaudComboBox->setCurrentText(data.arbitrationBaud);
    ui->dataBaudComboBox->setCurrentText(data.dataBaud);
    ui->customBaudLineEdit->setText(data.customBaud);

    // 工作模式配置
    ui->workModeComboBox->setCurrentText(data.workMode);
    ui->resistanceComboBox->setCurrentText(data.terminalResistance ? "使能" : "禁用");

    // 高级配置
    ui->busUtilizationComboBox->setCurrentText(data.busUtilization ? "使能" : "禁用");
    ui->clearBufferCheckBox->setChecked(data.clearBuffer);

    // 触发协议变化处理，确保界面状态正确
    updateUIForProtocol();

    // 更新自定义配置状态
    onCustomConfigTextChanged();

    qDebug() << "设置配置数据 - 协议:" << data.protocol
             << "仲裁段波特率:" << data.arbitrationBaud
             << "数据段波特率:" << data.dataBaud;
}
