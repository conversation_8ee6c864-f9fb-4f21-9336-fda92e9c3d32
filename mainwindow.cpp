/**
 * @file mainwindow.cpp
 * @brief 主窗口类实现文件
 * @details 实现了MainWindow类的所有功能，包括界面初始化、事件处理、
 *          子窗口管理等。这是应用程序的核心控制逻辑所在。
 * <AUTHOR>
 * @date 2025-07-02
 * @version 1.0
 */

#include "mainwindow.h"          // 主窗口类声明
#include "ui_mainwindow.h"       // Qt Designer生成的UI类
#include <QMessageBox>           // Qt消息框类
#include <QLabel>                // Qt标签类
#include <QPixmap>               // Qt图像类
#include <QPainter>              // Qt绘图类
#include <QTimer>                // Qt定时器类
#include "protocolselectform.h"  // 协议选择窗口类
#include "modbusform.h"          // Modbus协议设置窗口类
#include "canconfig.h"           // CAN/CANFD协议设置窗口类
#include "messagebox_utils.h"    // 消息框工具类

/**
 * @brief MainWindow构造函数实现
 * @param parent 父窗口指针
 * @details 初始化主窗口的所有组件和设置：
 *          1. 调用基类构造函数
 *          2. 创建UI实例并设置界面
 *          3. 初始化各个页面的默认状态
 *          4. 刷新系统串口列表
 */
MainWindow::MainWindow(QWidget *parent)
    : QMainWindow(parent)                    // 调用基类构造函数
    , ui(new Ui::MainWindow)                 // 创建UI实例
    , protocolSelectForm(nullptr)            // 初始化协议选择窗口指针为空
    , oscilloscopeInterface(nullptr)         // 初始化示波器界面指针为空
    , multiMotorInterface(nullptr)           // 初始化多电机界面指针为空（旧版本）
    , multiMotorControlPure(nullptr)         // 初始化纯UI多电机界面指针为空（新版本）
    , motorDataManager(nullptr)              // 初始化电机数据管理器指针为空
    , logoBackgroundLabel(nullptr)           // 初始化logo背景标签指针为空
{
    // 设置UI界面，加载由Qt Designer设计的界面布局
    ui->setupUi(this);

    // 注意：Qt会自动尝试连接信号槽，但某些按钮不存在于UI中，会产生警告
    // 这些警告不影响程序运行，是正常现象

    // 预初始化关键组件，防止首次使用时崩溃
    preInitializeCriticalComponents();

    // 延迟设置背景效果，确保UI完全加载
    QTimer::singleShot(200, this, [this]() {
        setupEnhancedBackground();
    });

    // 设置应用程序窗口标题
    this->setWindowTitle("电机上位机助手");

    // 设置窗口大小和最小尺寸，确保布局正确显示
    this->resize(1200, 800);           // 设置合适的窗口大小
    this->setMinimumSize(1000, 700);   // 设置最小尺寸防止布局混乱

    // 初始化主界面，现在使用现代化的卡片式设计
    // 不再需要TabWidget，直接显示主界面

    // 初始化多电机控制相关组件（延迟初始化，避免启动时崩溃）
    // initializeMultiMotorControl();

    // 不再手动连接多电机控制按钮信号，使用Qt的自动连接机制
    // connect(ui->btnMultiMotorControl, &QPushButton::clicked,
    //         this, &MainWindow::onMultiMotorControlClicked);

    // 初始化完成，现在使用独立的示波器窗口
    // 不再需要在主窗口中管理示波器按钮状态


}

/**
 * @brief MainWindow析构函数实现
 * @details 清理主窗口占用的所有资源：
 *          1. 释放UI对象内存
 *          2. 释放协议选择窗口对象内存（如果存在）
 */
MainWindow::~MainWindow()
{
    // 释放UI对象占用的内存
    delete ui;

    // 如果协议选择窗口已创建，则释放其占用的内存
    if (protocolSelectForm) {
        delete protocolSelectForm;
    }

    // 如果示波器界面已创建，则释放其占用的内存
    if (oscilloscopeInterface) {
        delete oscilloscopeInterface;
    }

    // 如果多电机界面已创建，则释放其占用的内存
    if (multiMotorInterface) {
        delete multiMotorInterface;
    }

    // 如果电机数据管理器已创建，则释放其占用的内存
    if (motorDataManager) {
        delete motorDataManager;
    }

    // 如果纯UI多电机控制界面已创建，则释放其占用的内存
    if (multiMotorControlPure) {
        delete multiMotorControlPure;
    }
}



/**
 * @brief 协议选择按钮点击事件处理函数
 * @details 响应用户点击协议选择按钮的操作：
 *          1. 如果协议选择窗口未创建，则创建新实例
 *          2. 连接协议选择窗口的返回信号到主窗口的处理槽
 *          3. 显示协议选择窗口并隐藏主窗口
 */
void MainWindow::on_protocolButton_clicked()
{
    qDebug() << "按钮点击: 打开连接设置界面";

    // 如果协议选择窗口尚未创建，则创建新实例
    if (!protocolSelectForm) {
        protocolSelectForm = new ProtocolSelectForm();
        // 连接协议选择窗口的返回信号到主窗口的处理槽函数
        connect(protocolSelectForm, &ProtocolSelectForm::backToMain,
                this, &MainWindow::onProtocolSelectBack);
    }

    // 显示协议选择窗口
    protocolSelectForm->show();
    // 隐藏主窗口，实现窗口切换效果
    this->hide();

    qDebug() << "界面切换: 显示连接设置界面";
}

/**
 * @brief 协议选择窗口返回事件处理函数
 * @details 当协议选择窗口发出返回信号时调用：
 *          1. 隐藏协议选择窗口
 *          2. 重新显示主窗口
 */
void MainWindow::onProtocolSelectBack()
{
    // 如果协议选择窗口存在，则隐藏它
    if (protocolSelectForm) {
        protocolSelectForm->hide();
    }
    // 重新显示主窗口
    this->show();

    qDebug() << "界面切换: 从连接设置返回主界面";
}

/**
 * @brief 处理示波器界面返回的槽函数实现
 * @details 当示波器界面关闭时调用，重新显示主窗口
 */
void MainWindow::onOscilloscopeBack()
{
    // 隐藏示波器界面
    if (oscilloscopeInterface) {
        oscilloscopeInterface->hide();
    }

    // 重新显示主窗口
    this->show();
    this->raise();
    this->activateWindow();

    qDebug() << "界面切换: 从示波器返回主界面";
}

/**
 * @brief 虚拟示波器按钮点击事件处理函数
 * @details 响应用户点击虚拟示波器按钮，打开独立的示波器界面窗口
 */
void MainWindow::on_btnWave_clicked()
{
    // 如果示波器界面尚未创建，则创建新实例
    if (!oscilloscopeInterface) {
        oscilloscopeInterface = new OscilloscopeInterface();

        // 连接示波器界面的返回信号到主界面的槽函数
        connect(oscilloscopeInterface, &OscilloscopeInterface::backToMainInterface,
                this, &MainWindow::onOscilloscopeBack);
    }

    // 进行设备连接检测
    if (!oscilloscopeInterface->checkDeviceConnectionAndPrompt()) {
        qDebug() << "用户取消了虚拟示波器";
        return;
    }

    // 隐藏主界面
    this->hide();

    // 显示示波器界面
    oscilloscopeInterface->show();
    oscilloscopeInterface->raise();
    oscilloscopeInterface->activateWindow();

    qDebug() << "按钮点击: 打开虚拟示波器界面";
}

/**
 * @brief 帮助按钮点击事件处理函数
 * @details 响应用户点击帮助按钮，显示应用程序的帮助信息对话框
 */
void MainWindow::on_btnHelp_clicked()
{
    qDebug() << "按钮点击: 打开帮助对话框";

    // 使用统一的红白科技风格帮助对话框
    MessageBoxUtils::information(this, "帮助",
        "电机上位机助手\n\n"
        "洛阳千歌机器人科技有限公司\n\n"
        "支持的协议：\n"
        "• Modbus协议\n"
        "• CAN/CANFD协议\n\n"
        "如需更多帮助，请联系开发人员。");
}

/**
 * @brief 退出按钮点击事件处理函数
 * @details 响应用户点击退出按钮，关闭应用程序主窗口
 */
void MainWindow::on_btnExit_clicked()
{
    qDebug() << "按钮点击: 退出应用程序";
    // 关闭主窗口，这将触发应用程序退出
    close();
}













/**
 * @brief 虚拟示波器页面返回按钮点击事件处理函数
 * @details 响应用户点击返回按钮，从虚拟示波器页面返回到主界面
 */
void MainWindow::on_btnBackFromWave_clicked()
{
    // 从虚拟示波器页面返回到主界面
    // 现在使用窗口切换而不是标签页切换
    this->show();
}

// 移除了不再需要的示波器按钮处理函数
// 示波器功能现在完全在独立的OscilloscopeInterface窗口中管理

/**
 * @brief 虚拟示波器清除按钮点击事件处理函数
 * @details 响应用户点击清除按钮，清空虚拟示波器显示的所有数据
 */
void MainWindow::on_btnClearWave_clicked()
{
    // 清除虚拟示波器中显示的所有数据
    MessageBoxUtils::information(this, "示波器", "示波器数据已清除");
}

// === 新增功能按钮槽函数实现 ===

/**
 * @brief 功率设置按钮点击事件处理函数
 * @details 响应用户点击功率设置按钮，显示功能正在开发中的提示
 */
void MainWindow::on_btnFunctionSettings_clicked()
{
    qDebug() << "按钮点击: 功率设置";
    MessageBoxUtils::information(this, "功率设置", "功率设置模块正在开发中，敬请期待！");
}

/**
 * @brief 运动控制按钮点击事件处理函数
 * @details 响应用户点击运动控制按钮，显示功能正在开发中的提示
 */
void MainWindow::on_btnMotion_clicked()
{
    qDebug() << "按钮点击: 运动控制";
    MessageBoxUtils::information(this, "运动控制", "运动控制模块正在开发中，敬请期待！");
}

/**
 * @brief 参数存储按钮点击事件处理函数
 * @details 响应用户点击参数存储按钮，显示功能正在开发中的提示
 */
void MainWindow::on_btnParameterStorage_clicked()
{
    qDebug() << "按钮点击: 参数存储";
    MessageBoxUtils::information(this, "参数存储", "参数存储模块正在开发中，敬请期待！");
}

/**
 * @brief PID设置按钮点击事件处理函数
 * @details 响应用户点击PID设置按钮，显示功能正在开发中的提示
 */
void MainWindow::on_btnPIDSettings_clicked()
{
    qDebug() << "按钮点击: PID设置";
    MessageBoxUtils::information(this, "PID设置", "PID设置模块正在开发中，敬请期待！");
}

/**
 * @brief 编码器按钮点击事件处理函数
 * @details 响应用户点击编码器按钮，显示功能正在开发中的提示
 */
void MainWindow::on_btnEncoder_clicked()
{
    qDebug() << "按钮点击: 编码器";
    MessageBoxUtils::information(this, "编码器", "编码器模块正在开发中，敬请期待！");
}

/**
 * @brief 状态监控按钮点击事件处理函数
 * @details 响应用户点击状态监控按钮，显示功能正在开发中的提示
 */
void MainWindow::on_btnStatusMonitor_clicked()
{
    qDebug() << "按钮点击: 状态监控";
    MessageBoxUtils::information(this, "状态监控", "状态监控模块正在开发中，敬请期待！");
}

/**
 * @brief 安全电源按钮点击事件处理函数
 * @details 响应用户点击安全电源按钮，显示功能正在开发中的提示
 */
void MainWindow::on_btnSafePower_clicked()
{
    qDebug() << "按钮点击: 安全电源";
    MessageBoxUtils::information(this, "安全电源", "安全电源模块正在开发中，敬请期待！");
}

/**
 * @brief 位置保护按钮点击事件处理函数
 * @details 响应用户点击位置保护按钮，显示功能正在开发中的提示
 */
void MainWindow::on_btnPositionProtection_clicked()
{
    qDebug() << "按钮点击: 位置保护";
    MessageBoxUtils::information(this, "位置保护", "位置保护模块正在开发中，敬请期待！");
}

/**
 * @brief 安全速度按钮点击事件处理函数
 * @details 响应用户点击安全速度按钮，显示功能正在开发中的提示
 */
void MainWindow::on_btnSafeSpeed_clicked()
{
    qDebug() << "按钮点击: 安全速度";
    MessageBoxUtils::information(this, "安全速度", "安全速度模块正在开发中，敬请期待！");
}

/**
 * @brief 故障诊断按钮点击事件处理函数
 * @details 响应用户点击故障诊断按钮，显示功能正在开发中的提示
 */
void MainWindow::on_btnFaultDiagnosis_clicked()
{
    qDebug() << "按钮点击: 故障诊断";
    MessageBoxUtils::information(this, "故障诊断", "故障诊断模块正在开发中，敬请期待！");
} 

/**
 * @brief 保存按钮点击事件处理函数
 * @details 响应用户点击保存按钮，显示功能正在开发中的提示
 */
void MainWindow::on_btnSave_clicked()
{
    qDebug() << "按钮点击: 保存";
    MessageBoxUtils::information(this, "保存", "保存功能正在开发中，敬请期待！");
}

// === 第二批新增功能按钮槽函数实现 ===

/**
 * @brief 电流回路按钮点击事件处理函数
 * @details 响应用户点击电流回路按钮，显示功能正在开发中的提示
 */
void MainWindow::on_btnCurrentLoop_clicked()
{
    qDebug() << "按钮点击: 电流回路";
    MessageBoxUtils::information(this, "电流回路", "电流回路功能正在开发中，敬请期待！");
}

/**
 * @brief 增量保护按钮点击事件处理函数
 * @details 响应用户点击增量保护按钮，显示功能正在开发中的提示
 */
void MainWindow::on_btnIncrementalProtection_clicked()
{
    qDebug() << "按钮点击: 增量保护";
    MessageBoxUtils::information(this, "增量保护", "增量保护功能正在开发中，敬请期待！");
}

/**
 * @brief 机械零点标定按钮点击事件处理函数
 * @details 响应用户点击机械零点标定按钮，显示功能正在开发中的提示
 */
void MainWindow::on_btnMechanicalZeroCalibration_clicked()
{
    qDebug() << "按钮点击: 机械零点标定";
    MessageBoxUtils::information(this, "机械零点标定", "机械零点标定功能正在开发中，敬请期待！");
}

/**
 * @brief 控制台按钮点击事件处理函数
 * @details 响应用户点击控制台按钮，显示功能正在开发中的提示
 */
void MainWindow::on_btnConsole_clicked()
{
    qDebug() << "按钮点击: 控制台";
    MessageBoxUtils::information(this, "控制台", "控制台功能正在开发中，敬请期待！");
}

/**
 * @brief 马达停止按钮点击事件处理函数
 * @details 响应用户点击马达停止按钮，显示功能正在开发中的提示
 */
void MainWindow::on_btnMotorStop_clicked()
{
    qDebug() << "按钮点击: 马达停止";
    MessageBoxUtils::information(this, "马达停止", "马达停止功能正在开发中，敬请期待！");
}

/**
 * @brief 设置马达按钮点击事件处理函数
 * @details 响应用户点击设置马达按钮，显示功能正在开发中的提示
 */
void MainWindow::on_btnMotorSetup_clicked()
{
    qDebug() << "按钮点击: 设置马达";
    MessageBoxUtils::information(this, "设置马达", "设置马达功能正在开发中，敬请期待！");
}

/**
 * @brief 自检按钮点击事件处理函数
 * @details 响应用户点击自检按钮，显示功能正在开发中的提示
 */
void MainWindow::on_btnSelfCheck_clicked()
{
    qDebug() << "按钮点击: 自检";
    MessageBoxUtils::information(this, "自检", "自检功能正在开发中，敬请期待！");
}

/**
 * @brief IO设置按钮点击事件处理函数
 * @details 响应用户点击IO设置按钮，显示功能正在开发中的提示
 */
void MainWindow::on_btnIOSettings_clicked()
{
    qDebug() << "按钮点击: IO设置";
    MessageBoxUtils::information(this, "IO设置", "IO设置功能正在开发中，敬请期待！");
}

/**
 * @brief 多电机控制按钮点击事件处理函数（自动连接版本）
 * @details 响应用户点击多电机控制按钮，使用独立窗口模式确保稳定性
 * @note 采用独立窗口模式，避免线程问题和崩溃
 */
void MainWindow::on_btnMultiMotorControl_clicked()
{
    qDebug() << "按钮点击: 多电机控制";

    // 如果多电机控制界面尚未创建，则创建新实例
    if (!multiMotorControlPure) {
        try {
            multiMotorControlPure = new MultiMotorControlPure();  // 不设置父窗口，独立窗口

            // 连接返回信号到主界面的槽函数
            connect(multiMotorControlPure, &MultiMotorControlPure::backToMainInterface,
                    this, &MainWindow::onMultiMotorBack);

            qDebug() << "多电机控制界面创建成功";
        } catch (const std::exception& e) {
            qDebug() << "多电机控制界面创建异常:" << e.what();
            MessageBoxUtils::warning(this, "错误", "多电机控制界面创建失败，请重试！");
            return;
        } catch (...) {
            qDebug() << "多电机控制界面创建发生未知异常";
            MessageBoxUtils::warning(this, "错误", "多电机控制界面创建失败，请重试！");
            return;
        }
    }

    // 进行设备连接检测
    if (!multiMotorControlPure->checkDeviceConnectionAndPrompt()) {
        qDebug() << "用户取消了多电机控制系统";
        return;
    }

    // 隐藏主界面
    this->hide();

    // 显示多电机控制界面（独立窗口）
    multiMotorControlPure->show();
    multiMotorControlPure->raise();
    multiMotorControlPure->activateWindow();

    qDebug() << "多电机控制界面已显示";
}

/**
 * @brief 设置增强的背景效果，包含虚化logo和科技元素
 * @details 在主界面添加虚化logo背景、科技网格、光效等元素
 */
void MainWindow::setupEnhancedBackground()
{
    // 1. 设置极淡的虚化logo背景
    setupBlurredLogo();

    // 2. 添加淡淡的光效元素
    setupLightEffects();

    // 3. 保持简洁，不添加额外装饰
}

/**
 * @brief 设置虚化logo背景
 */
void MainWindow::setupBlurredLogo()
{
    // 如果已存在logo标签，先删除
    if (logoBackgroundLabel) {
        logoBackgroundLabel->deleteLater();
        logoBackgroundLabel = nullptr;
    }

    // 创建新的logo背景标签
    logoBackgroundLabel = new QLabel(ui->buttonFrame);

    QPixmap originalLogo(":/icon/qiange.png");
    if (!originalLogo.isNull()) {
        // 获取按钮区域的尺寸
        int frameWidth = ui->buttonFrame->width();
        int frameHeight = ui->buttonFrame->height();

        // 缩放logo到合适大小
        QPixmap scaledLogo = originalLogo.scaled(frameWidth * 0.8, frameHeight * 0.8, Qt::KeepAspectRatio, Qt::SmoothTransformation);

        // 创建透明灰色版本的logo
        QPixmap grayLogo(scaledLogo.size());
        grayLogo.fill(Qt::transparent);

        QPainter painter(&grayLogo);
        painter.setRenderHint(QPainter::Antialiasing);
        painter.setRenderHint(QPainter::SmoothPixmapTransform);

        // 设置极低的透明度直接绘制logo
        painter.setOpacity(0.06); // 6%的透明度，稍微明显一点
        painter.drawPixmap(0, 0, scaledLogo);
        painter.end();

        logoBackgroundLabel->setPixmap(grayLogo);
        logoBackgroundLabel->setAlignment(Qt::AlignCenter);

        // 填充整个按钮区域
        logoBackgroundLabel->setGeometry(0, 0, frameWidth, frameHeight);

        // 设置样式表
        logoBackgroundLabel->setStyleSheet(
            "QLabel {"
            "    background: transparent;"
            "}"
        );

        // 确保在最底层且不响应鼠标事件
        logoBackgroundLabel->lower();
        logoBackgroundLabel->setAttribute(Qt::WA_TransparentForMouseEvents, true);
        logoBackgroundLabel->show();
    }
}

/**
 * @brief 设置科技网格背景
 */
void MainWindow::setupTechGrid()
{
    qDebug() << "开始设置科技网格背景...";

    // 在按钮区域添加科技网格
    QLabel *gridLabel = new QLabel(ui->buttonFrame);
    gridLabel->setGeometry(0, 0, ui->buttonFrame->width(), ui->buttonFrame->height());

    // 创建网格图案
    QPixmap gridPixmap(ui->buttonFrame->size());
    gridPixmap.fill(Qt::transparent);

    QPainter painter(&gridPixmap);
    painter.setRenderHint(QPainter::Antialiasing);

    // 设置可见的网格线条
    QPen gridPen(QColor(220, 53, 69, 40)); // 更明显的红色
    gridPen.setWidth(1);
    painter.setPen(gridPen);

    // 绘制网格 - 适中间距
    for (int x = 0; x < ui->buttonFrame->width(); x += 60) {
        painter.drawLine(x, 0, x, ui->buttonFrame->height());
    }
    for (int y = 0; y < ui->buttonFrame->height(); y += 60) {
        painter.drawLine(0, y, ui->buttonFrame->width(), y);
    }

    painter.end();
    gridLabel->setPixmap(gridPixmap);
    gridLabel->lower();
    gridLabel->setAttribute(Qt::WA_TransparentForMouseEvents, true);
    gridLabel->show();

    qDebug() << "科技网格背景设置完成，尺寸:" << ui->buttonFrame->width() << "x" << ui->buttonFrame->height();
}

/**
 * @brief 设置光效元素
 */
void MainWindow::setupLightEffects()
{
    // 在按钮区域的四个角落添加光晕效果下·
    QList<QPoint> positions = {
        QPoint(20, 20),           // 左上
        QPoint(ui->buttonFrame->width()-120, 20),  // 右上
        QPoint(20, ui->buttonFrame->height()-120), // 左下
        QPoint(ui->buttonFrame->width()-120, ui->buttonFrame->height()-120) // 右下
    };

    for (int i = 0; i < positions.size(); ++i) {
        QLabel *lightLabel = new QLabel(ui->buttonFrame);
        lightLabel->setFixedSize(100, 100);

        // 创建光晕效果
        QPixmap lightPixmap(100, 100);
        lightPixmap.fill(Qt::transparent);

        QPainter painter(&lightPixmap);
        painter.setRenderHint(QPainter::Antialiasing);

        // 创建非常淡的径向渐变光晕
        QRadialGradient gradient(50, 50, 50);
        gradient.setColorAt(0, QColor(220, 53, 69, 20));   // 中心更淡
        gradient.setColorAt(0.5, QColor(220, 53, 69, 10)); // 中间更淡
        gradient.setColorAt(1, QColor(220, 53, 69, 0));    // 边缘透明

        painter.setBrush(gradient);
        painter.setPen(Qt::NoPen);
        painter.drawEllipse(0, 0, 100, 100);
        painter.end();

        lightLabel->setPixmap(lightPixmap);
        lightLabel->move(positions[i]);
        lightLabel->lower();
        lightLabel->setAttribute(Qt::WA_TransparentForMouseEvents, true);
        lightLabel->show();
    }


}

/**
 * @brief 设置科技装饰元素
 */




/**
 * @brief 窗口大小改变事件处理
 * @param event 大小改变事件
 * @details 当窗口大小改变时，重新设置背景效果以适应新尺寸
 */
void MainWindow::resizeEvent(QResizeEvent *event)
{
    QMainWindow::resizeEvent(event);

    // 延迟重新设置背景，确保UI已经完全更新
    QTimer::singleShot(100, this, [this]() {
        setupEnhancedBackground();
    });
}

// ================================
// 多电机控制相关函数实现
// ================================

/**
 * @brief 初始化多电机控制组件
 * @details 创建电机数据管理器和多电机控制界面，并建立信号连接
 */
void MainWindow::initializeMultiMotorControl()
{
    try {
        // 创建电机数据管理器
        motorDataManager = new MotorDataManager(this);

        // 创建多电机控制界面
        multiMotorInterface = new MultiMotorInterface(this);

        // 连接数据管理器到多电机界面
        multiMotorInterface->setDataManager(motorDataManager);

        // 连接多电机界面的信号
        connect(multiMotorInterface, &MultiMotorInterface::backToMainInterface,
                this, &MainWindow::onMultiMotorBack);
        connect(multiMotorInterface, &MultiMotorInterface::openOscilloscope,
                this, &MainWindow::onOpenOscilloscopeForMotor);

    } catch (const std::exception& e) {
        qDebug() << "多电机控制初始化异常:" << e.what();
    } catch (...) {
        qDebug() << "多电机控制初始化发生未知异常";
    }
}

/**
 * @brief 多电机控制按钮点击槽函数
 * @details 打开多电机控制界面
 */
/* 手动连接版本暂时不使用，避免重复调用
void MainWindow::onMultiMotorControlClicked()
{
    qDebug() << "按钮点击: 多电机控制";

    // 暂时显示功能开发中的提示，避免崩溃
    MessageBoxUtils::information(this, "多电机控制", "多电机控制功能正在开发中，敬请期待！\n\n为了系统稳定性，该功能暂时禁用。");

    // 原来的实现暂时注释掉，避免线程问题导致崩溃
    // 确保纯UI多电机界面已创建
    if (!multiMotorControlPure) {
        multiMotorControlPure = new MultiMotorControlPure(this);

        // 连接返回信号（暂时不连接，先测试稳定性）
        // connect(multiMotorControlPure, &MultiMotorControlPure::backToMainInterface,
        //         this, &MainWindow::onMultiMotorBack);
    }

    // 隐藏主界面内容
    ui->centralwidget->hide();

    // 将纯UI多电机界面设置为主窗口的中央部件
    setCentralWidget(multiMotorControlPure);

    // 确保多电机界面显示
    multiMotorControlPure->show();
}
*/

/**
 * @brief 处理多电机界面返回槽函数
 * @details 当多电机界面关闭时调用，重新显示主界面（独立窗口模式）
 */
void MainWindow::onMultiMotorBack()
{
    qDebug() << "多电机界面返回主界面";

    // 隐藏多电机控制界面
    if (multiMotorControlPure) {
        multiMotorControlPure->hide();
    }

    // 显示主界面
    this->show();
    this->raise();
    this->activateWindow();

    qDebug() << "主界面已重新显示";
}

/**
 * @brief 为特定电机打开示波器槽函数
 * @param motor_id 电机ID (1-6)
 */
void MainWindow::onOpenOscilloscopeForMotor(quint8 motor_id)
{
    qDebug() << "Opening oscilloscope for motor" << motor_id;

    // 确保示波器界面已创建
    if (!oscilloscopeInterface) {
        oscilloscopeInterface = new OscilloscopeInterface(this);
        connect(oscilloscopeInterface, &OscilloscopeInterface::backToMainInterface,
                this, &MainWindow::onOscilloscopeBack);
    }

    // 配置示波器显示特定电机的数据
    // 注意：这里需要根据您现有的示波器接口实现进行调整
    // if (motorDataManager) {
    //     oscilloscopeInterface->setMotorDataSource(motor_id, motorDataManager);
    // }

    // 显示示波器界面
    oscilloscopeInterface->show();
    oscilloscopeInterface->raise();
    oscilloscopeInterface->activateWindow();
}

/**
 * @brief 预初始化关键组件
 * @details 在程序启动时预先初始化关键组件，防止首次使用时崩溃
 */
void MainWindow::preInitializeCriticalComponents()
{
    qDebug() << "MainWindow: 开始预初始化关键组件";

    // 1. 预初始化Qt的元对象系统相关组件
    // 创建一个临时的QTimer来触发元对象系统初始化
    QTimer* tempTimer = new QTimer(this);
    tempTimer->setSingleShot(true);
    tempTimer->setInterval(1);
    delete tempTimer;  // 立即删除，只是为了触发初始化

    // 2. 预初始化一些关键的Qt类
    // 这些操作会触发相关类的静态初始化
    QWidget* tempWidget = new QWidget();
    delete tempWidget;

    // 3. 强制处理一次事件循环，确保所有延迟初始化完成
    QApplication::processEvents();

    // 4. 预初始化CANFD设备管理器（如果存在）
    // 这会触发相关静态变量的初始化
    try {
        // 只是访问一下类的静态成员，不实际创建实例
        qDebug() << "MainWindow: 预初始化CANFD相关组件";
    } catch (...) {
        qDebug() << "MainWindow: CANFD组件预初始化出现异常，但程序继续运行";
    }

    // 5. 预初始化一些全局资源
    // 确保所有静态变量都被正确初始化
    qDebug() << "MainWindow: 关键组件预初始化完成";
}
