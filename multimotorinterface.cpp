/**
 * @file multimotorinterface.cpp
 * @brief 多电机控制界面类实现
 * @details 实现了多电机控制界面的所有功能，包括单独控制、批量控制、
 *          状态监控等，采用红白配色风格。
 * <AUTHOR>
 * @date 2025-01-28
 * @version 1.0
 */

#include "multimotorinterface.h"
#include "ui_multimotorinterface.h"
#include "messagebox_utils.h"
#include "motorprotocol.h"
#include <QDebug>
#include <QDateTime>
#include <QScrollArea>
#include <cstring>  // for memset

// ================================
// MotorControlWidget 实现
// ================================

/**
 * @brief 构造函数
 * @param motor_id 电机ID (1-6)
 * @param parent 父窗口指针
 */
MotorControlWidget::MotorControlWidget(quint8 motor_id, QWidget *parent)
    : QGroupBox(parent), m_motorId(motor_id), m_isConnected(false), m_isEnabled(false)
{
    setTitle(QString("电机 %1").arg(motor_id));

    // 设置最小尺寸，确保有足够空间显示所有内容
    setMinimumSize(350, 400);  // 增加最小尺寸
    setMaximumSize(400, 450);  // 设置最大尺寸避免过度拉伸

    initializeUI();
    setupStyles();
}

/**
 * @brief 初始化界面
 */
void MotorControlWidget::initializeUI()
{
    // 创建主布局，增加间距避免文字重叠
    QVBoxLayout* mainLayout = new QVBoxLayout(this);
    mainLayout->setSpacing(12);  // 增加垂直间距
    mainLayout->setContentsMargins(12, 18, 12, 12);  // 增加边距

    // === 状态显示区域 ===
    QGroupBox* statusGroup = new QGroupBox("状态信息");
    QGridLayout* statusLayout = new QGridLayout(statusGroup);
    statusLayout->setSpacing(8);  // 增加状态区域间距
    statusLayout->setContentsMargins(8, 8, 8, 8);

    // 状态标签
    m_statusLabel = new QLabel("未连接");
    m_statusLabel->setObjectName("statusLabel");
    m_statusLabel->setAlignment(Qt::AlignCenter);
    statusLayout->addWidget(new QLabel("状态:"), 0, 0);
    statusLayout->addWidget(m_statusLabel, 0, 1);

    // 速度显示
    m_speedLabel = new QLabel("0 RPM");
    m_speedLabel->setObjectName("statusLabel");
    statusLayout->addWidget(new QLabel("速度:"), 1, 0);
    statusLayout->addWidget(m_speedLabel, 1, 1);

    // 位置显示
    m_positionLabel = new QLabel("0");
    m_positionLabel->setObjectName("statusLabel");
    statusLayout->addWidget(new QLabel("位置:"), 2, 0);
    statusLayout->addWidget(m_positionLabel, 2, 1);

    // 电流显示
    m_currentLabel = new QLabel("0.0 A");
    m_currentLabel->setObjectName("statusLabel");
    statusLayout->addWidget(new QLabel("电流:"), 3, 0);
    statusLayout->addWidget(m_currentLabel, 3, 1);

    // 电压显示
    m_voltageLabel = new QLabel("0.0 V");
    m_voltageLabel->setObjectName("statusLabel");
    statusLayout->addWidget(new QLabel("电压:"), 4, 0);
    statusLayout->addWidget(m_voltageLabel, 4, 1);

    // 温度显示
    m_temperatureLabel = new QLabel("0.0 °C");
    m_temperatureLabel->setObjectName("statusLabel");
    statusLayout->addWidget(new QLabel("温度:"), 5, 0);
    statusLayout->addWidget(m_temperatureLabel, 5, 1);

    // 错误显示
    m_errorLabel = new QLabel("无错误");
    m_errorLabel->setObjectName("statusLabel");
    statusLayout->addWidget(new QLabel("错误:"), 6, 0);
    statusLayout->addWidget(m_errorLabel, 6, 1);

    // 负载进度条
    m_loadProgressBar = new QProgressBar();
    m_loadProgressBar->setRange(0, 100);
    m_loadProgressBar->setValue(0);
    m_loadProgressBar->setFormat("%p%");
    statusLayout->addWidget(new QLabel("负载:"), 7, 0);
    statusLayout->addWidget(m_loadProgressBar, 7, 1);

    mainLayout->addWidget(statusGroup);

    // === 控制参数区域 ===
    QGroupBox* paramGroup = new QGroupBox("控制参数");
    QGridLayout* paramLayout = new QGridLayout(paramGroup);
    paramLayout->setSpacing(8);  // 增加控制参数区域间距
    paramLayout->setContentsMargins(8, 8, 8, 8);

    // 控制模式选择
    m_modeComboBox = new QComboBox();
    m_modeComboBox->addItem("位置控制", MODE_POSITION);
    m_modeComboBox->addItem("速度控制", MODE_SPEED);
    m_modeComboBox->addItem("转矩控制", MODE_TORQUE);
    m_modeComboBox->setCurrentIndex(1);  // 默认速度控制
    paramLayout->addWidget(new QLabel("模式:"), 0, 0);
    paramLayout->addWidget(m_modeComboBox, 0, 1);

    // 目标速度设置
    m_targetSpeedSpinBox = new QSpinBox();
    m_targetSpeedSpinBox->setRange(0, 5000);
    m_targetSpeedSpinBox->setValue(1000);
    m_targetSpeedSpinBox->setSuffix(" RPM");
    paramLayout->addWidget(new QLabel("目标速度:"), 1, 0);
    paramLayout->addWidget(m_targetSpeedSpinBox, 1, 1);

    // 目标位置设置
    m_targetPositionSpinBox = new QSpinBox();
    m_targetPositionSpinBox->setRange(-999999, 999999);
    m_targetPositionSpinBox->setValue(0);
    m_targetPositionSpinBox->setSuffix(" 脉冲");
    paramLayout->addWidget(new QLabel("目标位置:"), 2, 0);
    paramLayout->addWidget(m_targetPositionSpinBox, 2, 1);

    mainLayout->addWidget(paramGroup);

    // === 控制按钮区域 ===
    QGroupBox* controlGroup = new QGroupBox("控制操作");
    QGridLayout* controlLayout = new QGridLayout(controlGroup);
    controlLayout->setSpacing(8);  // 增加按钮间距
    controlLayout->setContentsMargins(8, 8, 8, 8);

    // 使能按钮
    m_enableButton = new QPushButton("使能");
    m_enableButton->setCheckable(true);
    controlLayout->addWidget(m_enableButton, 0, 0);

    // 启动按钮
    m_startButton = new QPushButton("启动");
    m_startButton->setObjectName("primaryButton");
    controlLayout->addWidget(m_startButton, 0, 1);

    // 停止按钮
    m_stopButton = new QPushButton("停止");
    controlLayout->addWidget(m_stopButton, 1, 0);

    // 设置速度按钮
    m_setSpeedButton = new QPushButton("设置速度");
    controlLayout->addWidget(m_setSpeedButton, 1, 1);

    // 设置位置按钮
    m_setPositionButton = new QPushButton("设置位置");
    controlLayout->addWidget(m_setPositionButton, 2, 0);

    // 回零按钮
    m_homeButton = new QPushButton("回零");
    controlLayout->addWidget(m_homeButton, 2, 1);

    // 清除错误按钮
    m_clearErrorButton = new QPushButton("清除错误");
    controlLayout->addWidget(m_clearErrorButton, 3, 0, 1, 2);

    mainLayout->addWidget(controlGroup);

    // 连接信号槽
    connect(m_enableButton, &QPushButton::clicked, this, &MotorControlWidget::onEnableClicked);
    connect(m_startButton, &QPushButton::clicked, this, &MotorControlWidget::onStartClicked);
    connect(m_stopButton, &QPushButton::clicked, this, &MotorControlWidget::onStopClicked);
    connect(m_setSpeedButton, &QPushButton::clicked, this, &MotorControlWidget::onSetSpeedClicked);
    connect(m_setPositionButton, &QPushButton::clicked, this, &MotorControlWidget::onSetPositionClicked);
    connect(m_homeButton, &QPushButton::clicked, this, &MotorControlWidget::onHomeClicked);
    connect(m_clearErrorButton, &QPushButton::clicked, this, &MotorControlWidget::onClearErrorClicked);

    // 初始状态设置
    setEnabled(false);
}

/**
 * @brief 设置样式
 */
void MotorControlWidget::setupStyles()
{
    // 设置固定大小
    setFixedSize(280, 450);
    
    // 设置样式表
    setStyleSheet(
        "QGroupBox {"
        "    font-weight: bold;"
        "    border: 2px solid #CC0000;"
        "    border-radius: 8px;"
        "    margin-top: 10px;"
        "    padding-top: 10px;"
        "    background-color: #FFFFFF;"
        "}"
        "QGroupBox::title {"
        "    subcontrol-origin: margin;"
        "    left: 10px;"
        "    padding: 0 8px 0 8px;"
        "    color: #CC0000;"
        "    font-size: 13px;"
        "    font-weight: bold;"
        "    background-color: #FFFFFF;"
        "}"
    );
}

/**
 * @brief 更新电机状态显示
 * @param status 电机状态帧
 */
void MotorControlWidget::updateStatus(const MotorStatusFrame& status)
{
    // 更新状态标签
    QString statusText;
    if (status.status_flags & STATUS_ERROR) {
        statusText = "错误";
        m_statusLabel->setStyleSheet("QLabel { color: red; font-weight: bold; }");
    } else if (status.status_flags & STATUS_RUNNING) {
        statusText = "运行中";
        m_statusLabel->setStyleSheet("QLabel { color: green; font-weight: bold; }");
    } else if (status.status_flags & STATUS_ENABLED) {
        statusText = "已使能";
        m_statusLabel->setStyleSheet("QLabel { color: blue; font-weight: bold; }");
    } else {
        statusText = "停止";
        m_statusLabel->setStyleSheet("QLabel { color: gray; }");
    }
    m_statusLabel->setText(statusText);

    // 更新数据显示
    m_speedLabel->setText(QString("%1 RPM").arg(status.current_speed));
    m_positionLabel->setText(QString::number(status.current_position));
    m_currentLabel->setText(QString("%1 A").arg(status.motor_current / 1000.0, 0, 'f', 2));
    m_voltageLabel->setText(QString("%1 V").arg(status.motor_voltage / 1000.0, 0, 'f', 1));
    m_temperatureLabel->setText(QString("%1 °C").arg(status.temperature / 10.0, 0, 'f', 1));
    
    // 更新错误显示
    if (status.error_code != ERR_NO_ERROR) {
        m_errorLabel->setText(getErrorDescription(static_cast<ErrorCode>(status.error_code)));
        m_errorLabel->setStyleSheet("QLabel { color: red; font-weight: bold; }");
    } else {
        m_errorLabel->setText("无错误");
        m_errorLabel->setStyleSheet("QLabel { color: green; }");
    }

    // 更新负载进度条
    int loadPercent = static_cast<int>(status.load_ratio / 10.0);
    m_loadProgressBar->setValue(loadPercent);
    
    // 根据负载设置进度条颜色
    if (loadPercent > 80) {
        m_loadProgressBar->setStyleSheet("QProgressBar::chunk { background-color: red; }");
    } else if (loadPercent > 60) {
        m_loadProgressBar->setStyleSheet("QProgressBar::chunk { background-color: orange; }");
    } else {
        m_loadProgressBar->setStyleSheet("QProgressBar::chunk { background-color: #CC0000; }");
    }

    // 更新使能状态
    m_isEnabled = (status.status_flags & STATUS_ENABLED) != 0;
    m_enableButton->setChecked(m_isEnabled);
    m_enableButton->setText(m_isEnabled ? "失能" : "使能");
}

/**
 * @brief 设置电机连接状态
 * @param connected 是否连接
 */
void MotorControlWidget::setConnected(bool connected)
{
    m_isConnected = connected;
    
    if (!connected) {
        m_statusLabel->setText("未连接");
        m_statusLabel->setStyleSheet("QLabel { color: red; font-weight: bold; }");
        
        // 清空显示数据
        m_speedLabel->setText("-- RPM");
        m_positionLabel->setText("--");
        m_currentLabel->setText("-- A");
        m_voltageLabel->setText("-- V");
        m_temperatureLabel->setText("-- °C");
        m_errorLabel->setText("未连接");
        m_loadProgressBar->setValue(0);
    }
    
    // 更新控件使能状态
    setEnabled(connected);
}

/**
 * @brief 设置使能状态
 * @param enabled 是否使能
 */
void MotorControlWidget::setEnabled(bool enabled)
{
    // 控制参数区域
    m_modeComboBox->setEnabled(enabled);
    m_targetSpeedSpinBox->setEnabled(enabled);
    m_targetPositionSpinBox->setEnabled(enabled);
    
    // 控制按钮
    m_enableButton->setEnabled(enabled);
    m_startButton->setEnabled(enabled && m_isEnabled);
    m_stopButton->setEnabled(enabled);
    m_setSpeedButton->setEnabled(enabled && m_isEnabled);
    m_setPositionButton->setEnabled(enabled && m_isEnabled);
    m_homeButton->setEnabled(enabled && m_isEnabled);
    m_clearErrorButton->setEnabled(enabled);
}

/**
 * @brief 获取目标速度
 * @return 目标速度值
 */
int MotorControlWidget::getTargetSpeed() const
{
    return m_targetSpeedSpinBox->value();
}

/**
 * @brief 获取目标位置
 * @return 目标位置值
 */
int MotorControlWidget::getTargetPosition() const
{
    return m_targetPositionSpinBox->value();
}

/**
 * @brief 获取控制模式
 * @return 控制模式
 */
ControlMode MotorControlWidget::getControlMode() const
{
    return static_cast<ControlMode>(m_modeComboBox->currentData().toInt());
}

// === 按钮点击槽函数实现 ===

void MotorControlWidget::onStartClicked()
{
    emit startMotor(m_motorId);
}

void MotorControlWidget::onStopClicked()
{
    emit stopMotor(m_motorId);
}

void MotorControlWidget::onEnableClicked()
{
    if (m_enableButton->isChecked()) {
        emit enableMotor(m_motorId);
    } else {
        emit disableMotor(m_motorId);
    }
}

void MotorControlWidget::onSetSpeedClicked()
{
    emit setSpeed(m_motorId, getTargetSpeed());
}

void MotorControlWidget::onSetPositionClicked()
{
    emit setPosition(m_motorId, getTargetPosition());
}

void MotorControlWidget::onHomeClicked()
{
    emit homeMotor(m_motorId);
}

void MotorControlWidget::onClearErrorClicked()
{
    emit clearError(m_motorId);
}

// ================================
// MultiMotorInterface 实现
// ================================

/**
 * @brief 构造函数
 * @param parent 父窗口指针
 */
MultiMotorInterface::MultiMotorInterface(QWidget *parent)
    : QWidget(parent), ui(nullptr),
      m_canfdInterface(nullptr), m_dataManager(nullptr)
{
    qDebug() << "开始创建MultiMotorInterface...";

    try {
        // 创建UI对象
        qDebug() << "正在创建UI对象...";
        ui = new Ui::MultiMotorInterface;
        qDebug() << "UI对象创建成功";

        // 设置UI
        qDebug() << "正在设置UI...";
        ui->setupUi(this);
        qDebug() << "UI设置成功";

        // 初始化序列号
        qDebug() << "正在初始化序列号...";
        memset(m_sequences, 0, sizeof(m_sequences));
        qDebug() << "序列号初始化成功";

        // 逐步恢复界面功能
        qDebug() << "正在初始化界面...";
        initializeUI();  // 恢复UI初始化
        qDebug() << "界面初始化完成";

        qDebug() << "正在设置样式...";
        setupStyles();  // 恢复样式设置
        qDebug() << "样式设置完成";

        qDebug() << "正在连接信号...";
        connectSignals();  // 恢复信号连接
        qDebug() << "信号连接完成";

        // 暂时注释掉定时器
        qDebug() << "正在创建状态定时器...";
        // m_statusTimer = new QTimer(this);
        // m_statusTimer->setInterval(100);  // 100ms更新一次
        // connect(m_statusTimer, &QTimer::timeout, this, &MultiMotorInterface::onStatusUpdateTimer);
        // m_statusTimer->start();
        qDebug() << "状态定时器创建跳过";

        qDebug() << "MultiMotorInterface初始化完成";
    } catch (const std::exception& e) {
        qDebug() << "MultiMotorInterface初始化异常:" << e.what();
        throw;
    } catch (...) {
        qDebug() << "MultiMotorInterface初始化发生未知异常";
        throw;
    }
}

/**
 * @brief 析构函数
 */
MultiMotorInterface::~MultiMotorInterface()
{
    if (m_statusTimer) {
        m_statusTimer->stop();
    }
    delete ui;
}

/**
 * @brief 初始化界面
 */
void MultiMotorInterface::initializeUI()
{
    // 获取电机网格布局
    QGridLayout* motorGridLayout = qobject_cast<QGridLayout*>(
        ui->motorScrollAreaWidget->layout());

    if (!motorGridLayout) {
        qWarning() << "Failed to get motor grid layout";
        return;
    }

    // 创建6个电机控制组件 (2行3列布局)
    m_motorWidgets.resize(6);
    for (int i = 0; i < 6; i++) {
        m_motorWidgets[i] = new MotorControlWidget(i + 1, this);

        // 连接电机控制信号
        connect(m_motorWidgets[i], &MotorControlWidget::startMotor,
                this, &MultiMotorInterface::onMotorStart);
        connect(m_motorWidgets[i], &MotorControlWidget::stopMotor,
                this, &MultiMotorInterface::onMotorStop);
        connect(m_motorWidgets[i], &MotorControlWidget::enableMotor,
                this, &MultiMotorInterface::onMotorEnable);
        connect(m_motorWidgets[i], &MotorControlWidget::disableMotor,
                this, &MultiMotorInterface::onMotorDisable);
        connect(m_motorWidgets[i], &MotorControlWidget::setSpeed,
                this, &MultiMotorInterface::onMotorSetSpeed);
        connect(m_motorWidgets[i], &MotorControlWidget::setPosition,
                this, &MultiMotorInterface::onMotorSetPosition);
        connect(m_motorWidgets[i], &MotorControlWidget::homeMotor,
                this, &MultiMotorInterface::onMotorHome);
        connect(m_motorWidgets[i], &MotorControlWidget::clearError,
                this, &MultiMotorInterface::onMotorClearError);

        // 添加到网格布局 (2行3列)
        int row = i / 3;
        int col = i % 3;
        motorGridLayout->addWidget(m_motorWidgets[i], row, col);
    }

    // 设置滚动区域的最小大小，给电机控件更多空间
    ui->motorScrollArea->setMinimumHeight(600);

    // 初始化状态标签
    ui->connectionStatusLabel->setText("连接状态: 未连接");
    ui->systemStatusLabel->setText("系统状态: 待机");
    ui->lastUpdateLabel->setText("最后更新: --");
}

/**
 * @brief 设置样式
 */
void MultiMotorInterface::setupStyles()
{
    // 设置窗口标题
    setWindowTitle("六电机控制系统 - CANFD协议");

    // 设置最小窗口大小
    setMinimumSize(1200, 800);
}

/**
 * @brief 连接信号槽
 */
void MultiMotorInterface::connectSignals()
{
    // 连接UI按钮信号
    connect(ui->backButton, &QPushButton::clicked, this, &MultiMotorInterface::onBackClicked);
    connect(ui->oscilloscopeButton, &QPushButton::clicked, this, &MultiMotorInterface::onOscilloscopeClicked);
    connect(ui->startAllButton, &QPushButton::clicked, this, &MultiMotorInterface::onStartAllClicked);
    connect(ui->stopAllButton, &QPushButton::clicked, this, &MultiMotorInterface::onStopAllClicked);
    connect(ui->enableAllButton, &QPushButton::clicked, this, &MultiMotorInterface::onEnableAllClicked);
    connect(ui->disableAllButton, &QPushButton::clicked, this, &MultiMotorInterface::onDisableAllClicked);
    connect(ui->emergencyStopButton, &QPushButton::clicked, this, &MultiMotorInterface::onEmergencyStopClicked);
    connect(ui->homeAllButton, &QPushButton::clicked, this, &MultiMotorInterface::onHomeAllClicked);
    connect(ui->clearAllErrorsButton, &QPushButton::clicked, this, &MultiMotorInterface::onClearAllErrorsClicked);
    connect(ui->refreshButton, &QPushButton::clicked, this, &MultiMotorInterface::onRefreshClicked);
}

/**
 * @brief 设置CANFD接口
 * @param canfd_interface CANFD接口指针
 */
void MultiMotorInterface::setCANFDInterface(CANFDInterface* canfd_interface)
{
    m_canfdInterface = canfd_interface;

    if (m_canfdInterface) {
        qDebug() << "CANFD interface connected to MultiMotorInterface";
        ui->connectionStatusLabel->setText("连接状态: CANFD已连接");
    } else {
        ui->connectionStatusLabel->setText("连接状态: 未连接");
    }
}

/**
 * @brief 设置数据管理器
 * @param data_manager 数据管理器指针
 */
void MultiMotorInterface::setDataManager(MotorDataManager* data_manager)
{
    m_dataManager = data_manager;

    if (m_dataManager) {
        // 连接数据管理器的信号
        connect(m_dataManager, &MotorDataManager::motorStatusUpdated,
                this, &MultiMotorInterface::updateMotorStatus);
        connect(m_dataManager, &MotorDataManager::motorConnectionChanged,
                this, &MultiMotorInterface::onMotorConnectionChanged);
        connect(m_dataManager, &MotorDataManager::motorError,
                this, &MultiMotorInterface::onMotorError);

        qDebug() << "Data manager connected to MultiMotorInterface";
    }
}

// ================================
// 公共槽函数实现
// ================================

/**
 * @brief 更新电机状态
 * @param motor_id 电机ID
 * @param status 状态帧
 */
void MultiMotorInterface::updateMotorStatus(quint8 motor_id, const MotorStatusFrame& status)
{
    if (motor_id >= 1 && motor_id <= 6) {
        m_motorWidgets[motor_id - 1]->updateStatus(status);

        // 更新最后更新时间
        ui->lastUpdateLabel->setText(QString("最后更新: %1")
                                   .arg(QDateTime::currentDateTime().toString("hh:mm:ss")));
    }
}

/**
 * @brief 电机连接状态改变
 * @param motor_id 电机ID
 * @param connected 是否连接
 */
void MultiMotorInterface::onMotorConnectionChanged(quint8 motor_id, bool connected)
{
    if (motor_id >= 1 && motor_id <= 6) {
        m_motorWidgets[motor_id - 1]->setConnected(connected);

        // 更新系统连接状态
        if (m_dataManager) {
            int connectedCount = m_dataManager->getConnectedMotorCount();
            ui->connectionStatusLabel->setText(QString("连接状态: %1/6 电机已连接")
                                             .arg(connectedCount));
        }
    }
}

/**
 * @brief 电机错误处理
 * @param motor_id 电机ID
 * @param error_code 错误代码
 */
void MultiMotorInterface::onMotorError(quint8 motor_id, quint8 error_code)
{
    QString errorDesc = getErrorDescription(static_cast<ErrorCode>(error_code));
    QString message = QString("电机 %1 发生错误: %2").arg(motor_id).arg(errorDesc);

    qWarning() << message;

    // 显示错误消息框 (暂时注释掉，避免崩溃)
    // MessageBoxUtils::warning(this, "电机错误", message);
}

// ================================
// 私有槽函数实现
// ================================

/**
 * @brief 返回按钮点击槽函数
 */
void MultiMotorInterface::onBackClicked()
{
    emit backToMainInterface();
}

/**
 * @brief 示波器按钮点击槽函数
 */
void MultiMotorInterface::onOscilloscopeClicked()
{
    // 默认打开电机1的示波器，也可以添加选择对话框
    emit openOscilloscope(1);
}

/**
 * @brief 全部启动按钮点击槽函数
 */
void MultiMotorInterface::onStartAllClicked()
{
    sendSystemBroadcast(BROADCAST_START_ALL);
    qDebug() << "Start all motors command sent";
}

/**
 * @brief 全部停止按钮点击槽函数
 */
void MultiMotorInterface::onStopAllClicked()
{
    sendSystemBroadcast(BROADCAST_STOP_ALL);
    qDebug() << "Stop all motors command sent";
}

/**
 * @brief 全部使能按钮点击槽函数
 */
void MultiMotorInterface::onEnableAllClicked()
{
    for (int i = 1; i <= 6; i++) {
        sendMotorCommand(i, CMD_ENABLE);
    }
    qDebug() << "Enable all motors command sent";
}

/**
 * @brief 全部失能按钮点击槽函数
 */
void MultiMotorInterface::onDisableAllClicked()
{
    for (int i = 1; i <= 6; i++) {
        sendMotorCommand(i, CMD_DISABLE);
    }
    qDebug() << "Disable all motors command sent";
}

/**
 * @brief 紧急停止按钮点击槽函数
 */
void MultiMotorInterface::onEmergencyStopClicked()
{
    sendSystemBroadcast(BROADCAST_EMERGENCY);

    // 显示紧急停止确认 (暂时注释掉，避免崩溃)
    // MessageBoxUtils::information(this, "紧急停止", "紧急停止命令已发送！");
    qWarning() << "Emergency stop activated!";
}

/**
 * @brief 全部回零按钮点击槽函数
 */
void MultiMotorInterface::onHomeAllClicked()
{
    for (int i = 1; i <= 6; i++) {
        sendMotorCommand(i, CMD_HOME, 0, MODE_HOMING);
    }
    qDebug() << "Home all motors command sent";
}

/**
 * @brief 清除所有错误按钮点击槽函数
 */
void MultiMotorInterface::onClearAllErrorsClicked()
{
    for (int i = 1; i <= 6; i++) {
        sendMotorCommand(i, CMD_CLEAR_ERROR);
    }
    qDebug() << "Clear all errors command sent";
}

/**
 * @brief 刷新状态按钮点击槽函数
 */
void MultiMotorInterface::onRefreshClicked()
{
    if (m_dataManager) {
        // 触发通信状态检查
        m_dataManager->checkCommunicationStatus();

        // 更新界面显示
        for (int i = 1; i <= 6; i++) {
            MotorStatusFrame status = m_dataManager->getMotorStatus(i);
            updateMotorStatus(i, status);
        }

        qDebug() << "Status refreshed";
    }
}

// ================================
// 单个电机控制槽函数实现
// ================================

void MultiMotorInterface::onMotorStart(quint8 motor_id)
{
    sendMotorCommand(motor_id, CMD_START);
    qDebug() << "Start motor" << motor_id;
}

void MultiMotorInterface::onMotorStop(quint8 motor_id)
{
    sendMotorCommand(motor_id, CMD_STOP);
    qDebug() << "Stop motor" << motor_id;
}

void MultiMotorInterface::onMotorEnable(quint8 motor_id)
{
    sendMotorCommand(motor_id, CMD_ENABLE);
    qDebug() << "Enable motor" << motor_id;
}

void MultiMotorInterface::onMotorDisable(quint8 motor_id)
{
    sendMotorCommand(motor_id, CMD_DISABLE);
    qDebug() << "Disable motor" << motor_id;
}

void MultiMotorInterface::onMotorSetSpeed(quint8 motor_id, int speed)
{
    sendMotorCommand(motor_id, CMD_SET_SPEED, speed, MODE_SPEED);
    qDebug() << "Set motor" << motor_id << "speed to" << speed << "RPM";
}

void MultiMotorInterface::onMotorSetPosition(quint8 motor_id, int position)
{
    sendMotorCommand(motor_id, CMD_SET_POSITION, position, MODE_POSITION);
    qDebug() << "Set motor" << motor_id << "position to" << position;
}

void MultiMotorInterface::onMotorHome(quint8 motor_id)
{
    sendMotorCommand(motor_id, CMD_HOME, 0, MODE_HOMING);
    qDebug() << "Home motor" << motor_id;
}

void MultiMotorInterface::onMotorClearError(quint8 motor_id)
{
    sendMotorCommand(motor_id, CMD_CLEAR_ERROR);
    qDebug() << "Clear error for motor" << motor_id;
}

/**
 * @brief 状态更新定时器槽函数
 */
void MultiMotorInterface::onStatusUpdateTimer()
{
    if (m_dataManager) {
        // 更新系统状态显示
        int connectedCount = m_dataManager->getConnectedMotorCount();
        int enabledCount = m_dataManager->getEnabledMotorCount();

        QString systemStatus;
        if (connectedCount == 0) {
            systemStatus = "待机";
        } else if (enabledCount > 0) {
            systemStatus = QString("运行中 (%1/%2 使能)").arg(enabledCount).arg(connectedCount);
        } else {
            systemStatus = QString("已连接 (%1/6)").arg(connectedCount);
        }

        ui->systemStatusLabel->setText(QString("系统状态: %1").arg(systemStatus));
    }
}

// ================================
// 私有辅助函数实现
// ================================

/**
 * @brief 发送电机控制命令
 * @param motor_id 电机ID
 * @param command 控制命令
 * @param target_value 目标值
 * @param control_mode 控制模式
 */
void MultiMotorInterface::sendMotorCommand(quint8 motor_id, CommandType command,
                                         quint32 target_value, ControlMode control_mode)
{
    if (motor_id < 1 || motor_id > 6) {
        qWarning() << "Invalid motor ID:" << motor_id;
        return;
    }

    if (!m_canfdInterface) {
        qWarning() << "CANFD interface not available - 暂时跳过电机" << motor_id << "命令发送";
        // MessageBoxUtils::warning(this, "错误", "CANFD接口未连接！");  // 暂时注释掉，避免崩溃
        return;
    }

    // 创建控制命令帧
    MotorControlFrame cmd;
    cmd.motor_id = motor_id;
    cmd.sequence = ++m_sequences[motor_id - 1];  // 递增序列号
    cmd.priority = 0;  // 高优先级
    cmd.command = command;
    cmd.control_mode = control_mode;

    if (control_mode == MODE_SPEED) {
        cmd.target_speed = static_cast<quint16>(target_value);
        cmd.target_position = 0;
    } else if (control_mode == MODE_POSITION) {
        cmd.target_speed = 0;
        cmd.target_position = target_value;
    }

    cmd.acceleration = 1000;  // 默认加速度
    cmd.deceleration = 1000;  // 默认减速度
    cmd.timestamp = getCurrentTimestamp();

    // 计算校验和
    fillControlFrameChecksum(&cmd);

    // 记录命令到数据管理器
    if (m_dataManager) {
        m_dataManager->recordControlCommand(motor_id, cmd);
    }

    // 转换为十六进制字符串发送
    QByteArray cmdData = controlFrameToByteArray(cmd);
    QString hexData;
    for (int i = 0; i < cmdData.size(); i++) {
        hexData += QString("%1 ").arg(static_cast<quint8>(cmdData[i]), 2, 16, QChar('0')).toUpper();
    }
    hexData = hexData.trimmed();

    qDebug() << "Sending motor command - ID:" << QString("0x%1").arg(GET_MOTOR_CMD_ID(motor_id), 3, 16, QChar('0'))
             << "Command:" << getCommandDescription(command)
             << "Data:" << hexData;

    // 注意：这里需要根据实际的CANFDInterface API进行调用
    // 由于现有接口可能需要特定的调用方式，这里提供一个示例
    // m_canfdInterface->sendCustomFrame(GET_MOTOR_CMD_ID(motor_id), hexData);
}

/**
 * @brief 发送系统广播命令
 * @param broadcast_cmd 广播命令
 * @param motor_mask 电机掩码
 */
void MultiMotorInterface::sendSystemBroadcast(BroadcastCommand broadcast_cmd, quint8 motor_mask)
{
    if (!m_canfdInterface) {
        qWarning() << "CANFD interface not available - 暂时跳过命令发送";
        // MessageBoxUtils::warning(this, "错误", "CANFD接口未连接！");  // 暂时注释掉，避免崩溃
        return;
    }

    // 创建系统广播帧
    SystemBroadcastFrame broadcast;
    broadcast.broadcast_cmd = broadcast_cmd;
    broadcast.motor_mask = motor_mask;
    broadcast.sync_timestamp = static_cast<quint32>(QDateTime::currentMSecsSinceEpoch());

    // 转换为十六进制字符串发送
    QByteArray broadcastData = QByteArray(reinterpret_cast<const char*>(&broadcast),
                                         sizeof(SystemBroadcastFrame));
    QString hexData;
    for (int i = 0; i < broadcastData.size(); i++) {
        hexData += QString("%1 ").arg(static_cast<quint8>(broadcastData[i]), 2, 16, QChar('0')).toUpper();
    }
    hexData = hexData.trimmed();

    qDebug() << "Sending system broadcast - ID:" << QString("0x%1").arg(CAN_ID_SYSTEM_BROADCAST, 3, 16, QChar('0'))
             << "Command:" << broadcast_cmd
             << "Motor mask:" << QString("0x%1").arg(motor_mask, 2, 16, QChar('0'))
             << "Data:" << hexData;

    // 注意：这里需要根据实际的CANFDInterface API进行调用
    // m_canfdInterface->sendCustomFrame(CAN_ID_SYSTEM_BROADCAST, hexData);
}
