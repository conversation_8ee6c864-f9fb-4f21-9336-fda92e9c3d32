/**
 * @file canfd_protocol_example.c
 * @brief CANFD协议使用示例 (下位机端)
 * @details 演示如何在下位机中使用CANFD协议进行电机控制
 * @version V1.0
 * @date 2025-01-28
 * <AUTHOR>
 */

#include "motor_canfd_protocol.h"
#include <stdio.h>
#include <string.h>

// 假设的CAN硬件接口函数
extern void CAN_Init(void);
extern bool CAN_SendFrame(uint32_t id, const uint8_t* data, uint8_t length);
extern bool CAN_ReceiveFrame(uint32_t* id, uint8_t* data, uint8_t* length);

// 全局协议管理器
static ProtocolManager g_protocol_manager;

// 模拟的电机硬件接口
typedef struct {
    bool enabled;
    bool running;
    uint16_t current_speed;
    uint32_t current_position;
    uint16_t target_speed;
    uint32_t target_position;
} MotorHardware;

static MotorHardware g_motors[MOTOR_COUNT];

// ================================
// 硬件抽象层函数
// ================================

/**
 * @brief 初始化电机硬件
 */
void motor_hardware_init(void)
{
    for (uint8_t i = 0; i < MOTOR_COUNT; i++) {
        memset(&g_motors[i], 0, sizeof(MotorHardware));
    }
}

/**
 * @brief 控制电机使能
 * @param motor_id 电机ID (1-6)
 * @param enable 是否使能
 */
void motor_set_enable(uint8_t motor_id, bool enable)
{
    if (motor_id >= 1 && motor_id <= MOTOR_COUNT) {
        g_motors[motor_id - 1].enabled = enable;
        if (!enable) {
            g_motors[motor_id - 1].running = false;
        }
    }
}

/**
 * @brief 启动/停止电机
 * @param motor_id 电机ID (1-6)
 * @param start 是否启动
 */
void motor_set_running(uint8_t motor_id, bool start)
{
    if (motor_id >= 1 && motor_id <= MOTOR_COUNT) {
        if (g_motors[motor_id - 1].enabled) {
            g_motors[motor_id - 1].running = start;
        }
    }
}

/**
 * @brief 设置电机目标速度
 * @param motor_id 电机ID (1-6)
 * @param speed 目标速度 (RPM)
 */
void motor_set_target_speed(uint8_t motor_id, uint16_t speed)
{
    if (motor_id >= 1 && motor_id <= MOTOR_COUNT) {
        g_motors[motor_id - 1].target_speed = speed;
    }
}

/**
 * @brief 获取电机当前速度
 * @param motor_id 电机ID (1-6)
 * @return 当前速度 (RPM)
 */
uint16_t motor_get_current_speed(uint8_t motor_id)
{
    if (motor_id >= 1 && motor_id <= MOTOR_COUNT) {
        // 模拟速度逐渐接近目标速度
        MotorHardware* motor = &g_motors[motor_id - 1];
        if (motor->running) {
            if (motor->current_speed < motor->target_speed) {
                motor->current_speed += 10; // 每次增加10RPM
                if (motor->current_speed > motor->target_speed) {
                    motor->current_speed = motor->target_speed;
                }
            } else if (motor->current_speed > motor->target_speed) {
                motor->current_speed -= 10; // 每次减少10RPM
                if (motor->current_speed < motor->target_speed) {
                    motor->current_speed = motor->target_speed;
                }
            }
        } else {
            motor->current_speed = 0;
        }
        return motor->current_speed;
    }
    return 0;
}

// ================================
// 协议处理函数
// ================================

/**
 * @brief 处理接收到的控制命令
 * @param frame 控制命令帧
 */
void handle_control_command(const MotorControlFrame* frame)
{
    // 使用协议管理器处理命令
    if (process_control_command(&g_protocol_manager, frame)) {
        uint8_t motor_id = frame->motor_id;
        
        // 根据命令类型执行硬件操作
        switch (frame->command) {
            case CMD_ENABLE:
                motor_set_enable(motor_id, true);
                printf("Motor %d enabled\n", motor_id);
                break;
                
            case CMD_DISABLE:
                motor_set_enable(motor_id, false);
                printf("Motor %d disabled\n", motor_id);
                break;
                
            case CMD_START:
                motor_set_running(motor_id, true);
                printf("Motor %d started\n", motor_id);
                break;
                
            case CMD_STOP:
                motor_set_running(motor_id, false);
                printf("Motor %d stopped\n", motor_id);
                break;
                
            case CMD_SET_SPEED:
                motor_set_target_speed(motor_id, frame->target_speed);
                printf("Motor %d target speed set to %d RPM\n", motor_id, frame->target_speed);
                break;
                
            default:
                printf("Unknown command: 0x%02X\n", frame->command);
                break;
        }
    }
}

/**
 * @brief 更新电机状态并发送状态帧
 * @param motor_id 电机ID (1-6)
 */
void update_and_send_motor_status(uint8_t motor_id)
{
    if (motor_id < 1 || motor_id > MOTOR_COUNT) {
        return;
    }
    
    // 获取当前状态
    const MotorStatusFrame* current_status = get_motor_status(&g_protocol_manager, motor_id);
    if (current_status == NULL) {
        return;
    }
    
    // 创建新的状态帧
    MotorStatusFrame status = *current_status;
    
    // 更新实时数据
    status.current_speed = motor_get_current_speed(motor_id);
    status.current_position += status.current_speed / 60; // 简单的位置积分
    status.motor_current = status.current_speed * 2;      // 模拟电流与速度成正比
    status.motor_voltage = 24000;                         // 固定24V
    status.temperature = 250 + (status.current_speed / 10); // 模拟温度
    status.torque = status.motor_current / 10;            // 模拟转矩
    status.encoder_position = status.current_position;
    status.following_error = 0;
    status.load_ratio = (status.current_speed * 1000) / 3000; // 模拟负载率
    
    // 更新状态标志
    MotorHardware* motor = &g_motors[motor_id - 1];
    status.status_flags = 0;
    if (motor->enabled) status.status_flags |= STATUS_ENABLED;
    if (motor->running) status.status_flags |= STATUS_RUNNING;
    if (motor->current_speed > 0) status.status_flags |= STATUS_MOVING;
    if (motor->current_speed == motor->target_speed && motor->target_speed > 0) {
        status.status_flags |= STATUS_TARGET_REACHED;
    }
    
    // 填充时间戳和校验和
    status.timestamp = get_current_timestamp() & 0xFFFF;
    fill_status_frame_checksum(&status);
    
    // 更新协议管理器中的状态
    update_motor_status(&g_protocol_manager, motor_id, &status);
    
    // 发送状态帧
    uint32_t can_id = GET_MOTOR_STATUS_ID(motor_id);
    CAN_SendFrame(can_id, (uint8_t*)&status, sizeof(MotorStatusFrame));
}

/**
 * @brief CAN接收中断处理函数
 */
void CAN_RX_IRQHandler(void)
{
    uint32_t can_id;
    uint8_t data[64];
    uint8_t length;
    
    if (CAN_ReceiveFrame(&can_id, data, &length)) {
        // 检查是否是电机控制命令
        if (can_id >= CAN_ID_MOTOR_CMD_BASE && 
            can_id <= CAN_ID_MOTOR_CMD_BASE + MOTOR_COUNT &&
            length == sizeof(MotorControlFrame)) {
            
            MotorControlFrame* cmd = (MotorControlFrame*)data;
            handle_control_command(cmd);
        }
        // 检查是否是紧急停止命令
        else if (can_id == CAN_ID_EMERGENCY_STOP) {
            // 立即停止所有电机
            for (uint8_t i = 1; i <= MOTOR_COUNT; i++) {
                motor_set_running(i, false);
            }
            printf("Emergency stop activated!\n");
        }
    }
}

/**
 * @brief 定时器中断处理函数 (10ms周期)
 */
void Timer_10ms_IRQHandler(void)
{
    static uint8_t counter = 0;
    
    // 每10ms更新一次所有电机状态
    for (uint8_t i = 1; i <= MOTOR_COUNT; i++) {
        update_and_send_motor_status(i);
    }
    
    // 每100ms检查一次通信超时
    if (++counter >= 10) {
        counter = 0;
        uint8_t timeout_mask = check_communication_timeout(&g_protocol_manager, 100);
        if (timeout_mask != 0) {
            printf("Communication timeout detected: 0x%02X\n", timeout_mask);
            // 可以在这里实现超时处理逻辑
        }
    }
}

/**
 * @brief 主函数
 */
int main(void)
{
    printf("Motor CANFD Protocol Example Started\n");
    
    // 初始化硬件
    CAN_Init();
    motor_hardware_init();
    
    // 初始化协议管理器
    init_protocol_manager(&g_protocol_manager);
    
    printf("System initialized, waiting for commands...\n");
    
    // 主循环
    while (1) {
        // 在实际应用中，这里可能是空的，
        // 因为所有处理都在中断中完成
        
        // 可以添加一些后台任务，如：
        // - 系统监控
        // - 参数保存
        // - 诊断信息更新
        // - LED状态指示等
        
        // 简单的延时
        for (volatile int i = 0; i < 100000; i++);
    }
    
    return 0;
}
