/**
 * @file common_datastructures.h
 * @brief 通用数据结构定义
 * @details 定义了项目中通用的数据结构，避免重复定义问题
 * <AUTHOR>
 * @date 2025-01-28
 * @version 1.0
 */

#ifndef COMMON_DATASTRUCTURES_H
#define COMMON_DATASTRUCTURES_H

#include <QVector>
#include <vector>

// ================================
// 电机数据管理器命名空间
// ================================
namespace MotorData {

/**
 * @brief 数据点结构
 * @details 包含时间戳的数据点，用于历史数据存储
 */
struct DataPoint {
    double value;           ///< 数据值
    qint64 timestamp;       ///< 时间戳(毫秒)
    
    DataPoint() : value(0.0), timestamp(0) {}
    DataPoint(double v, qint64 t) : value(v), timestamp(t) {}
};

/**
 * @brief 循环缓冲区模板类
 * @tparam T 数据类型
 * @details 用于存储历史数据，支持固定大小的循环存储
 */
template<typename T>
class CircularBuffer
{
public:
    /**
     * @brief 构造函数
     * @param capacity 缓冲区容量
     */
    explicit CircularBuffer(int capacity = 1000) 
        : m_capacity(capacity), m_size(0), m_head(0) {
        m_data.resize(capacity);
    }
    
    /**
     * @brief 添加数据
     * @param value 数据值
     */
    void append(const T& value) {
        m_data[m_head] = value;
        m_head = (m_head + 1) % m_capacity;
        if (m_size < m_capacity) {
            m_size++;
        }
    }
    
    /**
     * @brief 获取最新的N个数据
     * @param count 数据个数
     * @return 数据向量
     */
    QVector<T> getLatest(int count) const {
        QVector<T> result;
        if (count <= 0 || m_size == 0) return result;
        
        int actualCount = qMin(count, m_size);
        result.reserve(actualCount);
        
        int start = (m_head - actualCount + m_capacity) % m_capacity;
        for (int i = 0; i < actualCount; i++) {
            result.append(m_data[(start + i) % m_capacity]);
        }
        
        return result;
    }
    
    /**
     * @brief 获取所有数据
     * @return 所有数据的向量
     */
    QVector<T> getAll() const {
        return getLatest(m_size);
    }
    
    /**
     * @brief 清空缓冲区
     */
    void clear() {
        m_size = 0;
        m_head = 0;
    }
    
    /**
     * @brief 获取当前大小
     * @return 当前数据个数
     */
    int size() const { return m_size; }
    
    /**
     * @brief 获取容量
     * @return 缓冲区容量
     */
    int capacity() const { return m_capacity; }
    
    /**
     * @brief 是否为空
     * @return 是否为空
     */
    bool isEmpty() const { return m_size == 0; }
    
    /**
     * @brief 是否已满
     * @return 是否已满
     */
    bool isFull() const { return m_size == m_capacity; }

private:
    QVector<T> m_data;      ///< 数据存储
    int m_capacity;         ///< 缓冲区容量
    int m_size;             ///< 当前大小
    int m_head;             ///< 头指针
};

} // namespace MotorData

// ================================
// 示波器命名空间
// ================================
namespace Oscilloscope {

/**
 * @brief 数据点结构体
 * @details 存储单个数据采样点的信息
 */
struct DataPoint {
    double timestamp;       ///< 时间戳 (秒)
    double value;          ///< 数值
    bool isValid;          ///< 数据有效性标志

    DataPoint() : timestamp(0.0), value(0.0), isValid(false) {}
    DataPoint(double t, double v, bool valid = true) : timestamp(t), value(v), isValid(valid) {}
};

/**
 * @class CircularBuffer
 * @brief 环形缓冲区类
 * @details 优化的环形缓冲区实现，避免频繁的内存分配和释放
 */
template<typename T>
class CircularBuffer {
private:
    std::vector<T> buffer;
    size_t head;
    size_t tail;
    size_t count;
    size_t capacity;

public:
    explicit CircularBuffer(size_t cap = 1000)
        : buffer(cap), head(0), tail(0), count(0), capacity(cap) {}

    void push(const T& item) {
        buffer[tail] = item;
        tail = (tail + 1) % capacity;
        if (count < capacity) {
            count++;
        } else {
            head = (head + 1) % capacity;
        }
    }

    void clear() {
        head = tail = count = 0;
    }

    size_t size() const { return count; }
    bool empty() const { return count == 0; }

    const T& operator[](size_t index) const {
        return buffer[(head + index) % capacity];
    }

    // 迭代器支持
    class const_iterator {
    private:
        const CircularBuffer* buffer;
        size_t index;
    public:
        const_iterator(const CircularBuffer* buf, size_t idx) : buffer(buf), index(idx) {}
        const T& operator*() const { return (*buffer)[index]; }
        const_iterator& operator++() { ++index; return *this; }
        bool operator!=(const const_iterator& other) const { return index != other.index; }
    };

    const_iterator begin() const { return const_iterator(this, 0); }
    const_iterator end() const { return const_iterator(this, count); }
};

} // namespace Oscilloscope

#endif // COMMON_DATASTRUCTURES_H
