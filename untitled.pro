QT       += core gui serialport

greaterThan(QT_MAJOR_VERSION, 4): QT += widgets

CONFIG += c++17

# You can make your code fail to compile if it uses deprecated APIs.
# In order to do so, uncomment the following line.
#DEFINES += QT_DISABLE_DEPRECATED_BEFORE=0x060000    # disables all the APIs deprecated before Qt 6.0.0

SOURCES += \
    basecaninterface.cpp \
    baudratecalculator_new.cpp \
    canform.cpp \
    canconfig.cpp \
    canconfig_new.cpp \
    cancommform.cpp \
    caninterface.cpp \
    canfdinterface.cpp \
    main.cpp \
    mainwindow.cpp \
    messagebox_utils.cpp \
    modbusform.cpp \
    modbusprotocol.cpp \
    motordatamanager.cpp \
    motorprotocol.cpp \
    multimotorinterface.cpp \
    multimotorcontrol_pure.cpp \
    oscilloscopeinterface.cpp \
    protocolselectform.cpp \
    timermanager.cpp \
    canfddevicemanager.cpp \
    deviceconnectiondialog.cpp

HEADERS += \
    basecaninterface.h \
    baudratecalculator_new.h \
    canform.h \
    canconfig.h \
    canconfig_new.h \
    cancommform.h \
    caninterface.h \
    canfdinterface.h \
    common_datastructures.h \
    include/canframe.h \
    include/config.h \
    include/typedef.h \
    include/zlgcan.h \
    mainwindow.h \
    messagebox_utils.h \
    modbusform.h \
    modbusprotocol.h \
    motordatamanager.h \
    motorprotocol.h \
    multimotorinterface.h \
    multimotorcontrol_pure.h \
    oscilloscopeinterface.h \
    protocolselectform.h \
    timermanager.h \
    canfddevicemanager.h \
    deviceconnectiondialog.h

FORMS += \
    baudratecalculator_new.ui \
    canconfig_new.ui \
    canform.ui \
    caninterface.ui \
    canfdinterface.ui \
    mainwindow.ui \
    modbusform.ui \
    multimotorinterface.ui \
    multimotorcontrol_pure.ui \
    oscilloscopeinterface.ui \
    protocolselectform.ui

# ZLGCAN SDK - 使用动态加载方式（兼容MinGW）
INCLUDEPATH += $$PWD/include

# 注意：DLL文件需要手动复制到可执行文件目录
# 从 zlgcan_x64/ 复制 zlgcan.dll 和 kerneldlls/ 到构建输出目录

# Default rules for deployment.
qnx: target.path = /tmp/$${TARGET}/bin
else: unix:!android: target.path = /opt/$${TARGET}/bin
!isEmpty(target.path): INSTALLS += target

DISTFILES += \
    lib/zlgcan.lib \
    untitled.pro.user

RESOURCES += \
    resources.qrc
