/**
 * @file motorprotocol.h
 * @brief 六电机控制系统CANFD协议数据结构定义
 * @details 定义了完整的CANFD协议数据结构，包括电机控制命令帧、状态反馈帧、
 *          系统广播帧等，以及相关的枚举类型、常量定义和工具函数。
 *          该协议支持6个电机的独立控制和实时状态监控。
 * <AUTHOR>
 * @date 2025-01-28
 * @version 1.0
 */

#ifndef MOTORPROTOCOL_H
#define MOTORPROTOCOL_H

#include <QtGlobal>
#include <QDateTime>
#include <QMutex>
#include <QVector>

// 强制1字节对齐，确保数据结构一致性
#pragma pack(1)

// ================================
// 协议常量定义
// ================================

/// 电机数量
#define MOTOR_COUNT 6

/// 最大数据长度
#define MAX_DATA_LENGTH 64

/// 协议版本
#define PROTOCOL_VERSION 0x01

// ================================
// CAN ID 定义
// ================================

/// 系统广播命令ID
#define CAN_ID_SYSTEM_BROADCAST    0x000
/// 系统状态反馈ID  
#define CAN_ID_SYSTEM_STATUS       0x080
/// 电机控制命令基址
#define CAN_ID_MOTOR_CMD_BASE      0x100
/// 电机状态反馈基址
#define CAN_ID_MOTOR_STATUS_BASE   0x180
/// 紧急停止命令ID
#define CAN_ID_EMERGENCY_STOP      0x700
/// 心跳包ID
#define CAN_ID_HEARTBEAT           0x701

/// 根据电机ID计算控制命令CAN ID
#define GET_MOTOR_CMD_ID(motor_id)    (CAN_ID_MOTOR_CMD_BASE + motor_id)
/// 根据电机ID计算状态反馈CAN ID  
#define GET_MOTOR_STATUS_ID(motor_id) (CAN_ID_MOTOR_STATUS_BASE + motor_id)

// ================================
// 枚举类型定义
// ================================

/**
 * @brief 帧类型枚举
 */
enum FrameType {
    FRAME_TYPE_CONTROL = 0x01,      ///< 控制命令帧
    FRAME_TYPE_STATUS = 0x02,       ///< 状态反馈帧
    FRAME_TYPE_BROADCAST = 0x03,    ///< 系统广播帧
    FRAME_TYPE_HEARTBEAT = 0x04     ///< 心跳帧
};

/**
 * @brief 控制命令类型枚举
 */
enum CommandType {
    CMD_STOP = 0x00,           ///< 停止电机
    CMD_START = 0x01,          ///< 启动电机
    CMD_RESET = 0x02,          ///< 复位电机控制器
    CMD_ENABLE = 0x03,         ///< 使能电机
    CMD_DISABLE = 0x04,        ///< 失能电机
    CMD_SET_SPEED = 0x10,      ///< 设置目标速度
    CMD_SET_POSITION = 0x11,   ///< 设置目标位置
    CMD_JOG_FORWARD = 0x20,    ///< 正向点动
    CMD_JOG_REVERSE = 0x21,    ///< 反向点动
    CMD_HOME = 0x30,           ///< 回零操作
    CMD_CLEAR_ERROR = 0x40     ///< 清除错误状态
};

/**
 * @brief 控制模式枚举
 */
enum ControlMode {
    MODE_POSITION = 0x01,      ///< 位置控制模式
    MODE_SPEED = 0x02,         ///< 速度控制模式
    MODE_TORQUE = 0x03,        ///< 转矩控制模式
    MODE_PROFILE_POS = 0x04,   ///< 轮廓位置模式
    MODE_PROFILE_VEL = 0x05,   ///< 轮廓速度模式
    MODE_HOMING = 0x06         ///< 回零模式
};

/**
 * @brief 状态标志位枚举
 */
enum StatusFlags {
    STATUS_ENABLED = 0x01,         ///< 电机使能状态
    STATUS_RUNNING = 0x02,         ///< 电机运行状态
    STATUS_ERROR = 0x04,           ///< 错误状态
    STATUS_WARNING = 0x08,         ///< 警告状态
    STATUS_HOMED = 0x10,           ///< 已完成回零
    STATUS_MOVING = 0x20,          ///< 正在运动
    STATUS_TARGET_REACHED = 0x40,  ///< 已到达目标位置
    STATUS_EMERGENCY = 0x80        ///< 紧急停止状态
};

/**
 * @brief 系统广播命令枚举
 */
enum BroadcastCommand {
    BROADCAST_STOP_ALL = 0x00,     ///< 停止所有电机
    BROADCAST_START_ALL = 0x01,    ///< 启动所有电机
    BROADCAST_EMERGENCY = 0x02,    ///< 紧急停止所有电机
    BROADCAST_SYNC = 0x03,         ///< 同步信号
    BROADCAST_RESET_ALL = 0x04     ///< 复位所有电机
};

/**
 * @brief 错误代码枚举
 */
enum ErrorCode {
    ERR_NO_ERROR = 0x00,          ///< 无错误
    
    // 通信错误 (0x01-0x0F)
    ERR_COMM_TIMEOUT = 0x01,      ///< 通信超时
    ERR_CHECKSUM_FAIL = 0x02,     ///< 校验和错误
    ERR_INVALID_CMD = 0x03,       ///< 无效命令
    ERR_INVALID_PARAM = 0x04,     ///< 无效参数
    ERR_SEQUENCE_ERROR = 0x05,    ///< 序列号错误
    
    // 电机硬件错误 (0x10-0x2F)
    ERR_MOTOR_FAULT = 0x10,       ///< 电机故障
    ERR_OVERCURRENT = 0x11,       ///< 过流保护
    ERR_OVERVOLTAGE = 0x12,       ///< 过压保护
    ERR_UNDERVOLTAGE = 0x13,      ///< 欠压保护
    ERR_OVERTEMP = 0x14,          ///< 过温保护
    ERR_ENCODER_FAULT = 0x15,     ///< 编码器故障
    ERR_HALL_FAULT = 0x16,        ///< 霍尔传感器故障
    
    // 运动控制错误 (0x30-0x4F)
    ERR_POSITION_LIMIT = 0x30,    ///< 位置限制
    ERR_SPEED_LIMIT = 0x31,       ///< 速度限制
    ERR_FOLLOWING_ERROR = 0x32,   ///< 跟随误差过大
    ERR_HOME_TIMEOUT = 0x33,      ///< 回零超时
    ERR_NOT_HOMED = 0x34,         ///< 未回零
    
    // 系统错误 (0x50-0x6F)
    ERR_SYSTEM_FAULT = 0x50,      ///< 系统故障
    ERR_EMERGENCY_STOP = 0x51,    ///< 紧急停止
    ERR_POWER_FAULT = 0x52,       ///< 电源故障
    ERR_WATCHDOG_RESET = 0x53     ///< 看门狗复位
};

// ================================
// 数据结构定义
// ================================

/**
 * @brief 电机控制命令帧结构 (上位机→下位机)
 * @details 总长度: 20字节，适合CANFD传输
 */
struct MotorControlFrame {
    // === 帧头信息 (4字节) ===
    quint8 frame_type;        ///< 帧类型: 0x01=控制命令
    quint8 motor_id;          ///< 电机ID: 1-6
    quint8 sequence;          ///< 序列号(防重复，循环0-255)
    quint8 priority;          ///< 优先级: 0=最高, 255=最低
    
    // === 控制参数 (12字节) ===
    quint8 command;           ///< 控制命令(见CommandType枚举)
    quint8 control_mode;      ///< 控制模式(见ControlMode枚举)
    quint16 target_speed;     ///< 目标速度 (RPM, 0-65535)
    quint32 target_position;  ///< 目标位置 (脉冲数)
    quint16 acceleration;     ///< 加速度 (RPM/s)
    quint16 deceleration;     ///< 减速度 (RPM/s)
    
    // === 校验和时间戳 (4字节) ===
    quint16 checksum;         ///< 校验和(CRC16)
    quint16 timestamp;        ///< 时间戳(毫秒低16位)
    
    /**
     * @brief 构造函数
     */
    MotorControlFrame() {
        memset(this, 0, sizeof(MotorControlFrame));
        frame_type = FRAME_TYPE_CONTROL;
    }
};

/**
 * @brief 电机状态反馈帧结构 (下位机→上位机)
 * @details 总长度: 40字节，包含完整的电机状态信息
 */
struct MotorStatusFrame {
    // === 帧头信息 (4字节) ===
    quint8 frame_type;        ///< 帧类型: 0x02=状态反馈
    quint8 motor_id;          ///< 电机ID: 1-6
    quint8 sequence;          ///< 序列号(与控制命令对应)
    quint8 status_flags;      ///< 状态标志位(见StatusFlags)
    
    // === 实时数据 (24字节) - 给示波器显示 ===
    quint16 current_speed;    ///< 当前速度 (RPM) → 示波器蓝色通道
    quint32 current_position; ///< 当前位置 (脉冲数)
    quint16 motor_current;    ///< 电机电流 (mA) → 示波器红色通道
    quint16 motor_voltage;    ///< 电机电压 (mV) → 示波器绿色通道
    quint16 temperature;      ///< 温度 (0.1°C) → 示波器黄色通道
    quint16 torque;           ///< 转矩 (0.1Nm)
    quint32 encoder_position; ///< 编码器位置 (脉冲)
    quint16 following_error;  ///< 跟随误差 (脉冲)
    quint16 load_ratio;       ///< 负载率 (0.1%)
    
    // === 状态信息 (8字节) ===
    quint8 control_mode;      ///< 当前控制模式
    quint8 error_code;        ///< 错误代码
    quint16 warning_code;     ///< 警告代码
    quint32 runtime;          ///< 运行时间 (秒)
    
    // === 校验和时间戳 (4字节) ===
    quint16 checksum;         ///< 校验和(CRC16)
    quint16 timestamp;        ///< 时间戳(毫秒低16位)
    
    /**
     * @brief 构造函数
     */
    MotorStatusFrame() {
        memset(this, 0, sizeof(MotorStatusFrame));
        frame_type = FRAME_TYPE_STATUS;
    }
};

/**
 * @brief 系统广播帧结构
 * @details 总长度: 16字节，用于系统级控制
 */
struct SystemBroadcastFrame {
    quint8 frame_type;        ///< 帧类型: 0x03=系统广播
    quint8 broadcast_cmd;     ///< 广播命令(见BroadcastCommand)
    quint8 motor_mask;        ///< 电机掩码 (bit0-5对应电机1-6)
    quint8 reserved;          ///< 保留字段
    quint32 sync_timestamp;   ///< 同步时间戳
    quint32 reserved2[2];     ///< 保留字段
    
    /**
     * @brief 构造函数
     */
    SystemBroadcastFrame() {
        memset(this, 0, sizeof(SystemBroadcastFrame));
        frame_type = FRAME_TYPE_BROADCAST;
    }
};

/**
 * @brief 心跳帧结构
 * @details 总长度: 8字节，用于系统状态监控
 */
struct HeartbeatFrame {
    quint8 frame_type;        ///< 帧类型: 0x04=心跳
    quint8 system_state;      ///< 系统状态
    quint8 motor_count;       ///< 电机数量
    quint8 reserved;          ///< 保留字段
    quint32 system_runtime;   ///< 系统运行时间 (秒)
    
    /**
     * @brief 构造函数
     */
    HeartbeatFrame() {
        memset(this, 0, sizeof(HeartbeatFrame));
        frame_type = FRAME_TYPE_HEARTBEAT;
        motor_count = MOTOR_COUNT;
    }
};

// 恢复默认对齐
#pragma pack()

// ================================
// 工具函数声明
// ================================

/**
 * @brief CRC16校验计算
 * @param data 数据指针
 * @param length 数据长度
 * @return CRC16校验值
 */
quint16 calculateCRC16(const quint8* data, quint32 length);

/**
 * @brief 验证CRC16校验
 * @param data 数据指针
 * @param length 数据长度
 * @param expected_crc 期望的CRC值
 * @return 校验是否正确
 */
bool verifyCRC16(const quint8* data, quint32 length, quint16 expected_crc);

/**
 * @brief 获取当前时间戳(毫秒)
 * @return 时间戳低16位
 */
quint16 getCurrentTimestamp();

/**
 * @brief 获取错误描述字符串
 * @param error_code 错误代码
 * @return 错误描述
 */
QString getErrorDescription(ErrorCode error_code);

/**
 * @brief 获取命令描述字符串
 * @param command 命令类型
 * @return 命令描述
 */
QString getCommandDescription(CommandType command);

/**
 * @brief 获取控制模式描述字符串
 * @param mode 控制模式
 * @return 模式描述
 */
QString getControlModeDescription(ControlMode mode);

/**
 * @brief 填充电机控制命令帧的校验和
 * @param frame 控制命令帧指针
 * @details 计算并填充帧的CRC16校验和
 */
void fillControlFrameChecksum(MotorControlFrame* frame);

/**
 * @brief 验证电机控制命令帧的校验和
 * @param frame 控制命令帧指针
 * @return 校验是否正确
 */
bool verifyControlFrameChecksum(const MotorControlFrame* frame);

/**
 * @brief 填充电机状态反馈帧的校验和
 * @param frame 状态反馈帧指针
 */
void fillStatusFrameChecksum(MotorStatusFrame* frame);

/**
 * @brief 验证电机状态反馈帧的校验和
 * @param frame 状态反馈帧指针
 * @return 校验是否正确
 */
bool verifyStatusFrameChecksum(const MotorStatusFrame* frame);

/**
 * @brief 将电机状态帧转换为字节数组
 * @param frame 状态帧
 * @return 字节数组
 */
QByteArray statusFrameToByteArray(const MotorStatusFrame& frame);

/**
 * @brief 将字节数组转换为电机状态帧
 * @param data 字节数组
 * @param frame 输出的状态帧
 * @return 转换是否成功
 */
bool byteArrayToStatusFrame(const QByteArray& data, MotorStatusFrame& frame);

/**
 * @brief 将控制命令帧转换为字节数组
 * @param frame 控制帧
 * @return 字节数组
 */
QByteArray controlFrameToByteArray(const MotorControlFrame& frame);

/**
 * @brief 将字节数组转换为控制命令帧
 * @param data 字节数组
 * @param frame 输出的控制帧
 * @return 转换是否成功
 */
bool byteArrayToControlFrame(const QByteArray& data, MotorControlFrame& frame);

/**
 * @brief 打印控制命令帧信息（调试用）
 * @param frame 控制命令帧
 * @return 格式化的字符串
 */
QString debugControlFrame(const MotorControlFrame& frame);

/**
 * @brief 打印状态反馈帧信息（调试用）
 * @param frame 状态反馈帧
 * @return 格式化的字符串
 */
QString debugStatusFrame(const MotorStatusFrame& frame);

#endif // MOTORPROTOCOL_H
