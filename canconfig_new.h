/**
 * @file canconfig_new.h
 * @brief CAN/CANFD配置对话框头文件 - UI版本
 * @details 提供基于Qt Designer UI的CAN和CANFD协议配置界面
 * <AUTHOR>
 * @date 2024-12-19
 * @version 2.0
 */

#ifndef CANCONFIG_NEW_H
#define CANCONFIG_NEW_H

#include <QDialog>
#include <QComboBox>
#include <QCheckBox>
#include <QPushButton>
#include <QMessageBox>
#include "canconfig.h"          // 包含CANConfigData结构体定义
#include "baudratecalculator_new.h"

QT_BEGIN_NAMESPACE
namespace Ui { class CANConfigNew; }
QT_END_NAMESPACE

// CANConfigData结构体定义在canconfig.h中

/**
 * @brief CAN/CANFD配置对话框类 - UI版本
 * @details 基于Qt Designer UI文件的配置界面，解决代码生成界面的显示问题
 */
class CANConfigNew : public QDialog
{
    Q_OBJECT

public:
    /**
     * @brief 构造函数
     * @param parent 父窗口指针
     */
    explicit CANConfigNew(QWidget *parent = nullptr);

    /**
     * @brief 析构函数
     */
    ~CANConfigNew();

    /**
     * @brief 获取配置数据
     * @return 当前配置的所有参数
     * @details 从UI控件中读取用户设置的配置参数
     */
    CANConfigData getConfigData() const;

    /**
     * @brief 设置配置数据
     * @param data 要设置的配置数据
     * @details 将配置数据应用到UI控件中
     */
    void setConfigData(const CANConfigData &data);

private slots:
    /**
     * @brief 协议类型改变槽函数
     * @details 根据协议类型动态调整界面显示
     */
    void onProtocolChanged();

    /**
     * @brief 波特率计算器按钮点击槽函数
     * @details 打开波特率计算器对话框
     */
    void onBaudrateCalculatorClicked();

    /**
     * @brief 自定义配置文本变化槽函数
     * @details 当用户输入自定义配置时，禁用或启用仲裁段和数据段下拉框
     */
    void onCustomConfigTextChanged();

    /**
     * @brief 确定按钮点击槽函数
     * @details 验证配置并接受对话框
     */
    void onOkClicked();

    /**
     * @brief 取消按钮点击槽函数
     * @details 拒绝对话框并关闭
     */
    void onCancelClicked();

private:
    /**
     * @brief 设置信号槽连接
     * @details 连接UI控件的信号到相应的槽函数
     */
    void setupConnections();

    /**
     * @brief 根据协议类型更新界面
     * @details 根据当前选择的协议类型动态显示或隐藏相关控件
     */
    void updateUIForProtocol();

    /**
     * @brief 验证配置参数
     * @return 配置是否有效
     * @details 检查用户输入的配置参数是否合理
     */
    bool validateConfig();



    /**
     * @brief 显示统一样式的警告消息框
     * @param title 消息框标题
     * @param text 消息框内容
     * @details 显示红白风格的警告消息框
     */
    void showStyledWarning(const QString &title, const QString &text);

    // UI界面指针
    Ui::CANConfigNew *ui;
};

#endif // CANCONFIG_NEW_H
