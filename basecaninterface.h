/**
 * @file basecaninterface.h
 * @brief CAN/CANFD通信接口基类头文件
 * @details 定义了CAN和CANFD通信接口的公共基类，减少代码重复，
 *          提供统一的接口管理、日志处理、定时器管理等功能。
 * <AUTHOR>
 * @date 2025-07-23
 * @version 1.0
 */

#ifndef BASECANINTERFACE_H
#define BASECANINTERFACE_H

#include <QWidget>              // Qt窗口基类
#include <QTimer>               // Qt定时器类
#include <QTextEdit>            // Qt文本编辑器类
#include <QTextCursor>          // Qt文本光标类
#include <QTime>                // Qt时间类
#include <QLibrary>             // Qt动态库加载类
#include <QDebug>               // Qt调试输出类
#include <QMutex>               // Qt互斥锁类
#include "include/zlgcan.h"     // ZLGCAN硬件SDK头文件
#include "canconfig.h"          // CAN配置数据结构

/**
 * @class BaseCANInterface
 * @brief CAN/CANFD通信接口基类
 * @details 提供CAN和CANFD接口的公共功能，包括：
 *          1. 设备连接管理
 *          2. 日志处理和内存管理
 *          3. 定时器统一管理
 *          4. 公共UI操作
 */
class BaseCANInterface : public QWidget
{
    Q_OBJECT

public:
    /**
     * @brief 构造函数
     * @param parent 父窗口指针
     */
    explicit BaseCANInterface(QWidget *parent = nullptr);

    /**
     * @brief 析构函数
     * @details 清理公共资源
     */
    virtual ~BaseCANInterface();

    /**
     * @brief 设置配置数据（纯虚函数）
     * @param config 配置数据
     */
    virtual void setConfigData(const CANConfigData &config) = 0;

signals:
    /**
     * @brief 返回协议选择界面信号
     */
    void backToProtocolSelect();

protected slots:
    /**
     * @brief 心跳定时器槽函数
     * @details 处理心跳包发送
     */
    virtual void onHeartbeatTimer();

    /**
     * @brief 接收定时器槽函数
     * @details 处理数据接收
     */
    virtual void onReceiveTimer();

protected:
    // === 公共成员变量 ===
    bool isConnected;                    ///< 设备连接状态
    bool useRealDevice;                  ///< 是否使用真实设备
    bool isHeartbeatRunning;             ///< 心跳运行状态
    bool isContinuousSending;            ///< 连续发送状态
    
    int sendCount;                       ///< 发送计数
    int receiveCount;                    ///< 接收计数
    
    // === 定时器管理 ===
    QTimer *heartbeatTimer;              ///< 心跳定时器
    QTimer *receiveTimer;                ///< 接收定时器
    QTimer *continuousSendTimer;         ///< 连续发送定时器
    
    // === ZLGCAN相关 ===
    QLibrary zlgcanLib;                  ///< ZLGCAN动态库
    DEVICE_HANDLE deviceHandle;          ///< 设备句柄
    CHANNEL_HANDLE channelHandle;        ///< 通道句柄
    
    // === 配置数据 ===
    CANConfigData configData;            ///< 配置数据

    // === 公共方法 ===
    /**
     * @brief 添加日志文本（优化版本，防止内存泄漏）
     * @param text 日志文本
     * @param isReceived 是否为接收数据
     */
    virtual void appendLog(const QString &text, bool isReceived);

    /**
     * @brief 获取目标文本编辑器
     * @param isReceived 是否为接收数据
     * @return 对应的文本编辑器指针
     */
    virtual QTextEdit* getTargetTextEdit(bool isReceived) = 0;

    /**
     * @brief 启动通信
     * @details 启动相关定时器
     */
    virtual void startCommunication();

    /**
     * @brief 停止通信
     * @details 停止所有定时器
     */
    virtual void stopCommunication();

    /**
     * @brief 初始化定时器
     * @details 创建和配置所有定时器
     */
    void initializeTimers();

    /**
     * @brief 连接定时器信号
     * @details 连接定时器信号到对应槽函数
     */
    void connectTimerSignals();

    /**
     * @brief 确保静态变量已正确初始化
     */
    static void ensureStaticInitialization();

    /**
     * @brief 加载ZLGCAN库
     * @return 是否加载成功
     */
    bool loadZLGCANLibrary();

    /**
     * @brief 尝试连接真实设备
     * @return 是否连接成功
     */
    virtual bool tryConnectRealDevice() = 0;

    /**
     * @brief 设置模拟模式
     * @details 当真实设备连接失败时调用
     */
    void setupSimulationMode();

    /**
     * @brief 更新控件状态（纯虚函数）
     * @details 根据连接状态更新UI控件
     */
    virtual void updateControlState() = 0;

private:
    static const int MAX_LOG_LINES = 1000;  ///< 最大日志行数
    static QMutex s_deviceMutex;            ///< 设备访问互斥锁
    static bool s_deviceInUse;              ///< 设备是否正在使用
};

#endif // BASECANINTERFACE_H
