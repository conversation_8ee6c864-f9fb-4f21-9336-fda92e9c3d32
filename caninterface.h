/**
 * @file caninterface.h
 * @brief CAN通信接口类头文件
 * @details 定义了CAN通信接口类CANInterface，提供完整的CAN协议通信功能，
 *          包括设备连接、数据收发、频率控制、心跳功能等。集成ZLGCAN SDK
 *          实现与ZLGCANFD100U硬件设备的通信。
 * <AUTHOR>
 * @date 2025-07-02
 * @version 1.0
 */

#ifndef CANINTERFACE_H
#define CANINTERFACE_H

#include <QWidget>          // Qt窗口基类
#include <QTimer>           // Qt定时器类
#include <QTime>            // Qt时间类
#include <QMessageBox>      // Qt消息框类
#include <QFileDialog>      // Qt文件对话框类
#include <QTextCursor>      // Qt文本光标类
#include <QDateTime>        // Qt日期时间类
#include <QLibrary>         // Qt动态库加载类
#include "canconfig.h"      // CAN配置数据结构
#include "include/zlgcan.h" // ZLGCAN SDK头文件
#include "basecaninterface.h" // CAN接口基类

// Qt命名空间标记
QT_BEGIN_NAMESPACE
namespace Ui { class CANInterface; }  // UI类前向声明
QT_END_NAMESPACE

/**
 * @class CANInterface
 * @brief CAN通信接口类
 * @details 继承自BaseCANInterface，提供完整的CAN协议通信功能界面和逻辑。
 *          该类集成ZLGCAN SDK，实现与ZLGCANFD100U硬件设备的通信，
 *          支持设备连接管理、数据收发、频率控制、心跳功能、数据保存等。
 */
class CANInterface : public BaseCANInterface
{
    Q_OBJECT  // Qt元对象系统宏，支持信号槽机制

public:
    /**
     * @brief 构造函数
     * @param parent 父窗口指针，默认为nullptr
     * @details 初始化CAN通信接口，创建UI界面和设置各种功能
     */
    CANInterface(QWidget *parent = nullptr);

    /**
     * @brief 析构函数
     * @details 清理资源，断开设备连接，释放内存
     */
    ~CANInterface();

    /**
     * @brief 设置配置数据
     * @param config CAN配置数据结构
     * @details 应用用户配置的CAN参数到通信接口
     */
    void setConfigData(const CANConfigData &config) override;

    // 实现基类的纯虚函数
    QTextEdit* getTargetTextEdit(bool isReceived) override;
    bool tryConnectRealDevice() override;
    void updateControlState() override;

    // 重写基类的虚函数
    void startCommunication() override;
    void stopCommunication() override;
    void appendLog(const QString &text, bool isReceived) override;

private slots:
    /**
     * @brief 连接设备槽函数
     * @details 响应连接按钮点击，初始化并连接CAN设备
     */
    void onConnectDevice();

    /**
     * @brief 断开设备槽函数
     * @details 响应断开按钮点击，断开CAN设备连接
     */
    void onDisconnectDevice();

    /**
     * @brief 发送数据槽函数
     * @details 响应发送按钮点击，发送单次CAN数据帧
     */
    void onSendData();

    /**
     * @brief 连续发送槽函数
     * @details 响应连续发送按钮点击，启动或停止连续发送
     */
    void onContinuousSend();

    /**
     * @brief 发送心跳槽函数
     * @details 响应心跳按钮点击，启动或停止心跳发送
     */
    void onSendHeartbeat();

    /**
     * @brief 清空发送区域槽函数
     * @details 响应清空发送按钮点击，清空发送数据显示区域
     */
    void onClearSendArea();

    /**
     * @brief 清空接收区域槽函数
     * @details 响应清空接收按钮点击，清空接收数据显示区域
     */
    void onClearReceiveArea();

    /**
     * @brief 保存数据槽函数
     * @details 响应保存按钮点击，将接收数据保存到文件
     */
    void onSaveData();

    /**
     * @brief 返回按钮点击槽函数
     * @details 响应返回按钮点击，发出返回信号
     */
    void onBackClicked();

    /**
     * @brief 心跳定时器槽函数
     * @details 心跳定时器超时时调用，发送心跳数据帧
     */
    void onHeartbeatTimer();

    /**
     * @brief 接收定时器槽函数
     * @details 接收定时器超时时调用，检查并处理接收到的数据
     */
    void onReceiveTimer();

    /**
     * @brief 发送频率改变槽函数
     * @details 响应发送频率SpinBox值变化，更新连续发送频率
     */
    void onSendFrequencyChanged();

    /**
     * @brief 接收频率改变槽函数
     * @details 响应接收频率SpinBox值变化，更新接收检查频率
     */
    void onReceiveFrequencyChanged();

    /**
     * @brief 发送频率预设改变槽函数
     * @details 响应发送频率预设ComboBox选择变化，快速设置发送频率
     */
    void onSendFrequencyPresetChanged();

    /**
     * @brief 接收频率预设改变槽函数
     * @details 响应接收频率预设ComboBox选择变化，快速设置接收频率
     */
    void onReceiveFrequencyPresetChanged();

    /**
     * @brief 连续发送定时器槽函数
     * @details 连续发送定时器超时时调用，执行连续发送操作
     */
    void onContinuousSendTimer();

private:
    /**
     * @brief 设置信号槽连接
     * @details 连接UI控件的信号到相应的槽函数
     */
    void setupConnections();

    // updateControlState函数已在基类中声明为纯虚函数，在public部分重写

    /**
     * @brief 提取频率数值
     * @param text 包含频率信息的文本字符串
     * @return int 提取出的频率数值（毫秒）
     * @details 从频率文本中提取数值，用于定时器设置
     */
    int extractFrequencyValue(const QString &text);

    // appendLog, startCommunication, stopCommunication函数已在public部分声明为override函数

    // === UI界面成员变量 ===
    /**
     * @brief UI界面指针
     * @details 指向Qt Designer生成的UI类实例
     */
    Ui::CANInterface *ui;

    // 其他成员变量已在BaseCANInterface基类中定义
};

#endif // CANINTERFACE_H
