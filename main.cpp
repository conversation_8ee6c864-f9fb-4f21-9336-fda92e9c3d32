/**
 * @file main.cpp
 * @brief 电机上位机应用程序主入口文件
 * @details 这是Qt应用程序的主入口点，负责初始化Qt应用程序环境，
 *          创建主窗口实例并启动事件循环。该应用程序支持CAN/CANFD
 *          协议通信，用于电机控制和数据监控。
 * <AUTHOR>
 * @date 2025-07-02
 * @version 1.0
 */

#include "mainwindow.h"      // 主窗口类定义
#include <QApplication>      // Qt应用程序基类
#include <QDebug>           // Qt调试输出
#include <QTimer>           // Qt定时器类

/**
 * @brief 应用程序主函数
 * @details 程序的入口点，负责：
 *          1. 创建QApplication实例，初始化Qt框架
 *          2. 创建主窗口实例
 *          3. 显示主窗口
 *          4. 启动Qt事件循环，处理用户交互和系统事件
 * @param argc 命令行参数个数
 * @param argv 命令行参数数组
 * @return int 应用程序退出代码，0表示正常退出，非0表示异常退出
 */
int main(int argc, char *argv[])
{
    // 创建Qt应用程序实例，管理应用程序的生命周期和全局设置
    QApplication a(argc, argv);

    // 设置ZLGCAN库环境
    QString appDir = QApplication::applicationDirPath();
    QString kernelDllsDir = appDir + "/kerneldlls";

    // 添加kerneldlls目录到系统PATH
    QString currentPath = qgetenv("PATH");
    QString newPath = kernelDllsDir + ";" + currentPath;
    qputenv("PATH", newPath.toLocal8Bit());

    qDebug() << "应用程序启动，设置ZLGCAN环境路径:" << kernelDllsDir;

    // 强制初始化Qt的元对象系统和静态资源
    qDebug() << "初始化Qt元对象系统...";

    // 预先加载一些关键的Qt类，确保元对象系统完全初始化
    QTimer::singleShot(0, [](){
        // 这个空的lambda会触发QTimer的元对象系统初始化
    });

    // 处理一次事件循环，确保所有静态初始化完成
    QApplication::processEvents();

    qDebug() << "Qt系统初始化完成";

    // 创建主窗口实例，这是应用程序的主界面
    MainWindow w;

    // 显示主窗口，使其对用户可见
    w.show();

    // 启动Qt事件循环，程序将在此处等待和处理事件
    // 直到用户关闭应用程序或调用QApplication::quit()
    return a.exec();
}
