/**
 * @file motordatamanager.cpp
 * @brief 多电机数据管理器类实现
 * @details 实现了多电机数据管理器的所有功能，包括数据存储、
 *          统计分析、示波器数据提供等。
 * <AUTHOR>
 * @date 2025-01-28
 * @version 1.0
 */

#include "motordatamanager.h"
#include <QDebug>
#include <QMutexLocker>
#include <cstring>  // for memset, memcmp

/**
 * @brief 构造函数
 * @param parent 父对象指针
 */
MotorDataManager::MotorDataManager(QObject *parent)
    : QObject(parent)
{
    qDebug() << "开始创建MotorDataManager...";

    try {
        qDebug() << "MOTOR_COUNT =" << MOTOR_COUNT;

        // 恢复基本的电机数据初始化
        qDebug() << "正在调整电机数据向量大小...";
        m_motors.resize(MOTOR_COUNT);  // 恢复向量大小调整
        qDebug() << "电机数据向量大小调整完成，当前大小:" << m_motors.size();

        qDebug() << "正在初始化各个电机数据...";
        for (int i = 0; i < MOTOR_COUNT; i++) {
            qDebug() << "正在初始化电机" << (i + 1) << "...";
            m_motors[i] = SingleMotorData(i + 1);  // 电机ID从1开始
            qDebug() << "电机" << (i + 1) << "初始化完成";
        }
        qDebug() << "所有电机数据初始化完成";

        // 暂时注释掉定时器，看看是否是定时器导致的问题
        qDebug() << "正在创建通信状态检查定时器...";
        // m_checkTimer = new QTimer(this);
        // m_checkTimer->setInterval(500);  // 500ms检查一次
        // connect(m_checkTimer, &QTimer::timeout, this, &MotorDataManager::checkCommunicationStatus);
        // m_checkTimer->start();
        qDebug() << "定时器创建跳过";

        qDebug() << "MotorDataManager初始化完成，电机数量:" << MOTOR_COUNT;
    } catch (const std::exception& e) {
        qDebug() << "MotorDataManager初始化异常:" << e.what();
        throw;
    } catch (...) {
        qDebug() << "MotorDataManager初始化发生未知异常";
        throw;
    }
}

/**
 * @brief 析构函数
 */
MotorDataManager::~MotorDataManager()
{
    if (m_checkTimer) {
        m_checkTimer->stop();
    }
    qDebug() << "MotorDataManager destroyed";
}

// ================================
// 数据访问接口实现
// ================================

/**
 * @brief 验证电机ID
 * @param motor_id 电机ID
 * @return 是否有效
 */
bool MotorDataManager::isValidMotorId(quint8 motor_id) const
{
    return (motor_id >= 1 && motor_id <= MOTOR_COUNT);
}

/**
 * @brief 获取电机数据
 * @param motor_id 电机ID (1-6)
 * @return 电机数据引用
 */
const SingleMotorData& MotorDataManager::getMotorData(quint8 motor_id) const
{
    QMutexLocker locker(&m_mutex);
    
    if (!isValidMotorId(motor_id)) {
        static SingleMotorData dummy;
        qWarning() << "Invalid motor ID:" << motor_id;
        return dummy;
    }
    
    return m_motors[motor_id - 1];
}

/**
 * @brief 获取所有电机数据
 * @return 所有电机数据的向量
 */
QVector<SingleMotorData> MotorDataManager::getAllMotorData() const
{
    QMutexLocker locker(&m_mutex);
    return m_motors;
}

/**
 * @brief 获取电机当前状态
 * @param motor_id 电机ID (1-6)
 * @return 状态帧
 */
MotorStatusFrame MotorDataManager::getMotorStatus(quint8 motor_id) const
{
    QMutexLocker locker(&m_mutex);
    
    if (!isValidMotorId(motor_id)) {
        return MotorStatusFrame();
    }
    
    return m_motors[motor_id - 1].current_status;
}

/**
 * @brief 获取电机统计信息
 * @param motor_id 电机ID (1-6)
 * @return 统计信息
 */
MotorStatistics MotorDataManager::getMotorStatistics(quint8 motor_id) const
{
    QMutexLocker locker(&m_mutex);

    if (!isValidMotorId(motor_id)) {
        return MotorStatistics();
    }

    // 暂时返回空的统计信息，因为statistics成员被注释掉了
    qDebug() << "getMotorStatistics: 统计功能暂时禁用";
    return MotorStatistics();
}

// ================================
// 数据更新接口实现
// ================================

/**
 * @brief 更新电机状态
 * @param motor_id 电机ID (1-6)
 * @param status 状态帧
 */
void MotorDataManager::updateMotorStatus(quint8 motor_id, const MotorStatusFrame& status)
{
    if (!isValidMotorId(motor_id)) {
        qWarning() << "Invalid motor ID in updateMotorStatus:" << motor_id;
        return;
    }
    
    {
        QMutexLocker locker(&m_mutex);
        
        SingleMotorData& motor = m_motors[motor_id - 1];
        
        // 更新状态数据
        motor.current_status = status;
        // motor.last_status_time = QDateTime::currentDateTime();  // 暂时注释掉
        motor.last_sequence = status.sequence;
        motor.communication_timeout = false;
        
        // 更新连接状态
        if (!motor.is_connected) {
            motor.is_connected = true;
            emit motorConnectionChanged(motor_id, true);
        }
        
        // 检查使能状态变化
        bool was_enabled = motor.is_enabled;
        motor.is_enabled = (status.status_flags & STATUS_ENABLED) != 0;
        if (was_enabled != motor.is_enabled) {
            emit motorEnabledChanged(motor_id, motor.is_enabled);
        }
        
        // 检查错误状态
        if (status.error_code != ERR_NO_ERROR) {
            emit motorError(motor_id, status.error_code);
        }
        
        // 更新统计信息
        updateStatistics(motor_id, status);
        
        // 添加历史数据点
        addHistoryDataPoints(motor_id, status);
    }
    
    // 发送状态更新信号
    emit motorStatusUpdated(motor_id, status);
}

/**
 * @brief 记录发送的控制命令
 * @param motor_id 电机ID (1-6)
 * @param command 控制命令帧
 */
void MotorDataManager::recordControlCommand(quint8 motor_id, const MotorControlFrame& command)
{
    if (!isValidMotorId(motor_id)) {
        qWarning() << "Invalid motor ID in recordControlCommand:" << motor_id;
        return;
    }
    
    QMutexLocker locker(&m_mutex);
    
    SingleMotorData& motor = m_motors[motor_id - 1];
    motor.last_command = command;
    // motor.last_command_time = QDateTime::currentDateTime();  // 暂时注释掉
    // motor.statistics.total_commands++;  // 暂时注释掉
    
    qDebug() << "Recorded command for motor" << motor_id << ":" 
             << getCommandDescription(static_cast<CommandType>(command.command));
}

/**
 * @brief 设置电机连接状态
 * @param motor_id 电机ID (1-6)
 * @param connected 是否连接
 */
void MotorDataManager::setMotorConnected(quint8 motor_id, bool connected)
{
    if (!isValidMotorId(motor_id)) {
        qWarning() << "Invalid motor ID in setMotorConnected:" << motor_id;
        return;
    }
    
    QMutexLocker locker(&m_mutex);
    
    SingleMotorData& motor = m_motors[motor_id - 1];
    if (motor.is_connected != connected) {
        motor.is_connected = connected;
        motor.communication_timeout = !connected;
        
        if (!connected) {
            // 断开连接时清除状态
            motor.is_enabled = false;
            motor.current_status = MotorStatusFrame();
        }
        
        emit motorConnectionChanged(motor_id, connected);
        qDebug() << "Motor" << motor_id << (connected ? "connected" : "disconnected");
    }
}

/**
 * @brief 设置电机使能状态
 * @param motor_id 电机ID (1-6)
 * @param enabled 是否使能
 */
void MotorDataManager::setMotorEnabled(quint8 motor_id, bool enabled)
{
    if (!isValidMotorId(motor_id)) {
        qWarning() << "Invalid motor ID in setMotorEnabled:" << motor_id;
        return;
    }
    
    QMutexLocker locker(&m_mutex);
    
    SingleMotorData& motor = m_motors[motor_id - 1];
    if (motor.is_enabled != enabled) {
        motor.is_enabled = enabled;
        
        // 更新状态帧中的使能标志
        if (enabled) {
            motor.current_status.status_flags |= STATUS_ENABLED;
        } else {
            motor.current_status.status_flags &= ~STATUS_ENABLED;
        }
        
        emit motorEnabledChanged(motor_id, enabled);
        qDebug() << "Motor" << motor_id << (enabled ? "enabled" : "disabled");
    }
}

// ================================
// 示波器数据接口实现
// ================================

/**
 * @brief 获取示波器数据
 * @param motor_id 电机ID (1-6)
 * @param channel 数据通道类型
 * @param count 数据点数量
 * @return 数据点向量
 */
QVector<MotorData::DataPoint> MotorDataManager::getOscilloscopeData(quint8 motor_id, int channel, int count) const
{
    QMutexLocker locker(&m_mutex);
    
    if (!isValidMotorId(motor_id)) {
        return QVector<MotorData::DataPoint>();
    }
    
    const SingleMotorData& motor = m_motors[motor_id - 1];
    
    // 暂时返回空数据，因为CircularBuffer被注释掉了
    Q_UNUSED(channel);
    Q_UNUSED(count);
    qDebug() << "getOscilloscopeData: 历史数据功能暂时禁用";
    return QVector<MotorData::DataPoint>();
}

/**
 * @brief 获取最新的示波器数据点
 * @param motor_id 电机ID (1-6)
 * @param channel 数据通道类型
 * @return 最新数据点
 */
MotorData::DataPoint MotorDataManager::getLatestDataPoint(quint8 motor_id, int channel) const
{
    QVector<MotorData::DataPoint> data = getOscilloscopeData(motor_id, channel, 1);
    if (!data.isEmpty()) {
        return data.last();
    }
    return MotorData::DataPoint();
}

// ================================
// 数据管理接口实现
// ================================

/**
 * @brief 清空所有历史数据
 */
void MotorDataManager::clearAllHistory()
{
    QMutexLocker locker(&m_mutex);

    // 暂时跳过历史数据清理，因为CircularBuffer被注释掉了
    qDebug() << "clearAllHistory: 历史数据功能暂时禁用，跳过清理操作";
    Q_UNUSED(m_motors);

    qDebug() << "All motor history data cleared";
}

/**
 * @brief 清空指定电机的历史数据
 * @param motor_id 电机ID (1-6)
 */
void MotorDataManager::clearMotorHistory(quint8 motor_id)
{
    if (!isValidMotorId(motor_id)) {
        qWarning() << "Invalid motor ID in clearMotorHistory:" << motor_id;
        return;
    }

    QMutexLocker locker(&m_mutex);

    // 暂时跳过历史数据清理，因为CircularBuffer被注释掉了
    qDebug() << "clearMotorHistory: 历史数据功能暂时禁用，跳过电机" << motor_id << "的清理操作";
    Q_UNUSED(motor_id);

    qDebug() << "Motor" << motor_id << "history data cleared";
}

/**
 * @brief 重置统计信息
 */
void MotorDataManager::resetStatistics()
{
    QMutexLocker locker(&m_mutex);

    // 暂时跳过统计信息重置，因为statistics成员被注释掉了
    qDebug() << "resetStatistics: 统计功能暂时禁用，跳过重置操作";

    qDebug() << "All motor statistics reset";
}

/**
 * @brief 重置指定电机的统计信息
 * @param motor_id 电机ID (1-6)
 */
void MotorDataManager::resetMotorStatistics(quint8 motor_id)
{
    if (!isValidMotorId(motor_id)) {
        qWarning() << "Invalid motor ID in resetMotorStatistics:" << motor_id;
        return;
    }

    QMutexLocker locker(&m_mutex);

    // 暂时跳过统计信息重置，因为statistics成员被注释掉了
    qDebug() << "resetMotorStatistics: 统计功能暂时禁用，跳过电机" << motor_id << "的重置操作";

    qDebug() << "Motor" << motor_id << "statistics reset";
}

// ================================
// 通信状态管理实现
// ================================

/**
 * @brief 检查通信超时
 * @param timeout_ms 超时时间(毫秒)
 * @return 超时的电机ID列表
 */
QVector<quint8> MotorDataManager::checkCommunicationTimeout(int timeout_ms)
{
    QMutexLocker locker(&m_mutex);

    QVector<quint8> timeout_motors;
    QDateTime current_time = QDateTime::currentDateTime();

    for (int i = 0; i < MOTOR_COUNT; i++) {
        SingleMotorData& motor = m_motors[i];

        // 暂时跳过通信超时检查，因为last_status_time被注释掉了
        // if (motor.is_connected && !motor.last_status_time.isNull()) {
        //     qint64 elapsed = motor.last_status_time.msecsTo(current_time);
        //     if (elapsed > timeout_ms) {
        //         if (!motor.communication_timeout) {
        //             motor.communication_timeout = true;
        //             timeout_motors.append(motor.motor_id);
        //             emit communicationTimeout(motor.motor_id);
        //             qWarning() << "Motor" << motor.motor_id << "communication timeout";
        //         }
        //     }
        // }
        Q_UNUSED(motor);
        Q_UNUSED(timeout_ms);
        Q_UNUSED(current_time);
    }

    return timeout_motors;
}

/**
 * @brief 获取连接的电机数量
 * @return 连接的电机数量
 */
int MotorDataManager::getConnectedMotorCount() const
{
    QMutexLocker locker(&m_mutex);

    int count = 0;
    for (const SingleMotorData& motor : m_motors) {
        if (motor.is_connected) {
            count++;
        }
    }

    return count;
}

/**
 * @brief 获取使能的电机数量
 * @return 使能的电机数量
 */
int MotorDataManager::getEnabledMotorCount() const
{
    QMutexLocker locker(&m_mutex);

    int count = 0;
    for (const SingleMotorData& motor : m_motors) {
        if (motor.is_enabled) {
            count++;
        }
    }

    return count;
}

/**
 * @brief 定时检查通信状态
 * @details 定时检查各电机的通信状态，更新超时标志
 */
void MotorDataManager::checkCommunicationStatus()
{
    checkCommunicationTimeout(1000);  // 1秒超时
}

// ================================
// 私有辅助函数实现
// ================================

/**
 * @brief 更新统计信息
 * @param motor_id 电机ID
 * @param status 状态帧
 */
void MotorDataManager::updateStatistics(quint8 motor_id, const MotorStatusFrame& status)
{
    // 暂时跳过统计信息更新，因为statistics成员被注释掉了
    Q_UNUSED(motor_id);
    Q_UNUSED(status);
    // qDebug() << "updateStatistics: 统计功能暂时禁用";  // 注释掉以减少日志输出
}

/**
 * @brief 添加历史数据点
 * @param motor_id 电机ID
 * @param status 状态帧
 */
void MotorDataManager::addHistoryDataPoints(quint8 motor_id, const MotorStatusFrame& status)
{
    // 暂时跳过历史数据添加，因为CircularBuffer被注释掉了
    Q_UNUSED(motor_id);
    Q_UNUSED(status);

    // 仍然发送新数据点信号（给示波器使用），但使用当前时间戳
    qint64 timestamp = QDateTime::currentMSecsSinceEpoch();
    emit newDataPointAvailable(motor_id, CHANNEL_SPEED, MotorData::DataPoint(status.current_speed, timestamp));
    emit newDataPointAvailable(motor_id, CHANNEL_CURRENT, MotorData::DataPoint(status.motor_current, timestamp));
    emit newDataPointAvailable(motor_id, CHANNEL_VOLTAGE, MotorData::DataPoint(status.motor_voltage, timestamp));
    emit newDataPointAvailable(motor_id, CHANNEL_TEMPERATURE, MotorData::DataPoint(status.temperature / 10.0, timestamp));
}
