@echo off
echo ========================================
echo    复制ZLGCAN库文件到程序目录
echo ========================================
echo.

set SOURCE_DIR=zlgcan_x64
set TARGET_DIR=build\Desktop_Qt_6_6_3_MinGW_64_bit-Debug\debug

echo 源目录: %SOURCE_DIR%
echo 目标目录: %TARGET_DIR%
echo.

if not exist "%SOURCE_DIR%" (
    echo 错误: 源目录 %SOURCE_DIR% 不存在
    pause
    exit /b 1
)

if not exist "%TARGET_DIR%" (
    echo 创建目标目录: %TARGET_DIR%
    mkdir "%TARGET_DIR%"
)

echo 复制ZLGCAN库文件...

:: 复制主要的DLL文件
copy "%SOURCE_DIR%\zlgcan.dll" "%TARGET_DIR%\" >nul
if errorlevel 1 (
    echo 错误: 无法复制 zlgcan.dll
    pause
    exit /b 1
)
echo ✓ 复制 zlgcan.dll

:: 创建kerneldlls目录
if not exist "%TARGET_DIR%\kerneldlls" (
    mkdir "%TARGET_DIR%\kerneldlls"
)

:: 复制kerneldlls目录下的所有文件
xcopy "%SOURCE_DIR%\kerneldlls\*" "%TARGET_DIR%\kerneldlls\" /E /Y >nul
if errorlevel 1 (
    echo 错误: 无法复制 kerneldlls 目录
    pause
    exit /b 1
)
echo ✓ 复制 kerneldlls 目录

:: 复制整个zlgcan_x64目录结构
if not exist "%TARGET_DIR%\zlgcan_x64" (
    mkdir "%TARGET_DIR%\zlgcan_x64"
)

xcopy "%SOURCE_DIR%\*" "%TARGET_DIR%\zlgcan_x64\" /E /Y >nul
if errorlevel 1 (
    echo 错误: 无法复制 zlgcan_x64 目录
    pause
    exit /b 1
)
echo ✓ 复制 zlgcan_x64 目录

echo.
echo ========================================
echo           复制完成！
echo ========================================
echo.
echo 已复制的文件:
echo - zlgcan.dll (主库文件)
echo - kerneldlls\ (依赖库目录)
echo - zlgcan_x64\ (完整库目录)
echo.
echo 现在可以运行程序测试CANFD连接了。
echo.
pause
