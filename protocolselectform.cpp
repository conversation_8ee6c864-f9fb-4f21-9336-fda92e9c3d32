/**
 * @file protocolselectform.cpp
 * @brief 协议选择窗口类实现文件
 * @details 实现了ProtocolSelectForm类的所有功能，包括界面初始化、
 *          协议选择处理、子窗口管理等核心功能。
 * <AUTHOR>
 * @date 2025-07-02
 * @version 1.0
 */

#include "protocolselectform.h"     // 协议选择窗口类声明
#include "ui_protocolselectform.h"  // Qt Designer生成的UI类
#include <QDebug>                   // Qt调试输出类
#include "modbusform.h"             // Modbus协议窗口类
#include <QMessageBox>              // Qt消息框类

/**
 * @brief ProtocolSelectForm构造函数实现
 * @param parent 父窗口指针
 * @details 初始化协议选择窗口的所有组件和设置：
 *          1. 调用基类构造函数
 *          2. 创建UI实例并设置界面
 *          3. 初始化所有子窗口指针为空
 *          4. 设置窗口标题和信号槽连接
 */
ProtocolSelectForm::ProtocolSelectForm(QWidget *parent) :
    QWidget(parent),                        // 调用基类构造函数
    ui(new Ui::ProtocolSelectForm),         // 创建UI实例
    modbusForm(nullptr),                    // 初始化Modbus窗口指针为空
    canForm(nullptr),                       // 初始化CAN窗口指针为空
    canConfigDialog(nullptr),               // 初始化CAN配置对话框指针为空
    canInterface(nullptr),                  // 初始化CAN接口指针为空
    canfdInterface(nullptr)                 // 初始化CANFD接口指针为空
{
    // 设置UI界面，加载由Qt Designer设计的界面布局
    ui->setupUi(this);

    // 设置窗口标题
    setWindowTitle("协议选择");

    // 连接按钮点击信号到相应的槽函数
    connect(ui->modbusButton, &QPushButton::clicked, this, &ProtocolSelectForm::on_modbusButton_clicked);
    connect(ui->canButton, &QPushButton::clicked, this, &ProtocolSelectForm::on_canButton_clicked);
    connect(ui->backButton, &QPushButton::clicked, this, &ProtocolSelectForm::on_backButton_clicked);
}

/**
 * @brief ProtocolSelectForm析构函数实现
 * @details 清理协议选择窗口占用的所有资源：
 *          1. 释放UI对象内存
 *          2. 释放所有子窗口对象内存（如果存在）
 *          3. 释放所有通信接口对象内存（如果存在）
 */
ProtocolSelectForm::~ProtocolSelectForm()
{
    // 释放UI对象占用的内存
    delete ui;

    // 安全释放Modbus窗口内存，防止重复删除
    if (modbusForm) {
        delete modbusForm;
        modbusForm = nullptr;
    }

    // 安全释放CAN窗口内存，防止重复删除
    if (canForm) {
        delete canForm;
        canForm = nullptr;
    }

    // 安全释放CAN配置对话框内存，防止重复删除
    if (canConfigDialog) {
        delete canConfigDialog;
        canConfigDialog = nullptr;
    }

    // 安全释放CAN接口内存，防止重复删除
    if (canInterface) {
        delete canInterface;
        canInterface = nullptr;
    }

    // 安全释放CANFD接口内存，防止重复删除
    if (canfdInterface) {
        delete canfdInterface;
        canfdInterface = nullptr;
    }
}

/**
 * @brief Modbus协议按钮点击事件处理函数
 * @details 响应用户点击Modbus协议按钮的操作：
 *          1. 如果Modbus窗口未创建，则创建新实例
 *          2. 连接Modbus窗口的返回信号到处理槽函数
 *          3. 显示Modbus窗口并隐藏当前窗口
 */
void ProtocolSelectForm::on_modbusButton_clicked()
{
    // 如果Modbus窗口尚未创建，则创建新实例
    if (!modbusForm) {
        modbusForm = new ModbusForm();
        // 连接Modbus窗口的返回信号到协议选择窗口的处理槽函数
        connect(modbusForm, &ModbusForm::backToProtocolSelect,
                this, &ProtocolSelectForm::onModbusBack);
    }

    // 显示Modbus窗口
    modbusForm->show();
    // 隐藏当前协议选择窗口，实现窗口切换效果
    this->hide();
}

/**
 * @brief CAN/CANFD协议按钮点击事件处理函数
 * @details 响应用户点击CAN/CANFD协议按钮的操作：
 *          1. 清理之前的配置对话框实例（如果存在）
 *          2. 创建新的CAN配置对话框
 *          3. 连接对话框的接受和拒绝信号
 *          4. 设置默认配置参数并显示对话框
 */
void ProtocolSelectForm::on_canButton_clicked()
{
    qDebug() << "CAN按钮被点击";

    // 每次都创建新的对话框，避免重复使用可能导致的问题
    if (canConfigDialog) {
        delete canConfigDialog;         // 删除旧的对话框实例
        canConfigDialog = nullptr;      // 重置指针为空
    }

    // 创建新的CAN配置对话框实例（UI版本）
    canConfigDialog = new CANConfigNew(this);

    // 连接对话框接受信号，当用户点击确定按钮时处理
    connect(canConfigDialog, &QDialog::accepted, [this]() {
        qDebug() << "配置对话框被接受";
        // 获取用户配置的参数数据
        CANConfigData config = canConfigDialog->getConfigData();
        // 调用配置确认处理函数
        onConfigConfirmed(config, config.protocol);

        // 关闭并删除对话框，释放资源
        canConfigDialog->close();
        canConfigDialog->deleteLater();
        canConfigDialog = nullptr;
    });

    // 连接对话框拒绝信号，当用户点击取消按钮时处理
    connect(canConfigDialog, &QDialog::rejected, [this]() {
        qDebug() << "配置对话框被取消";
        // 删除对话框并重置指针
        canConfigDialog->deleteLater();
        canConfigDialog = nullptr;
    });

    // 设置CAN配置对话框的默认参数值
    CANConfigData defaultConfig;
    defaultConfig.protocol = "CAN";                    // 默认协议类型为CAN
    defaultConfig.arbitrationBaud = "500Kbps";         // 默认仲裁波特率为500Kbps
    defaultConfig.dataBaud = "2Mbps";                  // 默认数据波特率为2Mbps
    defaultConfig.workMode = "正常模式";                // 默认工作模式为正常模式
    defaultConfig.terminalResistance = true;           // 默认启用终端电阻

    // 将默认配置应用到对话框
    canConfigDialog->setConfigData(defaultConfig);

    qDebug() << "显示配置对话框";
    // 使用show()显示非模态对话框，而不是exec()显示模态对话框
    canConfigDialog->show();
}

/**
 * @brief 返回按钮点击事件处理函数
 * @details 响应用户点击返回按钮，发出返回主界面的信号
 */
void ProtocolSelectForm::on_backButton_clicked()
{
    // 发出返回主界面信号，通知主窗口进行界面切换
    emit backToMain();
}

/**
 * @brief Modbus窗口返回事件处理函数
 * @details 处理从Modbus窗口返回到协议选择界面的操作：
 *          1. 隐藏Modbus窗口
 *          2. 重新显示协议选择窗口
 */
void ProtocolSelectForm::onModbusBack()
{
    // 如果Modbus窗口存在，则隐藏它
    if (modbusForm) {
        modbusForm->hide();
    }
    // 重新显示协议选择窗口
    this->show();
}

/**
 * @brief CAN窗口返回事件处理函数
 * @details 处理从CAN/CANFD窗口返回到协议选择界面的操作：
 *          1. 隐藏CAN接口窗口（如果存在）
 *          2. 隐藏CANFD接口窗口（如果存在）
 *          3. 重新显示协议选择窗口
 */
void ProtocolSelectForm::onCANBack()
{
    // 如果CAN接口窗口存在，则隐藏它
    if (canInterface) {
        canInterface->hide();
    }

    // 如果CANFD接口窗口存在，则隐藏它
    if (canfdInterface) {
        canfdInterface->hide();
    }

    // 重新显示协议选择窗口
    this->show();
}



/**
 * @brief CAN配置确认事件处理函数
 * @param config CAN配置数据结构，包含用户设置的所有参数
 * @param protocol 协议类型字符串，"CAN"或"CANFD"
 * @details 处理CAN配置对话框确认后的操作：
 *          1. 根据协议类型创建相应的通信接口
 *          2. 设置配置参数到通信接口
 *          3. 显示通信接口窗口并隐藏当前窗口
 */
void ProtocolSelectForm::onConfigConfirmed(const CANConfigData &config, const QString &protocol)
{
    qDebug() << "onConfigConfirmed: 协议=" << protocol;

    // 配置对话框已经自动关闭，不需要手动关闭

    if (protocol == "CAN") {
        // === CAN协议处理 ===
        qDebug() << "创建CAN界面";

        // 如果CAN接口尚未创建，则创建新实例
        if (!canInterface) {
            qDebug() << "创建新的CANInterface实例";
            canInterface = new CANInterface();  // 不设置父窗口，让它独立显示
            qDebug() << "CANInterface创建完成，连接信号";

            // 连接CAN接口的返回信号到协议选择窗口的处理槽函数
            connect(canInterface, &CANInterface::backToProtocolSelect,
                    this, &ProtocolSelectForm::onCANBack);
            qDebug() << "信号连接完成";
        }

        qDebug() << "设置配置数据";
        // 将用户配置的参数应用到CAN接口
        canInterface->setConfigData(config);

        qDebug() << "显示CAN界面";
        canInterface->showMaximized();  // 使用最大化显示
        canInterface->raise();          // 提升窗口到前台
        canInterface->activateWindow(); // 激活窗口获得焦点
        qDebug() << "CAN界面显示完成";

    } else if (protocol == "CANFD") {
        // === CANFD协议处理 ===
        qDebug() << "创建CANFD界面";

        // 如果CANFD接口尚未创建，则创建新实例
        if (!canfdInterface) {
            qDebug() << "创建新的CANFDInterface实例";
            canfdInterface = new CANFDInterface();  // 不设置父窗口，让它独立显示
            qDebug() << "CANFDInterface创建完成，连接信号";

            // 连接CANFD接口的返回信号到协议选择窗口的处理槽函数
            connect(canfdInterface, &CANFDInterface::backToProtocolSelect,
                    this, &ProtocolSelectForm::onCANBack);
            qDebug() << "信号连接完成";
        }

        qDebug() << "设置配置数据";
        // 将用户配置的参数应用到CANFD接口
        canfdInterface->setConfigData(config);

        qDebug() << "显示CANFD界面";
        canfdInterface->showMaximized();  // 使用最大化显示
        canfdInterface->raise();          // 提升窗口到前台
        canfdInterface->activateWindow(); // 激活窗口获得焦点
        qDebug() << "CANFD界面显示完成";
    }

    qDebug() << "隐藏协议选择界面";
    // 隐藏当前协议选择窗口，完成界面切换
    this->hide();
    qDebug() << "onConfigConfirmed完成";
}