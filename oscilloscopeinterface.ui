<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>OscilloscopeInterface</class>
 <widget class="QWidget" name="OscilloscopeInterface">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>1200</width>
    <height>800</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>虚拟示波器 - 电机数据监测</string>
  </property>
  <property name="styleSheet">
   <string>/* 主界面背景 - 红白配色 */
QWidget {
    background-color: #ffffff;
    color: #333333;
    font-family: "Microsoft YaHei UI", "Segoe UI", Arial, sans-serif;
    font-size: 14px;
}

/* 分组框样式 - 红色边框 */
QGroupBox {
    border: 2px solid #dc3545;
    border-radius: 8px;
    margin-top: 15px;
    padding-top: 15px;
    font-weight: bold;
    color: #dc3545;
    background-color: #ffffff;
}

QGroupBox::title {
    subcontrol-origin: margin;
    left: 15px;
    padding: 0 8px 0 8px;
    color: #dc3545;
    font-weight: bold;
}

/* 按钮样式 - 红白配色 */
QPushButton {
    background-color: #ffffff;
    border: 2px solid #dc3545;
    border-radius: 6px;
    padding: 8px 15px;
    min-height: 30px;
    color: #dc3545;
    font-weight: bold;
    font-size: 14px;
}

QPushButton:hover {
    background-color: #dc3545;
    color: #ffffff;
}

QPushButton:pressed {
    background-color: #c82333;
    border-color: #bd2130;
    color: #ffffff;
}

QPushButton:checked {
    background-color: #dc3545;
    border-color: #dc3545;
    color: #ffffff;
}

/* 标签样式 */
QLabel {
    color: #333333;
    font-size: 14px;
}

/* 输入控件样式 */
QSpinBox, QDoubleSpinBox, QComboBox {
    background-color: #ffffff;
    border: 2px solid #dc3545;
    border-radius: 6px;
    padding: 6px;
    min-height: 25px;
    color: #333333;
    font-size: 14px;
}

QSpinBox:focus, QDoubleSpinBox:focus, QComboBox:focus {
    border-color: #c82333;
}

/* 复选框样式 */
QCheckBox {
    spacing: 8px;
    color: #333333;
    font-size: 14px;
}

QCheckBox::indicator {
    width: 18px;
    height: 18px;
}

QCheckBox::indicator:unchecked {
    border: 2px solid #dc3545;
    background-color: #ffffff;
    border-radius: 3px;
}

QCheckBox::indicator:checked {
    border: 2px solid #dc3545;
    background-color: #dc3545;
    border-radius: 3px;
}</string>
  </property>
  <layout class="QHBoxLayout" name="horizontalLayout_main">
   <item>
    <!-- 左侧控制面板 -->
    <widget class="QWidget" name="controlPanel">
     <property name="maximumWidth">
      <number>250</number>
     </property>
     <property name="minimumWidth">
      <number>220</number>
     </property>
     <layout class="QVBoxLayout" name="verticalLayout_control">
      <!-- 数据源状态 -->
      <item>
       <widget class="QGroupBox" name="dataSourceGroup">
        <property name="title">
         <string>数据源状态</string>
        </property>
        <layout class="QVBoxLayout" name="verticalLayout_dataSource">
         <item>
          <widget class="QLabel" name="dataSourceLabel">
           <property name="text">
            <string>当前数据源: 模拟数据</string>
           </property>
           <property name="styleSheet">
            <string>QLabel {
    color: #ffa500;
    font-weight: bold;
    font-size: 12px;
}</string>
           </property>
          </widget>
         </item>
         <item>
          <layout class="QHBoxLayout" name="horizontalLayout_dataControl">
           <item>
            <widget class="QPushButton" name="startSimulationButton">
             <property name="text">
              <string>开始模拟</string>
             </property>
             <property name="checkable">
              <bool>true</bool>
             </property>
            </widget>
           </item>
          </layout>
         </item>
        </layout>
       </widget>
      </item>
      
      <!-- 通道控制 -->
      <item>
       <widget class="QGroupBox" name="channelGroup">
        <property name="title">
         <string>通道控制</string>
        </property>
        <layout class="QVBoxLayout" name="verticalLayout_channels">
         <!-- 电气参数通道 -->
         <item>
          <widget class="QLabel" name="electricLabel">
           <property name="text">
            <string>电气参数</string>
           </property>
           <property name="styleSheet">
            <string>font-weight: bold; color: #00ff00;</string>
           </property>
          </widget>
         </item>
         <item>
          <layout class="QGridLayout" name="gridLayout_electric">
           <item row="0" column="0">
            <widget class="QCheckBox" name="currentACheckBox">
             <property name="text">
              <string>电流A相</string>
             </property>
             <property name="checked">
              <bool>true</bool>
             </property>
            </widget>
           </item>
           <item row="0" column="1">
            <widget class="QCheckBox" name="currentBCheckBox">
             <property name="text">
              <string>电流B相</string>
             </property>
             <property name="checked">
              <bool>true</bool>
             </property>
            </widget>
           </item>
           <item row="1" column="0">
            <widget class="QCheckBox" name="currentCCheckBox">
             <property name="text">
              <string>电流C相</string>
             </property>
             <property name="checked">
              <bool>true</bool>
             </property>
            </widget>
           </item>
           <item row="1" column="1">
            <widget class="QCheckBox" name="voltageCheckBox">
             <property name="text">
              <string>母线电压</string>
             </property>
             <property name="checked">
              <bool>true</bool>
             </property>
            </widget>
           </item>
          </layout>
         </item>
         
         <!-- 机械参数通道 -->
         <item>
          <widget class="QLabel" name="mechanicalLabel">
           <property name="text">
            <string>机械参数</string>
           </property>
           <property name="styleSheet">
            <string>font-weight: bold; color: #ff6600;</string>
           </property>
          </widget>
         </item>
         <item>
          <layout class="QGridLayout" name="gridLayout_mechanical">
           <item row="0" column="0">
            <widget class="QCheckBox" name="speedCheckBox">
             <property name="text">
              <string>转速</string>
             </property>
             <property name="checked">
              <bool>true</bool>
             </property>
            </widget>
           </item>
           <item row="0" column="1">
            <widget class="QCheckBox" name="torqueCheckBox">
             <property name="text">
              <string>转矩</string>
             </property>
             <property name="checked">
              <bool>true</bool>
             </property>
            </widget>
           </item>
           <item row="1" column="0">
            <widget class="QCheckBox" name="positionCheckBox">
             <property name="text">
              <string>位置</string>
             </property>
            </widget>
           </item>
           <item row="1" column="1">
            <widget class="QCheckBox" name="vibrationCheckBox">
             <property name="text">
              <string>振动</string>
             </property>
            </widget>
           </item>
          </layout>
         </item>
         
         <!-- 温度参数通道 -->
         <item>
          <widget class="QLabel" name="temperatureLabel">
           <property name="text">
            <string>温度参数</string>
           </property>
           <property name="styleSheet">
            <string>font-weight: bold; color: #ff0066;</string>
           </property>
          </widget>
         </item>
         <item>
          <layout class="QGridLayout" name="gridLayout_temperature">
           <item row="0" column="0">
            <widget class="QCheckBox" name="motorTempCheckBox">
             <property name="text">
              <string>电机温度</string>
             </property>
            </widget>
           </item>
           <item row="0" column="1">
            <widget class="QCheckBox" name="controllerTempCheckBox">
             <property name="text">
              <string>控制器温度</string>
             </property>
            </widget>
           </item>
          </layout>
         </item>
        </layout>
       </widget>
      </item>
      
      <!-- 时间轴控制 -->
      <item>
       <widget class="QGroupBox" name="timeAxisGroup">
        <property name="title">
         <string>时间轴设置</string>
        </property>
        <layout class="QFormLayout" name="formLayout_timeAxis">
         <item row="0" column="0">
          <widget class="QLabel" name="timeScaleLabel">
           <property name="text">
            <string>时间刻度:</string>
           </property>
          </widget>
         </item>
         <item row="0" column="1">
          <widget class="QComboBox" name="timeScaleComboBox">
           <item>
            <property name="text">
             <string>1s/div</string>
            </property>
           </item>
           <item>
            <property name="text">
             <string>500ms/div</string>
            </property>
           </item>
           <item>
            <property name="text">
             <string>200ms/div</string>
            </property>
           </item>
           <item>
            <property name="text">
             <string>100ms/div</string>
            </property>
           </item>
           <item>
            <property name="text">
             <string>50ms/div</string>
            </property>
           </item>
          </widget>
         </item>
         <item row="1" column="0">
          <widget class="QLabel" name="sampleRateLabel">
           <property name="text">
            <string>采样率:</string>
           </property>
          </widget>
         </item>
         <item row="1" column="1">
          <widget class="QSpinBox" name="sampleRateSpinBox">
           <property name="suffix">
            <string> Hz</string>
           </property>
           <property name="minimum">
            <number>10</number>
           </property>
           <property name="maximum">
            <number>10000</number>
           </property>
           <property name="value">
            <number>100</number>
           </property>
          </widget>
         </item>
         <item row="2" column="0">
          <widget class="QLabel" name="updateRateLabel">
           <property name="text">
            <string>更新频率:</string>
           </property>
          </widget>
         </item>
         <item row="2" column="1">
          <widget class="QSpinBox" name="updateRateSpinBox">
           <property name="suffix">
            <string> Hz</string>
           </property>
           <property name="minimum">
            <number>1</number>
           </property>
           <property name="maximum">
            <number>60</number>
           </property>
           <property name="value">
            <number>20</number>
           </property>
          </widget>
         </item>
        </layout>
       </widget>
      </item>
      
      <!-- 触发设置 -->
      <item>
       <widget class="QGroupBox" name="triggerGroup">
        <property name="title">
         <string>触发设置</string>
        </property>
        <layout class="QFormLayout" name="formLayout_trigger">
         <item row="0" column="0">
          <widget class="QLabel" name="triggerModeLabel">
           <property name="text">
            <string>触发模式:</string>
           </property>
          </widget>
         </item>
         <item row="0" column="1">
          <widget class="QComboBox" name="triggerModeComboBox">
           <item>
            <property name="text">
             <string>自动</string>
            </property>
           </item>
           <item>
            <property name="text">
             <string>正常</string>
            </property>
           </item>
           <item>
            <property name="text">
             <string>单次</string>
            </property>
           </item>
          </widget>
         </item>
         <item row="1" column="0">
          <widget class="QLabel" name="triggerLevelLabel">
           <property name="text">
            <string>触发电平:</string>
           </property>
          </widget>
         </item>
         <item row="1" column="1">
          <widget class="QDoubleSpinBox" name="triggerLevelSpinBox">
           <property name="decimals">
            <number>2</number>
           </property>
           <property name="minimum">
            <number>-100.000000000000000</number>
           </property>
           <property name="maximum">
            <number>100.000000000000000</number>
           </property>
           <property name="value">
            <number>0.000000000000000</number>
           </property>
          </widget>
         </item>
        </layout>
       </widget>
      </item>
      
      <!-- 控制按钮 -->
      <item>
       <layout class="QHBoxLayout" name="horizontalLayout_buttons">
        <item>
         <widget class="QPushButton" name="pauseButton">
          <property name="text">
           <string>暂停</string>
          </property>
          <property name="checkable">
           <bool>true</bool>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QPushButton" name="clearButton">
          <property name="text">
           <string>清除</string>
          </property>
         </widget>
        </item>
       </layout>
      </item>
      
      <!-- 返回按钮 -->
      <item>
       <widget class="QPushButton" name="backButton">
        <property name="text">
         <string>返回主界面</string>
        </property>
        <property name="styleSheet">
         <string>QPushButton {
    background-color: #dc3545;
    border: 2px solid #dc3545;
    border-radius: 6px;
    color: #ffffff;
    font-weight: bold;
    font-size: 14px;
    padding: 8px 15px;
    min-height: 30px;
}

QPushButton:hover {
    background-color: #c82333;
    border-color: #bd2130;
    color: #ffffff;
}

QPushButton:pressed {
    background-color: #bd2130;
    border-color: #b21f2d;
    color: #ffffff;
}</string>
        </property>
       </widget>
      </item>
      
      <!-- 弹性空间 -->
      <item>
       <spacer name="verticalSpacer">
        <property name="orientation">
         <enum>Qt::Vertical</enum>
        </property>
        <property name="sizeHint" stdset="0">
         <size>
          <width>20</width>
          <height>40</height>
         </size>
        </property>
       </spacer>
      </item>
     </layout>
    </widget>
   </item>
   
   <!-- 右侧波形显示区域 -->
   <item>
    <widget class="QWidget" name="waveformArea">
     <layout class="QVBoxLayout" name="verticalLayout_waveform">
      <!-- 状态栏 -->
      <item>
       <widget class="QWidget" name="statusBar">
        <property name="maximumHeight">
         <number>40</number>
        </property>
        <property name="minimumHeight">
         <number>40</number>
        </property>
        <property name="styleSheet">
         <string>QWidget {
    background-color: #f8f9fa;
    border: 1px solid #dc3545;
    border-radius: 6px;
}
QLabel {
    color: #333333;
    font-weight: bold;
    padding: 5px;
}</string>
        </property>
        <layout class="QHBoxLayout" name="horizontalLayout_status">
         <item>
          <widget class="QLabel" name="connectionStatusLabel">
           <property name="text">
            <string>连接状态: 未连接</string>
           </property>
          </widget>
         </item>
         <item>
          <spacer name="horizontalSpacer">
           <property name="orientation">
            <enum>Qt::Horizontal</enum>
           </property>
           <property name="sizeHint" stdset="0">
            <size>
             <width>40</width>
             <height>20</height>
            </size>
           </property>
          </spacer>
         </item>
         <item>
          <widget class="QLabel" name="samplingRateLabel">
           <property name="text">
            <string>采样率: 100 Hz</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QLabel" name="dataCountLabel">
           <property name="text">
            <string>数据点: 0</string>
           </property>
          </widget>
         </item>
        </layout>
       </widget>
      </item>
      
      <!-- 波形显示区域 - 这里将用自定义的QWidget来绘制波形 -->
      <item>
       <widget class="QWidget" name="plotWidget">
        <property name="minimumSize">
         <size>
          <width>800</width>
          <height>600</height>
         </size>
        </property>
        <property name="styleSheet">
         <string>QWidget {
    background-color: #ffffff;
    border: 2px solid #dc3545;
    border-radius: 8px;
}</string>
        </property>
       </widget>
      </item>
     </layout>
    </widget>
   </item>
  </layout>
 </widget>
 <resources/>
 <connections/>
</ui>
