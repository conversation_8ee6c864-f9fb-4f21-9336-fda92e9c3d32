/**
 * @file cancommform.cpp
 * @brief CAN通信表单类实现文件
 * @details 实现了CANCommForm类的所有功能，包括CAN协议的用户界面创建、
 *          设备连接管理、数据收发处理、心跳功能、频率控制、日志记录等
 *          完整的CAN通信功能。该类使用代码方式创建UI界面。
 * <AUTHOR>
 * @date 2025-07-02
 * @version 1.0
 */

#include "cancommform.h"  // CAN通信表单类声明

/**
 * @brief CANCommForm构造函数实现
 * @param parent 父窗口指针
 * @details 初始化CAN通信表单的所有组件和设置：
 *          1. 初始化成员变量和状态
 *          2. 设置窗口属性
 *          3. 创建UI界面
 *          4. 建立信号槽连接
 *          5. 创建和配置定时器
 *          6. 更新控件状态
 */
CANCommForm::CANCommForm(QWidget *parent)
    : QWidget(parent)                                    // 调用基类构造函数
    , isConnected(false)                                // 初始化连接状态为未连接
    , isHeartbeatRunning(false)                         // 初始化心跳状态为未运行
    , sendCount(0)                                      // 初始化发送计数器
    , receiveCount(0)                                   // 初始化接收计数器
{
    // 设置窗口标题和大小
    setWindowTitle("CAN/CANFD通信");
    resize(800, 600);

    // 创建用户界面
    setupUI();

    // 建立信号槽连接
    setupConnections();

    // 更新控件状态
    updateControlState();

    // === 初始化定时器 ===
    // 创建心跳定时器，用于定时发送心跳数据包
    heartbeatTimer = new QTimer(this);
    heartbeatTimer->setInterval(1000); // 默认1秒发送一次心跳包

    // 创建接收定时器，用于定时检查接收数据
    receiveTimer = new QTimer(this);
    receiveTimer->setInterval(50); // 50ms检查一次接收数据

    // 创建发送定时器，用于定时发送数据
    sendTimer = new QTimer(this);
    sendTimer->setSingleShot(false);  // 设置为重复触发模式
}

/**
 * @brief CANCommForm析构函数实现
 * @details 清理资源和关闭连接：
 *          - 检查连接状态并停止通信
 *          - 释放内存资源
 */
CANCommForm::~CANCommForm()
{
    // 如果设备已连接，先停止通信
    if (isConnected) {
        stopCommunication();
    }
}

void CANCommForm::setupUI()
{
    mainLayout = new QVBoxLayout(this);
    
    // 顶部控制区
    controlGroup = new QGroupBox("通信控制");
    controlLayout = new QGridLayout(controlGroup);
    
    int row = 0;
    
    // 协议显示
    controlLayout->addWidget(new QLabel("协议:"), row, 0);
    protocolLabel = new QLabel("CAN");
    protocolLabel->setStyleSheet("font-weight: bold; color: blue;");
    controlLayout->addWidget(protocolLabel, row++, 1);
    
    // 连接状态
    controlLayout->addWidget(new QLabel("状态:"), row, 0);
    statusLabel = new QLabel("未连接");
    statusLabel->setStyleSheet("color: red;");
    controlLayout->addWidget(statusLabel, row++, 1);
    
    // CAN ID设置
    controlLayout->addWidget(new QLabel("CAN ID:"), row, 0);
    canIdEdit = new QLineEdit("123");
    canIdEdit->setPlaceholderText("输入十六进制ID，如: 123");
    controlLayout->addWidget(canIdEdit, row++, 1);
    
    // 发送数据
    controlLayout->addWidget(new QLabel("发送数据:"), row, 0);
    sendDataEdit = new QLineEdit("00 11 22 33 44 55 66 77");
    sendDataEdit->setPlaceholderText("输入十六进制数据，空格分隔");
    controlLayout->addWidget(sendDataEdit, row++, 1);
    
    // 发送频率
    controlLayout->addWidget(new QLabel("发送频率(ms):"), row, 0);
    sendFrequencySpinBox = new QSpinBox();
    sendFrequencySpinBox->setRange(10, 10000);
    sendFrequencySpinBox->setValue(1000);
    sendFrequencySpinBox->setSuffix(" ms");
    controlLayout->addWidget(sendFrequencySpinBox, row++, 1);
    
    // 控制按钮
    QHBoxLayout *buttonLayout1 = new QHBoxLayout();
    connectButton = new QPushButton("连接设备");
    disconnectButton = new QPushButton("断开连接");
    sendButton = new QPushButton("发送数据");
    heartbeatButton = new QPushButton("发送心跳包");
    
    buttonLayout1->addWidget(connectButton);
    buttonLayout1->addWidget(disconnectButton);
    buttonLayout1->addWidget(sendButton);
    buttonLayout1->addWidget(heartbeatButton);
    buttonLayout1->addStretch();
    
    controlLayout->addLayout(buttonLayout1, row++, 0, 1, 2);
    
    mainLayout->addWidget(controlGroup);
    
    // 数据显示区
    dataSplitter = new QSplitter(Qt::Horizontal);
    
    // 发送区域
    sendGroup = new QGroupBox("发送数据");
    QVBoxLayout *sendLayout = new QVBoxLayout(sendGroup);
    sendTextEdit = new QTextEdit();
    sendTextEdit->setMaximumHeight(200);
    sendTextEdit->setReadOnly(true);
    sendLayout->addWidget(sendTextEdit);
    
    // 接收区域
    receiveGroup = new QGroupBox("接收数据");
    QVBoxLayout *receiveLayout = new QVBoxLayout(receiveGroup);
    receiveTextEdit = new QTextEdit();
    receiveTextEdit->setMaximumHeight(200);
    receiveTextEdit->setReadOnly(true);
    receiveLayout->addWidget(receiveTextEdit);
    
    dataSplitter->addWidget(sendGroup);
    dataSplitter->addWidget(receiveGroup);
    dataSplitter->setStretchFactor(0, 1);
    dataSplitter->setStretchFactor(1, 1);
    
    mainLayout->addWidget(dataSplitter);
    
    // 底部按钮区
    bottomLayout = new QHBoxLayout();
    clearSendButton = new QPushButton("清空发送区");
    clearReceiveButton = new QPushButton("清空接收区");
    saveDataButton = new QPushButton("保存数据");
    backButton = new QPushButton("返回");
    
    bottomLayout->addWidget(clearSendButton);
    bottomLayout->addWidget(clearReceiveButton);
    bottomLayout->addWidget(saveDataButton);
    bottomLayout->addStretch();
    bottomLayout->addWidget(backButton);
    
    mainLayout->addLayout(bottomLayout);
}

void CANCommForm::setupConnections()
{
    connect(connectButton, &QPushButton::clicked, this, &CANCommForm::onConnectDevice);
    connect(disconnectButton, &QPushButton::clicked, this, &CANCommForm::onDisconnectDevice);
    connect(sendButton, &QPushButton::clicked, this, &CANCommForm::onSendData);
    connect(heartbeatButton, &QPushButton::clicked, this, &CANCommForm::onSendHeartbeat);
    connect(clearSendButton, &QPushButton::clicked, this, &CANCommForm::onClearSendArea);
    connect(clearReceiveButton, &QPushButton::clicked, this, &CANCommForm::onClearReceiveArea);
    connect(saveDataButton, &QPushButton::clicked, this, &CANCommForm::onSaveData);
    connect(backButton, &QPushButton::clicked, this, &CANCommForm::onBackClicked);

    connect(sendFrequencySpinBox, QOverload<int>::of(&QSpinBox::valueChanged),
            this, &CANCommForm::onSendFrequencyChanged);

    connect(heartbeatTimer, &QTimer::timeout, this, &CANCommForm::onHeartbeatTimer);
    connect(receiveTimer, &QTimer::timeout, this, &CANCommForm::onReceiveTimer);
}

void CANCommForm::updateControlState()
{
    connectButton->setEnabled(!isConnected);
    disconnectButton->setEnabled(isConnected);
    sendButton->setEnabled(isConnected);

    if (isHeartbeatRunning) {
        heartbeatButton->setText("停止心跳包发送");
        heartbeatButton->setStyleSheet("background-color: #ff6b6b; color: white;");  // 心跳运行时红色样式
    } else {
        heartbeatButton->setText("发送心跳包");                   // 心跳停止时显示发送文本
        heartbeatButton->setStyleSheet("");                      // 恢复默认样式
    }
    heartbeatButton->setEnabled(isConnected);                    // 连接后才能使用心跳功能

    // === 连接状态标签更新 ===
    if (isConnected) {
        statusLabel->setText("已连接");                          // 显示已连接状态
        statusLabel->setStyleSheet("color: green; font-weight: bold;");
    } else {
        statusLabel->setText("未连接");                          // 显示未连接状态
        statusLabel->setStyleSheet("color: red;");
    }
}

/**
 * @brief 设置配置数据函数实现
 * @param config CAN配置数据结构
 * @details 设置通信配置参数并调整界面：
 *          1. 保存配置数据
 *          2. 更新协议显示标签
 *          3. 根据协议类型调整窗口标题和特殊配置
 */
void CANCommForm::setConfigData(const CANConfigData &config)
{
    // === 第一步：保存配置数据 ===
    configData = config;                                         // 保存配置参数
    protocolLabel->setText(config.protocol);                     // 更新协议显示标签

    // === 第二步：根据协议类型调整界面 ===
    if (config.protocol == "CANFD") {
        setWindowTitle("CANFD通信");                             // 设置CANFD窗口标题
        // CANFD模式下可以添加特有的配置和功能
    } else {
        setWindowTitle("CAN通信");                               // 设置CAN窗口标题
    }
}

/**
 * @brief 连接设备槽函数实现
 * @details 执行设备连接操作：
 *          1. 设置连接状态为已连接
 *          2. 更新UI控件状态
 *          3. 启动通信服务
 *          4. 记录连接信息和配置参数
 *          5. 显示连接成功提示
 */
void CANCommForm::onConnectDevice()
{
    // === 第一步：设置连接状态 ===
    isConnected = true;                                          // 设置连接状态为已连接

    // === 第二步：更新UI控件状态 ===
    updateControlState();                                        // 更新按钮和控件状态

    // === 第三步：启动通信服务 ===
    startCommunication();                                        // 启动接收定时器等通信服务

    // === 第四步：记录连接信息 ===
    appendLog("设备连接成功", false);
    appendLog(QString("协议: %1").arg(configData.protocol), false);
    appendLog(QString("仲裁域波特率: %1").arg(configData.arbitrationBaud), false);
    if (configData.protocol == "CANFD") {
        appendLog(QString("数据域波特率: %1").arg(configData.dataBaud), false);
    }

    // === 第五步：显示连接成功提示 ===
    QMessageBox msgBox(this);
    msgBox.setWindowTitle("提示");
    msgBox.setIcon(QMessageBox::Information);
    msgBox.setText("设备连接成功！");
    msgBox.setStandardButtons(QMessageBox::Ok);

    // 设置现代化样式
    msgBox.setStyleSheet(
        "QMessageBox {"
        "    background: qlineargradient(x1:0, y1:0, x2:0, y2:1, "
        "                               stop:0 #2c3e50, stop:1 #34495e);"
        "    color: #ecf0f1;"
        "    border-radius: 10px;"
        "}"
        "QMessageBox QLabel {"
        "    color: #ecf0f1;"
        "    font-family: 'Microsoft YaHei UI';"
        "    font-size: 12px;"
        "    background: transparent;"
        "    padding: 10px;"
        "}"
        "QMessageBox QPushButton {"
        "    background: qlineargradient(x1:0, y1:0, x2:0, y2:1, "
        "                               stop:0 #27ae60, stop:1 #229954);"
        "    color: white;"
        "    border: none;"
        "    border-radius: 8px;"
        "    padding: 8px 20px;"
        "    font-family: 'Microsoft YaHei UI';"
        "    font-size: 11px;"
        "    font-weight: bold;"
        "    min-width: 80px;"
        "}"
        "QMessageBox QPushButton:hover {"
        "    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,"
        "                               stop:0 #2ecc71, stop:1 #27ae60);"
        "}"
        "QMessageBox QPushButton:pressed {"
        "    background: qlineargradient(x1:0, y1:0, x2:0, y2:1, "
        "                               stop:0 #229954, stop:1 #1e8449);"
        "}"
    );

    msgBox.exec();
}

/**
 * @brief 断开设备槽函数实现
 * @details 执行设备断开操作：
 *          1. 停止通信服务
 *          2. 设置连接状态为未连接
 *          3. 更新UI控件状态
 *          4. 记录断开信息并显示提示
 */
void CANCommForm::onDisconnectDevice()
{
    // === 第一步：停止通信服务 ===
    stopCommunication();                                         // 停止接收定时器等通信服务

    // === 第二步：设置连接状态 ===
    isConnected = false;                                         // 设置连接状态为未连接

    // === 第三步：更新UI控件状态 ===
    updateControlState();                                        // 更新按钮和控件状态

    // === 第四步：记录断开信息 ===
    appendLog("设备已断开连接", false);
    QMessageBox::information(this, "提示", "设备已断开连接！");
}

/**
 * @brief 发送数据槽函数实现
 * @details 执行数据发送操作：
 *          1. 检查设备连接状态
 *          2. 获取和验证用户输入数据
 *          3. 验证数据格式和长度
 *          4. 发送数据并记录日志
 */
void CANCommForm::onSendData()
{
    // === 第一步：检查设备连接状态 ===
    if (!isConnected) {
        QMessageBox::warning(this, "警告", "设备未连接！");
        return;
    }

    // === 第二步：获取用户输入数据 ===
    QString canId = canIdEdit->text().trimmed();                 // 获取CAN ID
    QString data = sendDataEdit->text().trimmed();               // 获取发送数据

    // === 第三步：验证输入数据 ===
    if (canId.isEmpty() || data.isEmpty()) {
        QMessageBox::warning(this, "警告", "CAN ID和发送数据不能为空！");
        return;
    }

    // === 第四步：验证数据格式和长度 ===
    QStringList hexList = data.split(' ', Qt::SkipEmptyParts);  // 分割十六进制数据
    int maxLen = (configData.protocol == "CANFD") ? 64 : 8;      // 根据协议确定最大长度

    if (hexList.size() > maxLen) {
        QMessageBox::warning(this, "警告",
            QString("%1数据长度不能超过%2字节！").arg(configData.protocol).arg(maxLen));
        return;
    }

    // 模拟发送数据
    sendCount++;
    QString logText = QString("ID:0x%1 DLC:%2 Data:%3")
        .arg(canId.toUpper())
        .arg(hexList.size())
        .arg(data.toUpper());

    appendLog(logText, false);
}

void CANCommForm::onSendHeartbeat()
{
    if (!isConnected) {
        QMessageBox::warning(this, "警告", "设备未连接！");
        return;
    }

    if (!isHeartbeatRunning) {
        // 开始发送心跳包
        isHeartbeatRunning = true;
        heartbeatTimer->start();
        updateControlState();
        appendLog("开始发送心跳包", false);
    } else {
        // 停止发送心跳包
        onStopHeartbeat();
    }
}

void CANCommForm::onStopHeartbeat()
{
    isHeartbeatRunning = false;
    heartbeatTimer->stop();
    updateControlState();
    appendLog("停止发送心跳包", false);
}

void CANCommForm::onClearSendArea()
{
    sendTextEdit->clear();
    sendCount = 0;
}

void CANCommForm::onClearReceiveArea()
{
    receiveTextEdit->clear();
    receiveCount = 0;
}

void CANCommForm::onSaveData()
{
    QString fileName = QFileDialog::getSaveFileName(this,
        "保存通信数据",
        QString("CAN_Data_%1.txt").arg(QTime::currentTime().toString("hhmmss")),
        "文本文件 (*.txt)");

    if (!fileName.isEmpty()) {
        QFile file(fileName);
        if (file.open(QIODevice::WriteOnly | QIODevice::Text)) {
            QTextStream out(&file);
            out << "=== CAN/CANFD通信数据 ===" << Qt::endl;
            out << "协议: " << configData.protocol << Qt::endl;
            out << "保存时间: " << QTime::currentTime().toString("hh:mm:ss") << Qt::endl;
            out << Qt::endl;

            out << "=== 发送数据 ===" << Qt::endl;
            out << sendTextEdit->toPlainText() << Qt::endl;

            out << "=== 接收数据 ===" << Qt::endl;
            out << receiveTextEdit->toPlainText() << Qt::endl;

            file.close();
            QMessageBox::information(this, "提示", "数据保存成功！");
        } else {
            QMessageBox::warning(this, "错误", "文件保存失败！");
        }
    }
}

void CANCommForm::onBackClicked()
{
    if (isConnected) {
        QMessageBox msgBox(this);
        msgBox.setWindowTitle("确认");
        msgBox.setIcon(QMessageBox::Question);
        msgBox.setText("设备仍在连接中，是否断开连接并返回？");
        msgBox.setStandardButtons(QMessageBox::Yes | QMessageBox::No);
        msgBox.setDefaultButton(QMessageBox::No);

        // 设置现代化样式
        msgBox.setStyleSheet(
            "QMessageBox {"
            "    background: qlineargradient(x1:0, y1:0, x2:0, y2:1, "
            "                               stop:0 #2c3e50, stop:1 #34495e);"
            "    color: #ecf0f1;"
            "    border-radius: 10px;"
            "}"
            "QMessageBox QLabel {"
            "    color: #ecf0f1;"
            "    font-family: 'Microsoft YaHei UI';"
            "    font-size: 12px;"
            "    background: transparent;"
            "    padding: 10px;"
            "}"
            "QMessageBox QPushButton {"
            "    background: qlineargradient(x1:0, y1:0, x2:0, y2:1, "
            "                               stop:0 #3498db, stop:1 #2980b9);"
            "    color: white;"
            "    border: none;"
            "    border-radius: 8px;"
            "    padding: 8px 20px;"
            "    font-family: 'Microsoft YaHei UI';"
            "    font-size: 11px;"
            "    font-weight: bold;"
            "    min-width: 80px;"
            "}"
            "QMessageBox QPushButton:hover {"
            "    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,"
            "                               stop:0 #5dade2, stop:1 #3498db);"
            "}"
            "QMessageBox QPushButton:pressed {"
            "    background: qlineargradient(x1:0, y1:0, x2:0, y2:1, "
            "                               stop:0 #2980b9, stop:1 #1f618d);"
            "}"
        );

        int ret = msgBox.exec();
        if (ret == QMessageBox::Yes) {
            onDisconnectDevice();
            emit backToProtocolSelect();
        }
    } else {
        emit backToProtocolSelect();
    }
}

void CANCommForm::onHeartbeatTimer()
{
    if (!isConnected) {
        onStopHeartbeat();
        return;
    }

    // 发送心跳包数据
    QString canId = canIdEdit->text().trimmed();
    if (canId.isEmpty()) canId = "100";

    QString heartbeatData = "00 11 22 33 44 55 66 77";
    QString logText = QString("ID:0x%1 DLC:8 Data:%2 (心跳包)")
        .arg(canId.toUpper())
        .arg(heartbeatData);

    appendLog(logText, false);
}

void CANCommForm::onReceiveTimer()
{
    if (!isConnected) return;

    // 模拟接收数据（实际项目中这里会调用ZLGCAN接收函数）
    static int counter = 0;
    counter++;

    if (counter % 100 == 0) { // 每5秒模拟接收一次数据
        receiveCount++;
        QString logText = QString("ID:0x200 DLC:8 Data:AA BB CC DD EE FF 00 %1")
            .arg(counter % 256, 2, 16, QChar('0')).toUpper();
        appendLog(logText, true);
    }
}

void CANCommForm::onSendFrequencyChanged()
{
    int frequency = sendFrequencySpinBox->value();
    heartbeatTimer->setInterval(frequency);

    if (isHeartbeatRunning) {
        appendLog(QString("心跳包发送频率已调整为: %1ms").arg(frequency), false);
    }
}

void CANCommForm::appendLog(const QString &text, bool isReceived)
{
    QString timeStr = QTime::currentTime().toString("hh:mm:ss.zzz");
    QString prefix = isReceived ? "接收" : "发送";
    QString logText = QString("[%1] %2: %3").arg(timeStr).arg(prefix).arg(text);

    QTextEdit *textEdit = isReceived ? receiveTextEdit : sendTextEdit;
    textEdit->append(logText);

    // 自动滚动到底部
    QTextCursor cursor = textEdit->textCursor();
    cursor.movePosition(QTextCursor::End);
    textEdit->setTextCursor(cursor);
}

void CANCommForm::startCommunication()
{
    receiveTimer->start();

    // CANFD模式下自动启动心跳包
    if (configData.protocol == "CANFD") {
        QTimer::singleShot(1000, this, &CANCommForm::onSendHeartbeat);
    }
}

void CANCommForm::stopCommunication()
{
    receiveTimer->stop();
    if (isHeartbeatRunning) {
        onStopHeartbeat();
    }
}
