<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>ModbusForm</class>
 <widget class="QWidget" name="ModbusForm">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>1000</width>
    <height>700</height>
   </rect>
  </property>
  <property name="minimumSize">
   <size>
    <width>1000</width>
    <height>700</height>
   </size>
  </property>
  <property name="windowTitle">
   <string>Modbus通信设置</string>
  </property>
  <property name="styleSheet">
   <string>QWidget#ModbusForm {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                               stop:0 #ffffff, stop:1 #f8f9fa);
    color: #333333;
    font-family: 'Microsoft YaHei UI';
}

QGroupBox {
    background: rgba(255, 255, 255, 0.9);
    border: 2px solid #dc3545;
    border-radius: 15px;
    font-size: 16px;
    font-weight: bold;
    color: #dc3545;
    padding-top: 20px;
    margin-top: 15px;
}

QGroupBox::title {
    subcontrol-origin: margin;
    left: 20px;
    padding: 8px 20px;
    background: #dc3545;
    border-radius: 8px;
    color: white;
    font-size: 14px;
}

QLabel {
    color: #333333;
    font-size: 14px;
    font-weight: bold;
    padding: 5px;
}

QPushButton {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                               stop:0 #ffffff, stop:1 #f8f9fa);
    color: #dc3545;
    border: 2px solid #dc3545;
    border-radius: 10px;
    padding: 12px 20px;
    font-size: 14px;
    font-weight: bold;
    min-width: 100px;
}

QPushButton:hover {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                               stop:0 #dc3545, stop:1 #c82333);
    color: white;
}

QPushButton:pressed {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                               stop:0 #a71e2a, stop:1 #721c24);
    color: white;
}

QLineEdit, QSpinBox, QComboBox {
    background: #ffffff;
    border: 2px solid #6c757d;
    border-radius: 6px;
    padding: 8px 12px;
    color: #333333;
    font-size: 13px;
    font-weight: normal;
}

QLineEdit:focus, QSpinBox:focus, QComboBox:focus {
    border: 2px solid #dc3545;
    background: #ffffff;
}

QTextEdit {
    background: #ffffff;
    border: 2px solid #6c757d;
    border-radius: 8px;
    color: #333333;
    font-family: 'Consolas', 'Monaco', monospace;
    font-size: 12px;
    padding: 10px;
}

QCheckBox {
    color: #333333;
    font-size: 13px;
    font-weight: bold;
    spacing: 8px;
}

QCheckBox::indicator {
    width: 18px;
    height: 18px;
    border: 2px solid #6c757d;
    border-radius: 3px;
    background: #ffffff;
}

QCheckBox::indicator:checked {
    background: #dc3545;
    border: 2px solid #dc3545;
}</string>
  </property>
  <layout class="QGridLayout" name="gridLayout">
   <property name="spacing">
    <number>10</number>
   </property>
   <property name="leftMargin">
    <number>15</number>
   </property>
   <property name="topMargin">
    <number>15</number>
   </property>
   <property name="rightMargin">
    <number>15</number>
   </property>
   <property name="bottomMargin">
    <number>15</number>
   </property>
   <item row="0" column="0">
    <widget class="QGroupBox" name="serialSettingsGroup">
     <property name="title">
      <string>串口设置</string>
     </property>
     <layout class="QGridLayout" name="gridLayout_2">
      <item row="0" column="0">
       <widget class="QLabel" name="label">
        <property name="text">
         <string>串口：</string>
        </property>
       </widget>
      </item>
      <item row="0" column="1">
       <widget class="QComboBox" name="portComboBox"/>
      </item>
      <item row="0" column="2">
       <widget class="QPushButton" name="refreshPortButton">
        <property name="text">
         <string>刷新</string>
        </property>
       </widget>
      </item>
      <item row="1" column="0">
       <widget class="QLabel" name="label_2">
        <property name="text">
         <string>波特率：</string>
        </property>
       </widget>
      </item>
      <item row="1" column="1">
       <widget class="QComboBox" name="baudRateComboBox"/>
      </item>
      <item row="1" column="2">
       <widget class="QLabel" name="label_3">
        <property name="text">
         <string>数据位：</string>
        </property>
       </widget>
      </item>
      <item row="1" column="3">
       <widget class="QComboBox" name="dataBitsComboBox"/>
      </item>
      <item row="2" column="0">
       <widget class="QLabel" name="label_11">
        <property name="text">
         <string>停止位：</string>
        </property>
       </widget>
      </item>
      <item row="2" column="1">
       <widget class="QComboBox" name="stopBitsComboBox"/>
      </item>
      <item row="2" column="2">
       <widget class="QLabel" name="label_12">
        <property name="text">
         <string>校验位：</string>
        </property>
       </widget>
      </item>
      <item row="2" column="3">
       <widget class="QComboBox" name="parityComboBox"/>
      </item>
      <item row="3" column="1">
       <layout class="QHBoxLayout" name="horizontalLayout">
        <item>
         <widget class="QPushButton" name="connectButton">
          <property name="text">
           <string>连接</string>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QPushButton" name="disconnectButton">
          <property name="text">
           <string>断开</string>
          </property>
         </widget>
        </item>
       </layout>
      </item>
     </layout>
    </widget>
   </item>
   <item row="0" column="1">
    <widget class="QGroupBox" name="modbusSettingsGroup">
     <property name="title">
      <string>Modbus设置</string>
     </property>
     <layout class="QGridLayout" name="gridLayout_3">
      <item row="0" column="0">
       <widget class="QLabel" name="label_5">
        <property name="text">
         <string>功能码：</string>
        </property>
       </widget>
      </item>
      <item row="0" column="1">
       <widget class="QComboBox" name="functionCodeComboBox"/>
      </item>
      <item row="1" column="0">
       <widget class="QLabel" name="label_6">
        <property name="text">
         <string>从站地址：</string>
        </property>
       </widget>
      </item>
      <item row="1" column="1">
       <widget class="QSpinBox" name="slaveAddressSpinBox">
        <property name="maximum">
         <number>255</number>
        </property>
        <property name="value">
         <number>1</number>
        </property>
       </widget>
      </item>
      <item row="2" column="0">
       <widget class="QLabel" name="label_7">
        <property name="text">
         <string>起始地址：</string>
        </property>
       </widget>
      </item>
      <item row="2" column="1">
       <widget class="QSpinBox" name="startAddressSpinBox">
        <property name="maximum">
         <number>65535</number>
        </property>
       </widget>
      </item>
      <item row="3" column="0">
       <widget class="QLabel" name="label_13">
        <property name="text">
         <string>数量：</string>
        </property>
       </widget>
      </item>
      <item row="3" column="1">
       <widget class="QSpinBox" name="quantitySpinBox">
        <property name="minimum">
         <number>1</number>
        </property>
        <property name="maximum">
         <number>125</number>
        </property>
        <property name="value">
         <number>1</number>
        </property>
       </widget>
      </item>
     </layout>
    </widget>
   </item>
   <item row="0" column="2">
    <widget class="QGroupBox" name="autoSendGroup">
     <property name="title">
      <string>自动发送设置</string>
     </property>
     <layout class="QGridLayout" name="gridLayout_4">
      <item row="0" column="0">
       <widget class="QCheckBox" name="autoSendCheckBox">
        <property name="text">
         <string>自动发送</string>
        </property>
       </widget>
      </item>
      <item row="1" column="0">
       <widget class="QLabel" name="label_8">
        <property name="text">
         <string>发送间隔(ms)：</string>
        </property>
       </widget>
      </item>
      <item row="1" column="1">
       <widget class="QSpinBox" name="autoSendIntervalSpinBox">
        <property name="minimum">
         <number>100</number>
        </property>
        <property name="maximum">
         <number>10000</number>
        </property>
        <property name="value">
         <number>1000</number>
        </property>
       </widget>
      </item>
     </layout>
    </widget>
   </item>
   <item row="1" column="0" colspan="3">
    <widget class="QGroupBox" name="sendReceiveGroup">
     <property name="title">
      <string>数据收发</string>
     </property>
     <layout class="QGridLayout" name="gridLayout_5">
      <item row="0" column="0">
       <widget class="QLabel" name="label_9">
        <property name="text">
         <string>发送区：</string>
        </property>
       </widget>
      </item>
      <item row="1" column="0" colspan="2">
       <widget class="QTextEdit" name="sendTextEdit">
        <property name="maximumSize">
         <size>
          <width>16777215</width>
          <height>120</height>
         </size>
        </property>
       </widget>
      </item>
      <item row="2" column="0">
       <layout class="QHBoxLayout" name="horizontalLayout_2">
        <item>
         <widget class="QPushButton" name="sendButton">
          <property name="text">
           <string>发送</string>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QPushButton" name="clearSendButton">
          <property name="text">
           <string>清空发送</string>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QCheckBox" name="hexSendCheckBox">
          <property name="text">
           <string>十六进制发送</string>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QCheckBox" name="autoCRCCheckBox">
          <property name="text">
           <string>自动CRC校验</string>
          </property>
          <property name="checked">
           <bool>true</bool>
          </property>
         </widget>
        </item>
       </layout>
      </item>
      <item row="3" column="0">
       <widget class="QLabel" name="label_10">
        <property name="text">
         <string>接收区：</string>
        </property>
       </widget>
      </item>
      <item row="4" column="0" colspan="2">
       <widget class="QTextEdit" name="receiveTextEdit">
        <property name="readOnly">
         <bool>true</bool>
        </property>
       </widget>
      </item>
      <item row="5" column="0">
       <layout class="QHBoxLayout" name="horizontalLayout_3">
        <item>
         <widget class="QPushButton" name="clearReceiveButton">
          <property name="text">
           <string>清空接收</string>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QPushButton" name="saveLogButton">
          <property name="text">
           <string>保存日志</string>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QCheckBox" name="hexDisplayCheckBox">
          <property name="text">
           <string>十六进制显示</string>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QCheckBox" name="autoNewLineCheckBox">
          <property name="text">
           <string>自动换行</string>
          </property>
          <property name="checked">
           <bool>true</bool>
          </property>
         </widget>
        </item>
       </layout>
      </item>
     </layout>
    </widget>
   </item>
   <item row="2" column="0" colspan="3">
    <layout class="QHBoxLayout" name="horizontalLayout_4">
     <item>
      <widget class="QPushButton" name="backButton">
       <property name="minimumSize">
        <size>
         <width>120</width>
         <height>40</height>
        </size>
       </property>
       <property name="text">
        <string>返回</string>
       </property>
       <property name="icon">
        <iconset resource="resources.qrc">
         <normaloff>:/icon/exit.png</normaloff>:/icon/exit.png</iconset>
       </property>
      </widget>
     </item>
    </layout>
   </item>
  </layout>
 </widget>
 <resources>
  <include location="resources.qrc"/>
 </resources>
 <connections/>
</ui> 