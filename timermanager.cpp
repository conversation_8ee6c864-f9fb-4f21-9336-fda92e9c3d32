/**
 * @file timermanager.cpp
 * @brief 统一定时器管理器实现文件
 * @details 实现统一的定时器管理功能，优化多定时器的性能和资源使用。
 * <AUTHOR>
 * @date 2025-07-23
 * @version 1.0
 */

#include "timermanager.h"

// 静态成员初始化
TimerManager* TimerManager::m_instance = nullptr;

/**
 * @brief 获取单例实例
 * @return TimerManager单例指针
 */
TimerManager* TimerManager::instance()
{
    if (!m_instance) {
        m_instance = new TimerManager();
    }
    return m_instance;
}

/**
 * @brief 私有构造函数
 * @param parent 父对象指针
 */
TimerManager::TimerManager(QObject *parent)
    : QObject(parent)
    , m_masterTimer(new QTimer(this))
    , m_masterInterval(50)  // 默认50ms精度
    , m_activeTaskCount(0)
{
    // 连接主定时器信号
    connect(m_masterTimer, &QTimer::timeout, this, &TimerManager::onMasterTimeout);
    
    qDebug() << "TimerManager: 定时器管理器初始化完成，主定时器间隔:" << m_masterInterval << "ms";
}

/**
 * @brief 析构函数
 */
TimerManager::~TimerManager()
{
    // 停止主定时器
    if (m_masterTimer->isActive()) {
        m_masterTimer->stop();
    }
    
    // 清理所有任务
    m_tasks.clear();
    
    qDebug() << "TimerManager: 定时器管理器已销毁";
}

/**
 * @brief 添加定时任务
 * @param taskId 任务ID
 * @param interval 间隔时间（毫秒）
 * @param callback 回调函数
 * @return 是否添加成功
 */
bool TimerManager::addTask(const QString& taskId, int interval, std::function<void()> callback)
{
    if (taskId.isEmpty() || interval <= 0 || !callback) {
        qDebug() << "TimerManager: 添加任务失败，参数无效:" << taskId;
        return false;
    }
    
    if (m_tasks.contains(taskId)) {
        qDebug() << "TimerManager: 任务已存在:" << taskId;
        return false;
    }
    
    TimerTask task(interval, callback);
    m_tasks[taskId] = task;
    
    qDebug() << "TimerManager: 添加任务成功:" << taskId << "间隔:" << interval << "ms";
    return true;
}

/**
 * @brief 移除定时任务
 * @param taskId 任务ID
 * @return 是否移除成功
 */
bool TimerManager::removeTask(const QString& taskId)
{
    if (!m_tasks.contains(taskId)) {
        qDebug() << "TimerManager: 任务不存在:" << taskId;
        return false;
    }
    
    // 如果任务正在运行，先停止它
    if (m_tasks[taskId].enabled) {
        stopTask(taskId);
    }
    
    m_tasks.remove(taskId);
    qDebug() << "TimerManager: 移除任务成功:" << taskId;
    return true;
}

/**
 * @brief 启动定时任务
 * @param taskId 任务ID
 * @return 是否启动成功
 */
bool TimerManager::startTask(const QString& taskId)
{
    if (!m_tasks.contains(taskId)) {
        qDebug() << "TimerManager: 启动失败，任务不存在:" << taskId;
        return false;
    }
    
    TimerTask& task = m_tasks[taskId];
    if (task.enabled) {
        qDebug() << "TimerManager: 任务已在运行:" << taskId;
        return true;
    }
    
    task.enabled = true;
    task.counter = 0;  // 重置计数器
    m_activeTaskCount++;
    
    // 如果这是第一个活跃任务，启动主定时器
    if (m_activeTaskCount == 1) {
        startMasterTimer();
    }
    
    qDebug() << "TimerManager: 启动任务成功:" << taskId << "活跃任务数:" << m_activeTaskCount;
    return true;
}

/**
 * @brief 停止定时任务
 * @param taskId 任务ID
 * @return 是否停止成功
 */
bool TimerManager::stopTask(const QString& taskId)
{
    if (!m_tasks.contains(taskId)) {
        qDebug() << "TimerManager: 停止失败，任务不存在:" << taskId;
        return false;
    }
    
    TimerTask& task = m_tasks[taskId];
    if (!task.enabled) {
        qDebug() << "TimerManager: 任务已停止:" << taskId;
        return true;
    }
    
    task.enabled = false;
    task.counter = 0;  // 重置计数器
    m_activeTaskCount--;
    
    // 如果没有活跃任务，停止主定时器
    if (m_activeTaskCount <= 0) {
        m_activeTaskCount = 0;
        stopMasterTimer();
    }
    
    qDebug() << "TimerManager: 停止任务成功:" << taskId << "活跃任务数:" << m_activeTaskCount;
    return true;
}

/**
 * @brief 更新任务间隔
 * @param taskId 任务ID
 * @param newInterval 新的间隔时间（毫秒）
 * @return 是否更新成功
 */
bool TimerManager::updateTaskInterval(const QString& taskId, int newInterval)
{
    if (!m_tasks.contains(taskId) || newInterval <= 0) {
        qDebug() << "TimerManager: 更新间隔失败:" << taskId;
        return false;
    }
    
    TimerTask& task = m_tasks[taskId];
    task.interval = newInterval;
    task.counter = 0;  // 重置计数器
    
    qDebug() << "TimerManager: 更新任务间隔成功:" << taskId << "新间隔:" << newInterval << "ms";
    return true;
}

/**
 * @brief 检查任务是否存在
 * @param taskId 任务ID
 * @return 是否存在
 */
bool TimerManager::hasTask(const QString& taskId) const
{
    return m_tasks.contains(taskId);
}

/**
 * @brief 检查任务是否正在运行
 * @param taskId 任务ID
 * @return 是否正在运行
 */
bool TimerManager::isTaskRunning(const QString& taskId) const
{
    if (!m_tasks.contains(taskId)) {
        return false;
    }
    return m_tasks[taskId].enabled;
}

/**
 * @brief 获取活跃任务数量
 * @return 活跃任务数量
 */
int TimerManager::getActiveTaskCount() const
{
    return m_activeTaskCount;
}

/**
 * @brief 设置主定时器间隔
 * @param interval 间隔时间（毫秒）
 */
void TimerManager::setMasterInterval(int interval)
{
    if (interval <= 0) {
        qDebug() << "TimerManager: 无效的主定时器间隔:" << interval;
        return;
    }
    
    bool wasActive = m_masterTimer->isActive();
    if (wasActive) {
        m_masterTimer->stop();
    }
    
    m_masterInterval = interval;
    m_masterTimer->setInterval(m_masterInterval);
    
    if (wasActive) {
        m_masterTimer->start();
    }
    
    qDebug() << "TimerManager: 主定时器间隔已更新为:" << m_masterInterval << "ms";
}

/**
 * @brief 主定时器超时处理
 * @details 处理所有定时任务的分发
 */
void TimerManager::onMasterTimeout()
{
    // 遍历所有任务，检查是否需要执行
    for (auto it = m_tasks.begin(); it != m_tasks.end(); ++it) {
        TimerTask& task = it.value();
        
        if (!task.enabled || !task.callback) {
            continue;
        }
        
        // 增加计数器
        task.counter += m_masterInterval;
        
        // 检查是否到达执行时间
        if (task.counter >= task.interval) {
            task.counter = 0;  // 重置计数器
            
            try {
                task.callback();  // 执行回调函数
            } catch (...) {
                qDebug() << "TimerManager: 任务执行异常:" << it.key();
            }
        }
    }
}

/**
 * @brief 启动主定时器
 */
void TimerManager::startMasterTimer()
{
    if (!m_masterTimer->isActive()) {
        m_masterTimer->start(m_masterInterval);
        qDebug() << "TimerManager: 主定时器已启动，间隔:" << m_masterInterval << "ms";
    }
}

/**
 * @brief 停止主定时器
 */
void TimerManager::stopMasterTimer()
{
    if (m_masterTimer->isActive()) {
        m_masterTimer->stop();
        qDebug() << "TimerManager: 主定时器已停止";
    }
}
