/**
 * @file timermanager.h
 * @brief 统一定时器管理器头文件
 * @details 提供统一的定时器管理功能，优化多定时器的性能和资源使用。
 *          通过单一主定时器分发事件，减少系统定时器数量，提高性能。
 * <AUTHOR>
 * @date 2025-07-23
 * @version 1.0
 */

#ifndef TIMERMANAGER_H
#define TIMERMANAGER_H

#include <QObject>
#include <QTimer>
#include <QMap>
#include <QDebug>
#include <functional>

/**
 * @struct TimerTask
 * @brief 定时器任务结构
 * @details 定义单个定时器任务的属性和回调函数
 */
struct TimerTask {
    int interval;                           ///< 任务间隔（毫秒）
    int counter;                            ///< 当前计数器
    bool enabled;                           ///< 是否启用
    std::function<void()> callback;         ///< 回调函数
    
    TimerTask() : interval(1000), counter(0), enabled(false) {}
    TimerTask(int ms, std::function<void()> cb) 
        : interval(ms), counter(0), enabled(false), callback(cb) {}
};

/**
 * @class TimerManager
 * @brief 统一定时器管理器类
 * @details 使用单一主定时器管理多个定时任务，提高性能和资源利用率。
 *          支持动态添加、移除、启动、停止定时任务。
 */
class TimerManager : public QObject
{
    Q_OBJECT

public:
    /**
     * @brief 获取单例实例
     * @return TimerManager单例指针
     */
    static TimerManager* instance();

    /**
     * @brief 析构函数
     */
    ~TimerManager();

    /**
     * @brief 添加定时任务
     * @param taskId 任务ID
     * @param interval 间隔时间（毫秒）
     * @param callback 回调函数
     * @return 是否添加成功
     */
    bool addTask(const QString& taskId, int interval, std::function<void()> callback);

    /**
     * @brief 移除定时任务
     * @param taskId 任务ID
     * @return 是否移除成功
     */
    bool removeTask(const QString& taskId);

    /**
     * @brief 启动定时任务
     * @param taskId 任务ID
     * @return 是否启动成功
     */
    bool startTask(const QString& taskId);

    /**
     * @brief 停止定时任务
     * @param taskId 任务ID
     * @return 是否停止成功
     */
    bool stopTask(const QString& taskId);

    /**
     * @brief 更新任务间隔
     * @param taskId 任务ID
     * @param newInterval 新的间隔时间（毫秒）
     * @return 是否更新成功
     */
    bool updateTaskInterval(const QString& taskId, int newInterval);

    /**
     * @brief 检查任务是否存在
     * @param taskId 任务ID
     * @return 是否存在
     */
    bool hasTask(const QString& taskId) const;

    /**
     * @brief 检查任务是否正在运行
     * @param taskId 任务ID
     * @return 是否正在运行
     */
    bool isTaskRunning(const QString& taskId) const;

    /**
     * @brief 获取活跃任务数量
     * @return 活跃任务数量
     */
    int getActiveTaskCount() const;

    /**
     * @brief 设置主定时器间隔
     * @param interval 间隔时间（毫秒）
     * @details 默认为50ms，可根据需要调整精度
     */
    void setMasterInterval(int interval);

private slots:
    /**
     * @brief 主定时器超时处理
     * @details 处理所有定时任务的分发
     */
    void onMasterTimeout();

private:
    /**
     * @brief 私有构造函数（单例模式）
     */
    explicit TimerManager(QObject *parent = nullptr);

    /**
     * @brief 启动主定时器
     */
    void startMasterTimer();

    /**
     * @brief 停止主定时器
     */
    void stopMasterTimer();

    static TimerManager* m_instance;        ///< 单例实例
    QTimer* m_masterTimer;                  ///< 主定时器
    QMap<QString, TimerTask> m_tasks;       ///< 任务映射表
    int m_masterInterval;                   ///< 主定时器间隔
    int m_activeTaskCount;                  ///< 活跃任务计数
};

#endif // TIMERMANAGER_H
