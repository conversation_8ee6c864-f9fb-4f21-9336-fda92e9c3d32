/**
 * @file deviceconnectiondialog.cpp
 * @brief 设备连接检测对话框实现文件
 * @details 在进入功能界面前检测CANFD设备连接状态，提供用户选择
 * <AUTHOR>
 * @date 2025-01-31
 * @version 1.0
 */

#include "deviceconnectiondialog.h"
#include <QApplication>
#include <QMessageBox>
#include <QPixmap>
#include <QIcon>

/**
 * @brief 构造函数
 */
DeviceConnectionDialog::DeviceConnectionDialog(const QString& functionName, QWidget *parent)
    : QDialog(parent)
    , m_functionName(functionName)
    , m_userChoice(UserChoice::CANCEL)
    , m_dataSourceType(DataSourceType::SIMULATION)
    , m_detectedStatus(CANFDConnectionStatus::DISCONNECTED)
    , m_mainLayout(nullptr)
    , m_titleLabel(nullptr)
    , m_statusLabel(nullptr)
    , m_iconLabel(nullptr)
    , m_progressBar(nullptr)
    , m_loadingMovie(nullptr)
    , m_connectDeviceBtn(nullptr)
    , m_useSimulationBtn(nullptr)
    , m_useRealDataBtn(nullptr)
    , m_cancelBtn(nullptr)
    , m_buttonLayout(nullptr)
    , m_detectionTimer(nullptr)
    , m_deviceManager(nullptr)
{
    // 获取设备管理器实例
    m_deviceManager = CANFDDeviceManager::getInstance();
    
    // 初始化UI
    initializeUI();
    setupStyles();
    
    // 连接信号槽
    connect(m_deviceManager, &CANFDDeviceManager::connectionStatusChanged,
            this, &DeviceConnectionDialog::onConnectionStatusChanged);
    connect(m_deviceManager, &CANFDDeviceManager::deviceError,
            this, &DeviceConnectionDialog::onDeviceError);
    
    // 创建检测定时器
    m_detectionTimer = new QTimer(this);
    m_detectionTimer->setSingleShot(true);
    m_detectionTimer->setInterval(3000); // 3秒检测超时
    connect(m_detectionTimer, &QTimer::timeout, this, &DeviceConnectionDialog::onDetectionCompleted);
}

/**
 * @brief 析构函数
 */
DeviceConnectionDialog::~DeviceConnectionDialog()
{
    if (m_loadingMovie) {
        m_loadingMovie->stop();
        delete m_loadingMovie;
    }
}

/**
 * @brief 初始化UI界面
 */
void DeviceConnectionDialog::initializeUI()
{
    setWindowTitle(QString("设备连接检测 - %1").arg(m_functionName));
    setFixedSize(400, 300);
    setModal(true);
    
    // 创建主布局
    m_mainLayout = new QVBoxLayout(this);
    m_mainLayout->setSpacing(20);
    m_mainLayout->setContentsMargins(30, 30, 30, 30);
    
    // 标题标签
    m_titleLabel = new QLabel(QString("正在检测CANFD设备连接状态..."), this);
    m_titleLabel->setAlignment(Qt::AlignCenter);
    m_titleLabel->setWordWrap(true);
    m_mainLayout->addWidget(m_titleLabel);
    
    // 图标标签
    m_iconLabel = new QLabel(this);
    m_iconLabel->setAlignment(Qt::AlignCenter);
    m_iconLabel->setFixedSize(64, 64);
    m_mainLayout->addWidget(m_iconLabel);
    
    // 状态标签
    m_statusLabel = new QLabel("请稍候...", this);
    m_statusLabel->setAlignment(Qt::AlignCenter);
    m_statusLabel->setWordWrap(true);
    m_mainLayout->addWidget(m_statusLabel);
    
    // 进度条
    m_progressBar = new QProgressBar(this);
    m_progressBar->setRange(0, 0); // 无限进度条
    m_progressBar->setVisible(false);
    m_mainLayout->addWidget(m_progressBar);
    
    // 添加弹性空间
    m_mainLayout->addStretch();
    
    // 按钮布局
    m_buttonLayout = new QHBoxLayout();
    m_buttonLayout->setSpacing(10);
    
    // 创建按钮
    m_connectDeviceBtn = new QPushButton("连接设备", this);
    m_useSimulationBtn = new QPushButton("使用模拟数据", this);
    m_useRealDataBtn = new QPushButton("使用真实数据", this);
    m_cancelBtn = new QPushButton("取消", this);
    
    // 连接按钮信号槽
    connect(m_connectDeviceBtn, &QPushButton::clicked, this, &DeviceConnectionDialog::onConnectDeviceClicked);
    connect(m_useSimulationBtn, &QPushButton::clicked, this, &DeviceConnectionDialog::onUseSimulationClicked);
    connect(m_useRealDataBtn, &QPushButton::clicked, this, &DeviceConnectionDialog::onUseRealDataClicked);
    connect(m_cancelBtn, &QPushButton::clicked, this, &DeviceConnectionDialog::onCancelClicked);
    
    // 初始时隐藏所有按钮
    m_connectDeviceBtn->setVisible(false);
    m_useSimulationBtn->setVisible(false);
    m_useRealDataBtn->setVisible(false);
    m_cancelBtn->setVisible(false);
    
    m_buttonLayout->addWidget(m_connectDeviceBtn);
    m_buttonLayout->addWidget(m_useSimulationBtn);
    m_buttonLayout->addWidget(m_useRealDataBtn);
    m_buttonLayout->addWidget(m_cancelBtn);
    
    m_mainLayout->addLayout(m_buttonLayout);
}

/**
 * @brief 设置对话框样式
 */
void DeviceConnectionDialog::setupStyles()
{
    // 设置整体样式
    setStyleSheet(R"(
        QDialog {
            background-color: #f5f5f5;
            border: 2px solid #d0d0d0;
            border-radius: 10px;
        }
        
        QLabel {
            color: #333333;
            font-size: 14px;
        }
        
        QPushButton {
            background-color: #4CAF50;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 5px;
            font-size: 12px;
            min-width: 80px;
        }
        
        QPushButton:hover {
            background-color: #45a049;
        }
        
        QPushButton:pressed {
            background-color: #3d8b40;
        }
        
        QPushButton:disabled {
            background-color: #cccccc;
            color: #666666;
        }
        
        QProgressBar {
            border: 2px solid #d0d0d0;
            border-radius: 5px;
            text-align: center;
        }
        
        QProgressBar::chunk {
            background-color: #4CAF50;
            border-radius: 3px;
        }
    )");
    
    // 设置标题样式
    m_titleLabel->setStyleSheet("QLabel { font-size: 16px; font-weight: bold; color: #2c3e50; }");
    
    // 设置取消按钮样式
    m_cancelBtn->setStyleSheet(R"(
        QPushButton {
            background-color: #f44336;
        }
        QPushButton:hover {
            background-color: #da190b;
        }
        QPushButton:pressed {
            background-color: #c62828;
        }
    )");
}

/**
 * @brief 显示对话框并开始检测
 */
int DeviceConnectionDialog::exec()
{
    // 显示检测中界面
    showDetectingUI();
    
    // 开始检测
    startDetection();
    
    // 调用父类的exec
    return QDialog::exec();
}

/**
 * @brief 开始设备检测
 */
void DeviceConnectionDialog::startDetection()
{
    qDebug() << QString("开始检测%1的设备连接状态").arg(m_functionName);
    
    // 启动检测定时器
    m_detectionTimer->start();
    
    // 异步检测设备状态
    QTimer::singleShot(100, this, [this]() {
        m_detectedStatus = m_deviceManager->detectDeviceStatus();
        onDetectionCompleted();
    });
}

/**
 * @brief 设备状态检测完成槽函数
 */
void DeviceConnectionDialog::onDetectionCompleted()
{
    m_detectionTimer->stop();
    
    qDebug() << QString("设备检测完成，状态: %1").arg(static_cast<int>(m_detectedStatus));
    
    // 根据检测结果显示相应界面
    showDetectionResult(m_detectedStatus);
}

/**
 * @brief 显示检测结果界面
 */
void DeviceConnectionDialog::showDetectionResult(CANFDConnectionStatus status)
{
    switch (status) {
    case CANFDConnectionStatus::ACTIVE:
        showConnectedUI();
        break;
    case CANFDConnectionStatus::CONNECTED:
        showConnectedUI(); // 已连接但无数据，仍显示连接界面
        break;
    case CANFDConnectionStatus::DISCONNECTED:
        showDisconnectedUI();
        break;
    case CANFDConnectionStatus::CONNECTING:
        showConnectingUI();
        break;
    }
}

/**
 * @brief 显示检测中界面
 */
void DeviceConnectionDialog::showDetectingUI()
{
    m_titleLabel->setText(QString("正在检测CANFD设备连接状态..."));
    m_statusLabel->setText("请稍候，正在扫描设备...");
    
    // 显示进度条
    m_progressBar->setVisible(true);
    
    // 设置检测图标
    m_iconLabel->setText("🔍");
    m_iconLabel->setStyleSheet("QLabel { font-size: 48px; }");
    
    // 隐藏所有按钮
    m_connectDeviceBtn->setVisible(false);
    m_useSimulationBtn->setVisible(false);
    m_useRealDataBtn->setVisible(false);
    m_cancelBtn->setVisible(true);
}

/**
 * @brief 显示已连接界面
 */
void DeviceConnectionDialog::showConnectedUI()
{
    bool hasData = (m_detectedStatus == CANFDConnectionStatus::ACTIVE);
    
    m_titleLabel->setText("✅ CANFD设备连接成功");
    
    if (hasData) {
        m_statusLabel->setText(QString("检测到真实数据传输，%1可以使用真实数据运行。").arg(m_functionName));
        m_dataSourceType = DataSourceType::REAL_DATA;
    } else {
        m_statusLabel->setText(QString("设备已连接但暂无数据传输，%1可以使用真实数据或模拟数据运行。").arg(m_functionName));
        m_dataSourceType = DataSourceType::REAL_DATA; // 默认选择真实数据
    }
    
    // 隐藏进度条
    m_progressBar->setVisible(false);
    
    // 设置成功图标
    m_iconLabel->setText("✅");
    m_iconLabel->setStyleSheet("QLabel { font-size: 48px; color: #4CAF50; }");
    
    // 显示相应按钮
    m_connectDeviceBtn->setVisible(false);
    m_useRealDataBtn->setVisible(true);
    m_useSimulationBtn->setVisible(true);
    m_cancelBtn->setVisible(true);
    
    // 设置默认按钮
    m_useRealDataBtn->setDefault(true);
    m_useRealDataBtn->setFocus();
}

/**
 * @brief 显示未连接界面
 */
void DeviceConnectionDialog::showDisconnectedUI()
{
    m_titleLabel->setText("❌ 未检测到CANFD设备");
    m_statusLabel->setText(QString("未找到CANFD设备连接。\n您可以连接设备后使用真实数据，或直接使用模拟数据运行%1。").arg(m_functionName));

    // 隐藏进度条
    m_progressBar->setVisible(false);

    // 设置错误图标
    m_iconLabel->setText("❌");
    m_iconLabel->setStyleSheet("QLabel { font-size: 48px; color: #f44336; }");

    // 显示相应按钮
    m_connectDeviceBtn->setVisible(true);
    m_useSimulationBtn->setVisible(true);
    m_useRealDataBtn->setVisible(false);
    m_cancelBtn->setVisible(true);

    // 设置默认按钮
    m_useSimulationBtn->setDefault(true);
    m_useSimulationBtn->setFocus();

    m_dataSourceType = DataSourceType::SIMULATION;
}

/**
 * @brief 显示连接中界面
 */
void DeviceConnectionDialog::showConnectingUI()
{
    m_titleLabel->setText("🔄 正在连接CANFD设备...");
    m_statusLabel->setText("设备连接中，请稍候...");

    // 显示进度条
    m_progressBar->setVisible(true);

    // 设置连接图标
    m_iconLabel->setText("🔄");
    m_iconLabel->setStyleSheet("QLabel { font-size: 48px; color: #2196F3; }");

    // 只显示取消按钮
    m_connectDeviceBtn->setVisible(false);
    m_useSimulationBtn->setVisible(false);
    m_useRealDataBtn->setVisible(false);
    m_cancelBtn->setVisible(true);
}

/**
 * @brief 连接设备按钮点击槽函数
 */
void DeviceConnectionDialog::onConnectDeviceClicked()
{
    qDebug() << "用户点击连接设备按钮";

    // 显示连接中界面
    showConnectingUI();

    // 异步尝试连接设备
    QTimer::singleShot(100, this, [this]() {
        bool connected = m_deviceManager->connectDevice();
        if (connected) {
            m_detectedStatus = m_deviceManager->getCurrentStatus();
            showDetectionResult(m_detectedStatus);
        } else {
            // 连接失败，显示错误信息
            QMessageBox::warning(this, "连接失败",
                "无法连接到CANFD设备。\n请检查：\n"
                "1. 设备是否正确连接到USB端口\n"
                "2. 设备驱动是否正确安装\n"
                "3. 设备是否被其他程序占用");
            showDisconnectedUI();
        }
    });
}

/**
 * @brief 使用模拟数据按钮点击槽函数
 */
void DeviceConnectionDialog::onUseSimulationClicked()
{
    qDebug() << "用户选择使用模拟数据";
    m_userChoice = UserChoice::USE_SIMULATION;
    m_dataSourceType = DataSourceType::SIMULATION;
    accept();
}

/**
 * @brief 使用真实数据按钮点击槽函数
 */
void DeviceConnectionDialog::onUseRealDataClicked()
{
    qDebug() << "用户选择使用真实数据";
    m_userChoice = UserChoice::USE_REAL_DATA;
    m_dataSourceType = DataSourceType::REAL_DATA;
    accept();
}

/**
 * @brief 取消按钮点击槽函数
 */
void DeviceConnectionDialog::onCancelClicked()
{
    qDebug() << "用户取消操作";
    m_userChoice = UserChoice::CANCEL;
    reject();
}

/**
 * @brief 连接状态改变槽函数
 */
void DeviceConnectionDialog::onConnectionStatusChanged(CANFDConnectionStatus status)
{
    qDebug() << "设备连接状态改变:" << static_cast<int>(status);
    m_detectedStatus = status;

    // 如果对话框正在显示，更新界面
    if (isVisible()) {
        showDetectionResult(status);
    }
}

/**
 * @brief 设备错误槽函数
 */
void DeviceConnectionDialog::onDeviceError(const QString& errorMessage)
{
    qDebug() << "设备错误:" << errorMessage;

    // 如果对话框正在显示，显示错误信息
    if (isVisible()) {
        m_statusLabel->setText(QString("设备错误: %1").arg(errorMessage));
        showDisconnectedUI();
    }
}
