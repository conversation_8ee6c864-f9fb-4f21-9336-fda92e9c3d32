<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>MultiMotorInterface</class>
 <widget class="QWidget" name="MultiMotorInterface">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>1200</width>
    <height>800</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>六电机控制系统</string>
  </property>
  <property name="styleSheet">
   <string>/* 主界面样式 - 红白配色风格 */
QWidget {
    background-color: #FFFFFF;
    font-family: "Microsoft YaHei", "SimHei", sans-serif;
    font-size: 13px;
}

/* 标题样式 */
QLabel#titleLabel {
    font-size: 18px;
    font-weight: bold;
    color: #CC0000;
    background-color: #FFFFFF;
    border: 2px solid #CC0000;
    border-radius: 8px;
    padding: 8px;
    margin: 5px;
}

/* 状态标签样式 */
QLabel#statusLabel {
    font-size: 12px;
    color: #333333;
    background-color: #F8F8F8;
    border: 1px solid #DDDDDD;
    border-radius: 4px;
    padding: 5px;
    margin: 3px;
}

/* 按钮基础样式 */
QPushButton {
    background-color: #FFFFFF;
    border: 2px solid #CC0000;
    border-radius: 6px;
    color: #CC0000;
    font-weight: bold;
    padding: 6px 12px;
    margin: 2px;
    min-height: 24px;
}

QPushButton:hover {
    background-color: #FFE6E6;
    border-color: #AA0000;
}

QPushButton:pressed {
    background-color: #FFCCCC;
    border-color: #880000;
}

QPushButton:disabled {
    background-color: #F0F0F0;
    border-color: #CCCCCC;
    color: #999999;
}

/* 主要操作按钮样式 */
QPushButton#primaryButton {
    background-color: #CC0000;
    color: #FFFFFF;
    border: 2px solid #CC0000;
}

QPushButton#primaryButton:hover {
    background-color: #AA0000;
    border-color: #AA0000;
}

QPushButton#primaryButton:pressed {
    background-color: #880000;
    border-color: #880000;
}

/* 危险操作按钮样式 */
QPushButton#dangerButton {
    background-color: #FF4444;
    color: #FFFFFF;
    border: 2px solid #FF4444;
    font-weight: bold;
}

QPushButton#dangerButton:hover {
    background-color: #FF2222;
    border-color: #FF2222;
}

/* 分组框样式 */
QGroupBox {
    font-weight: bold;
    border: 2px solid #CC0000;
    border-radius: 8px;
    margin-top: 10px;
    padding-top: 10px;
    background-color: #FFFFFF;
}

QGroupBox::title {
    subcontrol-origin: margin;
    left: 10px;
    padding: 0 8px 0 8px;
    color: #CC0000;
    font-size: 13px;
    font-weight: bold;
    background-color: #FFFFFF;
}

/* 输入控件样式 */
QSpinBox, QDoubleSpinBox, QComboBox {
    border: 1px solid #CCCCCC;
    border-radius: 4px;
    padding: 4px;
    background-color: #FFFFFF;
    selection-background-color: #FFE6E6;
}

QSpinBox:focus, QDoubleSpinBox:focus, QComboBox:focus {
    border-color: #CC0000;
}

/* 进度条样式 */
QProgressBar {
    border: 1px solid #CCCCCC;
    border-radius: 4px;
    text-align: center;
    background-color: #F0F0F0;
}

QProgressBar::chunk {
    background-color: #CC0000;
    border-radius: 3px;
}

/* 表格样式 */
QTableWidget {
    gridline-color: #DDDDDD;
    background-color: #FFFFFF;
    alternate-background-color: #F8F8F8;
    selection-background-color: #FFE6E6;
}

QHeaderView::section {
    background-color: #CC0000;
    color: #FFFFFF;
    padding: 6px;
    border: none;
    font-weight: bold;
}</string>
  </property>
  <layout class="QVBoxLayout" name="mainLayout">
   <property name="spacing">
    <number>10</number>
   </property>
   <property name="leftMargin">
    <number>15</number>
   </property>
   <property name="topMargin">
    <number>15</number>
   </property>
   <property name="rightMargin">
    <number>15</number>
   </property>
   <property name="bottomMargin">
    <number>15</number>
   </property>
   <item>
    <!-- 标题栏 -->
    <layout class="QHBoxLayout" name="titleLayout">
     <item>
      <widget class="QLabel" name="titleLabel">
       <property name="text">
        <string>六电机控制系统 - CANFD协议</string>
       </property>
       <property name="alignment">
        <set>Qt::AlignCenter</set>
       </property>
      </widget>
     </item>
     <item>
      <spacer name="titleSpacer">
       <property name="orientation">
        <enum>Qt::Horizontal</enum>
       </property>
       <property name="sizeHint" stdset="0">
        <size>
         <width>40</width>
         <height>20</height>
        </size>
       </property>
      </spacer>
     </item>
     <item>
      <widget class="QPushButton" name="backButton">
       <property name="text">
        <string>返回主界面</string>
       </property>
       <property name="objectName">
        <string>primaryButton</string>
       </property>
       <property name="minimumSize">
        <size>
         <width>100</width>
         <height>35</height>
        </size>
       </property>
      </widget>
     </item>
    </layout>
   </item>
   
   <item>
    <!-- 系统状态栏 -->
    <layout class="QHBoxLayout" name="statusLayout">
     <item>
      <widget class="QLabel" name="connectionStatusLabel">
       <property name="text">
        <string>连接状态: 未连接</string>
       </property>
       <property name="objectName">
        <string>statusLabel</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QLabel" name="systemStatusLabel">
       <property name="text">
        <string>系统状态: 待机</string>
       </property>
       <property name="objectName">
        <string>statusLabel</string>
       </property>
      </widget>
     </item>
     <item>
      <spacer name="statusSpacer">
       <property name="orientation">
        <enum>Qt::Horizontal</enum>
       </property>
       <property name="sizeHint" stdset="0">
        <size>
         <width>40</width>
         <height>20</height>
        </size>
       </property>
      </spacer>
     </item>
     <item>
      <widget class="QLabel" name="lastUpdateLabel">
       <property name="text">
        <string>最后更新: --</string>
       </property>
       <property name="objectName">
        <string>statusLabel</string>
       </property>
      </widget>
     </item>
    </layout>
   </item>

   <item>
    <!-- 批量控制按钮栏 -->
    <widget class="QGroupBox" name="batchControlGroup">
     <property name="title">
      <string>批量控制</string>
     </property>
     <layout class="QHBoxLayout" name="batchControlLayout">
      <item>
       <widget class="QPushButton" name="startAllButton">
        <property name="text">
         <string>全部启动</string>
        </property>
        <property name="objectName">
         <string>primaryButton</string>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QPushButton" name="stopAllButton">
        <property name="text">
         <string>全部停止</string>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QPushButton" name="enableAllButton">
        <property name="text">
         <string>全部使能</string>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QPushButton" name="disableAllButton">
        <property name="text">
         <string>全部失能</string>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QPushButton" name="homeAllButton">
        <property name="text">
         <string>全部回零</string>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QPushButton" name="clearAllErrorsButton">
        <property name="text">
         <string>清除所有错误</string>
        </property>
       </widget>
      </item>
      <item>
       <spacer name="batchControlSpacer">
        <property name="orientation">
         <enum>Qt::Horizontal</enum>
        </property>
        <property name="sizeHint" stdset="0">
         <size>
          <width>40</width>
          <height>20</height>
         </size>
        </property>
       </spacer>
      </item>
      <item>
       <widget class="QPushButton" name="emergencyStopButton">
        <property name="text">
         <string>紧急停止</string>
        </property>
        <property name="objectName">
         <string>dangerButton</string>
        </property>
        <property name="minimumSize">
         <size>
          <width>80</width>
          <height>35</height>
         </size>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QPushButton" name="oscilloscopeButton">
        <property name="text">
         <string>打开示波器</string>
        </property>
        <property name="objectName">
         <string>primaryButton</string>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QPushButton" name="refreshButton">
        <property name="text">
         <string>刷新状态</string>
        </property>
       </widget>
      </item>
     </layout>
    </widget>
   </item>

   <item>
    <!-- 电机控制区域 -->
    <widget class="QScrollArea" name="motorScrollArea">
     <property name="widgetResizable">
      <bool>true</bool>
     </property>
     <property name="horizontalScrollBarPolicy">
      <enum>Qt::ScrollBarAsNeeded</enum>
     </property>
     <property name="verticalScrollBarPolicy">
      <enum>Qt::ScrollBarAsNeeded</enum>
     </property>
     <widget class="QWidget" name="motorScrollAreaWidget">
      <property name="geometry">
       <rect>
        <x>0</x>
        <y>0</y>
        <width>1150</width>
        <height>700</height>
       </rect>
      </property>
      <layout class="QGridLayout" name="motorGridLayout">
       <property name="spacing">
        <number>20</number>
       </property>
       <property name="leftMargin">
        <number>15</number>
       </property>
       <property name="topMargin">
        <number>15</number>
       </property>
       <property name="rightMargin">
        <number>15</number>
       </property>
       <property name="bottomMargin">
        <number>15</number>
       </property>
       <!-- 电机控制组件将在代码中动态添加 -->
      </layout>
     </widget>
    </widget>
   </item>
  </layout>
 </widget>
 <resources/>
 <connections/>
</ui>
