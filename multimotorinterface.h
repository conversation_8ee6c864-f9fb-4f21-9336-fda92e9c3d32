/**
 * @file multimotorinterface.h
 * @brief 多电机控制界面类头文件
 * @details 定义了多电机控制界面类MultiMotorInterface，提供6个电机的统一控制界面，
 *          包括单独控制、批量控制、状态监控、参数设置等功能。
 *          界面采用红白配色风格，与主界面保持一致。
 * <AUTHOR>
 * @date 2025-01-28
 * @version 1.0
 */

#ifndef MULTIMOTORINTERFACE_H
#define MULTIMOTORINTERFACE_H

#include <QWidget>
#include <QTimer>
#include <QLabel>
#include <QPushButton>
#include <QSpinBox>
#include <QDoubleSpinBox>
#include <QComboBox>
#include <QCheckBox>
#include <QProgressBar>
#include <QGroupBox>
#include <QGridLayout>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QTableWidget>
#include <QHeaderView>
#include <QDateTime>
#include "motorprotocol.h"
#include "motordatamanager.h"
#include "canfdinterface.h"

QT_BEGIN_NAMESPACE
namespace Ui { class MultiMotorInterface; }
QT_END_NAMESPACE

/**
 * @brief 单个电机控制组件
 * @details 封装单个电机的控制界面，包括状态显示、参数设置、控制按钮等
 */
class MotorControlWidget : public QGroupBox
{
    Q_OBJECT

public:
    /**
     * @brief 构造函数
     * @param motor_id 电机ID (1-6)
     * @param parent 父窗口指针
     */
    explicit MotorControlWidget(quint8 motor_id, QWidget *parent = nullptr);

    /**
     * @brief 更新电机状态显示
     * @param status 电机状态帧
     */
    void updateStatus(const MotorStatusFrame& status);

    /**
     * @brief 设置电机连接状态
     * @param connected 是否连接
     */
    void setConnected(bool connected);

    /**
     * @brief 获取目标速度
     * @return 目标速度值
     */
    int getTargetSpeed() const;

    /**
     * @brief 获取目标位置
     * @return 目标位置值
     */
    int getTargetPosition() const;

    /**
     * @brief 获取控制模式
     * @return 控制模式
     */
    ControlMode getControlMode() const;

    /**
     * @brief 获取电机ID
     * @return 电机ID
     */
    quint8 getMotorId() const { return m_motorId; }

    /**
     * @brief 设置使能状态
     * @param enabled 是否使能
     */
    void setEnabled(bool enabled);

signals:
    /**
     * @brief 启动电机信号
     * @param motor_id 电机ID
     */
    void startMotor(quint8 motor_id);

    /**
     * @brief 停止电机信号
     * @param motor_id 电机ID
     */
    void stopMotor(quint8 motor_id);

    /**
     * @brief 使能电机信号
     * @param motor_id 电机ID
     */
    void enableMotor(quint8 motor_id);

    /**
     * @brief 失能电机信号
     * @param motor_id 电机ID
     */
    void disableMotor(quint8 motor_id);

    /**
     * @brief 设置速度信号
     * @param motor_id 电机ID
     * @param speed 目标速度
     */
    void setSpeed(quint8 motor_id, int speed);

    /**
     * @brief 设置位置信号
     * @param motor_id 电机ID
     * @param position 目标位置
     */
    void setPosition(quint8 motor_id, int position);

    /**
     * @brief 回零信号
     * @param motor_id 电机ID
     */
    void homeMotor(quint8 motor_id);

    /**
     * @brief 清除错误信号
     * @param motor_id 电机ID
     */
    void clearError(quint8 motor_id);

private slots:
    /**
     * @brief 启动按钮点击槽函数
     */
    void onStartClicked();

    /**
     * @brief 停止按钮点击槽函数
     */
    void onStopClicked();

    /**
     * @brief 使能按钮点击槽函数
     */
    void onEnableClicked();

    /**
     * @brief 设置速度按钮点击槽函数
     */
    void onSetSpeedClicked();

    /**
     * @brief 设置位置按钮点击槽函数
     */
    void onSetPositionClicked();

    /**
     * @brief 回零按钮点击槽函数
     */
    void onHomeClicked();

    /**
     * @brief 清除错误按钮点击槽函数
     */
    void onClearErrorClicked();

private:
    /**
     * @brief 初始化界面
     */
    void initializeUI();

    /**
     * @brief 设置样式
     */
    void setupStyles();

private:
    quint8 m_motorId;                   ///< 电机ID
    bool m_isConnected;                 ///< 连接状态
    bool m_isEnabled;                   ///< 使能状态

    // 状态显示控件
    QLabel* m_statusLabel;              ///< 状态标签
    QLabel* m_speedLabel;               ///< 速度显示
    QLabel* m_positionLabel;            ///< 位置显示
    QLabel* m_currentLabel;             ///< 电流显示
    QLabel* m_voltageLabel;             ///< 电压显示
    QLabel* m_temperatureLabel;         ///< 温度显示
    QLabel* m_errorLabel;               ///< 错误显示
    QProgressBar* m_loadProgressBar;    ///< 负载进度条

    // 控制参数控件
    QComboBox* m_modeComboBox;          ///< 控制模式选择
    QSpinBox* m_targetSpeedSpinBox;     ///< 目标速度设置
    QSpinBox* m_targetPositionSpinBox;  ///< 目标位置设置

    // 控制按钮
    QPushButton* m_enableButton;        ///< 使能按钮
    QPushButton* m_startButton;         ///< 启动按钮
    QPushButton* m_stopButton;          ///< 停止按钮
    QPushButton* m_setSpeedButton;      ///< 设置速度按钮
    QPushButton* m_setPositionButton;   ///< 设置位置按钮
    QPushButton* m_homeButton;          ///< 回零按钮
    QPushButton* m_clearErrorButton;    ///< 清除错误按钮
};

/**
 * @brief 多电机控制界面类
 * @details 提供6个电机的统一控制界面，支持单独控制和批量操作
 */
class MultiMotorInterface : public QWidget
{
    Q_OBJECT

public:
    /**
     * @brief 构造函数
     * @param parent 父窗口指针
     */
    MultiMotorInterface(QWidget *parent = nullptr);

    /**
     * @brief 析构函数
     */
    ~MultiMotorInterface();

    /**
     * @brief 设置CANFD接口
     * @param canfd_interface CANFD接口指针
     */
    void setCANFDInterface(CANFDInterface* canfd_interface);

    /**
     * @brief 设置数据管理器
     * @param data_manager 数据管理器指针
     */
    void setDataManager(MotorDataManager* data_manager);

public slots:
    /**
     * @brief 更新电机状态
     * @param motor_id 电机ID
     * @param status 状态帧
     */
    void updateMotorStatus(quint8 motor_id, const MotorStatusFrame& status);

    /**
     * @brief 电机连接状态改变
     * @param motor_id 电机ID
     * @param connected 是否连接
     */
    void onMotorConnectionChanged(quint8 motor_id, bool connected);

    /**
     * @brief 电机错误处理
     * @param motor_id 电机ID
     * @param error_code 错误代码
     */
    void onMotorError(quint8 motor_id, quint8 error_code);

signals:
    /**
     * @brief 返回主界面信号
     */
    void backToMainInterface();

    /**
     * @brief 打开示波器信号
     * @param motor_id 选中的电机ID
     */
    void openOscilloscope(quint8 motor_id);

private slots:
    /**
     * @brief 返回按钮点击槽函数
     */
    void onBackClicked();

    /**
     * @brief 示波器按钮点击槽函数
     */
    void onOscilloscopeClicked();

    /**
     * @brief 全部启动按钮点击槽函数
     */
    void onStartAllClicked();

    /**
     * @brief 全部停止按钮点击槽函数
     */
    void onStopAllClicked();

    /**
     * @brief 全部使能按钮点击槽函数
     */
    void onEnableAllClicked();

    /**
     * @brief 全部失能按钮点击槽函数
     */
    void onDisableAllClicked();

    /**
     * @brief 紧急停止按钮点击槽函数
     */
    void onEmergencyStopClicked();

    /**
     * @brief 全部回零按钮点击槽函数
     */
    void onHomeAllClicked();

    /**
     * @brief 清除所有错误按钮点击槽函数
     */
    void onClearAllErrorsClicked();

    /**
     * @brief 刷新状态按钮点击槽函数
     */
    void onRefreshClicked();

    // 单个电机控制槽函数
    void onMotorStart(quint8 motor_id);
    void onMotorStop(quint8 motor_id);
    void onMotorEnable(quint8 motor_id);
    void onMotorDisable(quint8 motor_id);
    void onMotorSetSpeed(quint8 motor_id, int speed);
    void onMotorSetPosition(quint8 motor_id, int position);
    void onMotorHome(quint8 motor_id);
    void onMotorClearError(quint8 motor_id);

    /**
     * @brief 状态更新定时器槽函数
     */
    void onStatusUpdateTimer();

private:
    /**
     * @brief 初始化界面
     */
    void initializeUI();

    /**
     * @brief 设置样式
     */
    void setupStyles();

    /**
     * @brief 连接信号槽
     */
    void connectSignals();

    /**
     * @brief 发送电机控制命令
     * @param motor_id 电机ID
     * @param command 控制命令
     * @param target_value 目标值
     * @param control_mode 控制模式
     */
    void sendMotorCommand(quint8 motor_id, CommandType command, 
                         quint32 target_value = 0, ControlMode control_mode = MODE_SPEED);

    /**
     * @brief 发送系统广播命令
     * @param broadcast_cmd 广播命令
     * @param motor_mask 电机掩码
     */
    void sendSystemBroadcast(BroadcastCommand broadcast_cmd, quint8 motor_mask = 0x3F);

private:
    Ui::MultiMotorInterface *ui;        ///< UI界面指针

    // 核心组件
    CANFDInterface* m_canfdInterface;   ///< CANFD接口指针
    MotorDataManager* m_dataManager;    ///< 数据管理器指针
    QTimer* m_statusTimer;              ///< 状态更新定时器

    // 电机控制组件
    QVector<MotorControlWidget*> m_motorWidgets;  ///< 电机控制组件数组

    // 批量控制按钮
    QPushButton* m_backButton;          ///< 返回按钮
    QPushButton* m_oscilloscopeButton;  ///< 示波器按钮
    QPushButton* m_startAllButton;      ///< 全部启动按钮
    QPushButton* m_stopAllButton;       ///< 全部停止按钮
    QPushButton* m_enableAllButton;     ///< 全部使能按钮
    QPushButton* m_disableAllButton;    ///< 全部失能按钮
    QPushButton* m_emergencyStopButton; ///< 紧急停止按钮
    QPushButton* m_homeAllButton;       ///< 全部回零按钮
    QPushButton* m_clearAllErrorsButton; ///< 清除所有错误按钮
    QPushButton* m_refreshButton;       ///< 刷新状态按钮

    // 状态显示
    QLabel* m_connectionStatusLabel;    ///< 连接状态标签
    QLabel* m_systemStatusLabel;        ///< 系统状态标签
    QLabel* m_lastUpdateLabel;          ///< 最后更新时间标签

    // 序列号管理
    quint8 m_sequences[6];              ///< 各电机的序列号
};

#endif // MULTIMOTORINTERFACE_H
