/**
 * @file modbusprotocol.h
 * @brief Modbus协议处理类头文件
 * @details 定义了Modbus协议处理类ModbusProtocol，提供完整的Modbus RTU协议
 *          实现功能。该类封装了Modbus协议的核心功能，包括CRC校验计算、
 *          数据帧构建和解析、各种功能码的实现等。
 * <AUTHOR>
 * @date 2025-07-02
 * @version 1.0
 */

#ifndef MODBUSPROTOCOL_H
#define MODBUSPROTOCOL_H

#include <QObject>      // Qt对象基类
#include <QByteArray>   // Qt字节数组类
#include <QVariant>     // Qt变体类型类
#include <QMap>         // Qt映射容器类
#include <QString>      // Qt字符串类

/**
 * @class ModbusProtocol
 * @brief Modbus协议处理类
 * @details 继承自QObject，提供完整的Modbus RTU协议实现功能。
 *          该类封装了Modbus协议的核心功能，包括：
 *          1. CRC-16校验计算和验证
 *          2. Modbus数据帧的构建和解析
 *          3. 常用功能码的完整实现
 *          4. 数据类型转换和处理
 *          5. 错误检测和异常处理
 */
class ModbusProtocol : public QObject
{
    Q_OBJECT  // Qt元对象系统宏，支持信号槽机制

public:
    /**
     * @enum FunctionCode
     * @brief Modbus功能码枚举
     * @details 定义了Modbus RTU协议中常用的功能码，用于不同类型的数据操作
     */
    enum FunctionCode {
        ReadCoils = 0x01,                    ///< 读取线圈状态（0x01）
        ReadDiscreteInputs = 0x02,           ///< 读取离散输入状态（0x02）
        ReadHoldingRegisters = 0x03,         ///< 读取保持寄存器（0x03）
        ReadInputRegisters = 0x04,           ///< 读取输入寄存器（0x04）
        WriteSingleCoil = 0x05,              ///< 写单个线圈（0x05）
        WriteSingleRegister = 0x06,          ///< 写单个寄存器（0x06）
        WriteMultipleCoils = 0x0F,           ///< 写多个线圈（0x0F）
        WriteMultipleRegisters = 0x10        ///< 写多个寄存器（0x10）
    };

    /**
     * @brief 构造函数
     * @param parent 父对象指针，默认为nullptr
     * @details 初始化Modbus协议处理对象，设置默认参数
     */
    explicit ModbusProtocol(QObject *parent = nullptr);

    /**
     * @brief 创建读取保持寄存器请求帧
     * @param slaveAddress 从站地址（1-247）
     * @param startAddress 起始寄存器地址（0-65535）
     * @param registerCount 要读取的寄存器数量（1-125）
     * @return 构建完成的Modbus RTU请求帧（包含CRC校验）
     * @details 构建符合Modbus RTU协议的读取保持寄存器请求帧，
     *          帧格式：从站地址 + 功能码(0x03) + 起始地址 + 寄存器数量 + CRC校验
     */
    QByteArray createReadHoldingRegistersRequest(uint8_t slaveAddress, uint16_t startAddress, uint16_t registerCount);
    
    /**
     * @brief 创建读取输入寄存器请求
     * @param slaveAddress 从站地址
     * @param startAddress 起始地址
     * @param registerCount 寄存器数量
     * @return 构建的Modbus请求帧
     */
    QByteArray createReadInputRegistersRequest(uint8_t slaveAddress, uint16_t startAddress, uint16_t registerCount);
    
    /**
     * @brief 创建写单个寄存器请求
     * @param slaveAddress 从站地址
     * @param registerAddress 寄存器地址
     * @param value 寄存器值
     * @return 构建的Modbus请求帧
     */
    QByteArray createWriteSingleRegisterRequest(uint8_t slaveAddress, uint16_t registerAddress, uint16_t value);
    
    /**
     * @brief 创建写多个寄存器请求
     * @param slaveAddress 从站地址
     * @param startAddress 起始地址
     * @param values 寄存器值列表
     * @return 构建的Modbus请求帧
     */
    QByteArray createWriteMultipleRegistersRequest(uint8_t slaveAddress, uint16_t startAddress, const QList<uint16_t>& values);
    
    /**
     * @brief 解析Modbus响应
     * @param response 响应数据
     * @param expectedSlaveAddress 期望的从站地址
     * @param expectedFunctionCode 期望的功能码
     * @return 解析结果，包含成功标志和数据/错误信息
     */
    QPair<bool, QString> parseResponse(const QByteArray& response, uint8_t expectedSlaveAddress, uint8_t expectedFunctionCode);
    
    /**
     * @brief 获取功能码描述
     * @param functionCode 功能码
     * @return 功能码描述文本
     */
    static QString getFunctionCodeDescription(uint8_t functionCode);
    
    /**
     * @brief 计算CRC校验值
     * @param data 需要计算校验的数据
     * @return 包含CRC校验值的字节数组(2字节)
     */
    static QByteArray calculateCRC(const QByteArray& data);
    
    /**
     * @brief 验证CRC校验
     * @param frame 完整的Modbus帧(包含CRC)
     * @return 校验是否通过
     */
    static bool validateCRC(const QByteArray& frame);
    
    /**
     * @brief 创建Modbus请求
     * @param slaveAddress 从站地址
     * @param functionCode 功能码
     * @param startAddress 起始地址
     * @param quantity 数量
     * @return 构建的Modbus请求帧
     */
    QByteArray createRequest(uint8_t slaveAddress, uint8_t functionCode, uint16_t startAddress, uint16_t quantity);
    
private:
    // CRC校验表
    static const uint8_t CRC_HIGH_TABLE[256];
    static const uint8_t CRC_LOW_TABLE[256];
    
    // 功能码描述映射
    static const QMap<uint8_t, QString> functionCodeDescriptions;
    
    // 解析不同类型的响应
    QString parseReadCoilsResponse(const QByteArray &response);
    QString parseReadRegistersResponse(const QByteArray &response);
    QString parseWriteResponse(const QByteArray &response);
    QString parseErrorResponse(const QByteArray &response);
    
    // 辅助函数
    bool checkFrameLength(const QByteArray &frame, QString &errorMsg);
    bool checkSlaveAddress(const QByteArray &frame, uint8_t expectedAddress, QString &errorMsg);
    bool checkFunctionCode(const QByteArray &frame, uint8_t expectedCode, QString &errorMsg);
};

#endif // MODBUSPROTOCOL_H 