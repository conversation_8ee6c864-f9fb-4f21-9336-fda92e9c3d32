/**
 * @file oscilloscopeinterface.h
 * @brief 虚拟示波器接口类头文件
 * @details 定义了虚拟示波器接口类OscilloscopeInterface，提供电机数据监测的专业示波器功能，
 *          包括多通道波形显示、实时数据采集、模拟数据生成、CAN/CANFD通信集成等。
 *          支持电机电气参数、机械参数、温度参数的实时监测和波形分析。
 * <AUTHOR>
 * @date 2025-07-03
 * @version 1.0
 */

#ifndef OSCILLOSCOPEINTERFACE_H
#define OSCILLOSCOPEINTERFACE_H

#include <QWidget>              // Qt窗口基类
#include <QTimer>               // Qt定时器类
#include <QTime>                // Qt时间类
#include <QDateTime>            // Qt日期时间类
#include <QPainter>             // Qt绘图类
#include <QPaintEvent>          // Qt绘图事件类
#include <QResizeEvent>         // Qt窗口大小改变事件类
#include <QVector>              // Qt向量容器类
#include <QQueue>               // Qt队列容器类
#include <QMutex>               // Qt互斥锁类
#include <QMutexLocker>         // Qt互斥锁管理类
#include <QRandomGenerator>     // Qt随机数生成器
#include <QMessageBox>          // Qt消息框类
#include <QDebug>               // Qt调试输出类
#include <QtMath>               // Qt数学函数库
#include <QEvent>               // Qt事件基类
#include "canfddevicemanager.h" // CANFD设备管理器
#include "deviceconnectiondialog.h" // 设备连接检测对话框
#include <vector>               // STL向量容器（用于环形缓冲区）
#include "common_datastructures.h" // 通用数据结构

// Qt命名空间标记
QT_BEGIN_NAMESPACE
namespace Ui { class OscilloscopeInterface; }  // UI类前向声明
QT_END_NAMESPACE

// 前向声明
class OscilloscopeInterface;

/**
 * @brief 自定义绘图widget类
 * @details 专门用于绘制示波器波形的widget
 */
class PlotWidget : public QWidget
{
    Q_OBJECT

public:
    explicit PlotWidget(QWidget *parent = nullptr);
    void setOscilloscopeInterface(OscilloscopeInterface *interface);

protected:
    void paintEvent(QPaintEvent *event) override;
    void mousePressEvent(QMouseEvent *event) override;
    void mouseMoveEvent(QMouseEvent *event) override;
    void mouseReleaseEvent(QMouseEvent *event) override;
    void wheelEvent(QWheelEvent *event) override;
    void focusInEvent(QFocusEvent *event) override;
    void showEvent(QShowEvent *event) override;

private:
    OscilloscopeInterface *oscilloscope;

    // 鼠标控制相关
    bool isDragging;
    QPoint lastMousePos;
    double timeOffset;      // 时间轴偏移
    double zoomFactor;      // 缩放因子
};

/**
 * @brief 电机数据通道枚举
 * @details 定义了示波器支持的所有电机监测数据通道类型
 */
enum class MotorDataChannel {
    // 电气参数通道
    CURRENT_A = 0,          ///< A相电流 (A)
    CURRENT_B,              ///< B相电流 (A)
    CURRENT_C,              ///< C相电流 (A)
    VOLTAGE_BUS,            ///< 母线电压 (V)
    POWER,                  ///< 功率 (W)
    
    // 机械参数通道
    SPEED,                  ///< 转速 (RPM)
    TORQUE,                 ///< 转矩 (N·m)
    POSITION,               ///< 位置 (°)
    VIBRATION,              ///< 振动 (g)
    
    // 温度参数通道
    MOTOR_TEMP,             ///< 电机温度 (°C)
    CONTROLLER_TEMP,        ///< 控制器温度 (°C)
    
    // 控制参数通道
    PWM_DUTY,               ///< PWM占空比 (%)
    FREQUENCY,              ///< 频率 (Hz)
    EFFICIENCY,             ///< 效率 (%)
    
    CHANNEL_COUNT           ///< 通道总数
};

// DataSourceType枚举已在canfddevicemanager.h中定义

/**
 * @brief 触发模式枚举
 * @details 定义了示波器的触发模式
 */
enum class TriggerMode {
    AUTO,                   ///< 自动触发
    NORMAL,                 ///< 正常触发
    SINGLE                  ///< 单次触发
};

// 使用命名空间中的定义（通过完全限定名避免冲突）

/**
 * @brief 通道数据结构体
 * @details 存储单个通道的完整数据信息
 */
struct ChannelData {
    MotorDataChannel channel;           ///< 通道类型
    QString name;                       ///< 通道名称
    QString unit;                       ///< 单位
    QColor color;                       ///< 显示颜色
    bool enabled;                       ///< 是否启用
    double scale;                       ///< 垂直缩放比例
    double offset;                      ///< 垂直偏移
    Oscilloscope::CircularBuffer<Oscilloscope::DataPoint> dataBuffer; ///< 优化的环形数据缓冲区
    double minValue;                    ///< 最小值
    double maxValue;                    ///< 最大值
    double currentValue;                ///< 当前值

    ChannelData() : channel(MotorDataChannel::CURRENT_A), enabled(false),
                   scale(1.0), offset(0.0), dataBuffer(1000),
                   minValue(0.0), maxValue(0.0), currentValue(0.0) {}
};

/**
 * @brief 虚拟示波器接口类
 * @details 提供专业的电机数据监测示波器功能，支持多通道实时波形显示、
 *          数据采集、模拟数据生成、CAN/CANFD通信等功能
 */
class OscilloscopeInterface : public QWidget
{
    Q_OBJECT

public:
    /**
     * @brief 构造函数
     * @param parent 父窗口指针，默认为nullptr
     * @details 初始化示波器界面和所有必要的组件
     */
    explicit OscilloscopeInterface(QWidget *parent = nullptr);
    
    /**
     * @brief 析构函数
     * @details 清理资源，停止所有定时器和数据采集
     */
    ~OscilloscopeInterface();

    /**
     * @brief 绘制网格
     * @param painter 绘图对象指针
     * @param rect 绘制区域
     * @details 绘制示波器的网格背景
     */
    void drawGrid(QPainter *painter, const QRect &rect);

    /**
     * @brief 绘制所有启用的通道
     * @param painter 绘图对象指针
     * @param rect 绘制区域
     * @details 绘制所有启用通道的波形
     */
    void drawAllChannels(QPainter *painter, const QRect &rect);

    /**
     * @brief 绘制通道波形
     * @param painter 绘图对象指针
     * @param rect 绘制区域
     * @param channelData 通道数据
     * @details 绘制指定通道的波形曲线
     */
    void drawChannelWaveform(QPainter *painter, const QRect &rect, const ChannelData &channelData);

    /**
     * @brief 绘制通道信息
     * @param painter 绘图对象指针
     * @param rect 绘制区域
     * @details 绘制通道的状态信息和数值
     */
    void drawChannelInfo(QPainter *painter, const QRect &rect);

    /**
     * @brief 获取当前时间刻度
     * @return 当前时间刻度值
     */
    double getTimeScale() const { return timeScale; }

    /**
     * @brief 获取基础时间刻度
     * @return 基础时间刻度值
     */
    double getBaseTimeScale() const { return baseTimeScale; }

    /**
     * @brief 设置时间刻度
     * @param scale 新的时间刻度值
     */
    void setTimeScale(double scale);

    /**
     * @brief 设置时间偏移
     * @param offset 新的时间偏移值
     */
    void setTimeOffset(double offset);

    /**
     * @brief 获取当前绘图区域尺寸
     * @return 绘图区域矩形
     */
    QRect getPlotArea() const { return plotArea; }

    /**
     * @brief 获取通道单位
     * @param channel 通道类型
     * @return 通道单位字符串
     */
    QString getChannelUnit(MotorDataChannel channel) const;

public slots:
    /**
     * @brief 开始模拟数据生成
     * @details 启动模拟数据定时器，生成电机运行的模拟波形数据
     */
    void startSimulation();
    
    /**
     * @brief 停止模拟数据生成
     * @details 停止模拟数据定时器，清除当前数据
     */
    void stopSimulation();
    
    /**
     * @brief 暂停/恢复数据显示
     * @details 暂停或恢复波形的实时更新显示
     */
    void togglePause();
    
    /**
     * @brief 清除所有波形数据
     * @details 清空所有通道的数据缓冲区，重置显示
     */
    void clearAllData();
    
    /**
     * @brief 通道启用状态改变槽函数
     * @details 响应通道复选框的状态改变，更新通道显示
     */
    void onChannelEnabledChanged();
    
    /**
     * @brief 时间刻度改变槽函数
     * @details 响应时间刻度下拉框的改变，更新时间轴显示
     */
    void onTimeScaleChanged();
    
    /**
     * @brief 采样率改变槽函数
     * @details 响应采样率设置的改变，更新数据采集频率
     */
    void onSampleRateChanged();
    
    /**
     * @brief 触发模式改变槽函数
     * @details 响应触发模式设置的改变，更新触发逻辑
     */
    void onTriggerModeChanged();
    
    /**
     * @brief 更新频率改变槽函数
     * @details 响应更新频率控件的变化
     */
    void onUpdateRateChanged();

    /**
     * @brief 返回主界面槽函数
     * @details 发送返回主界面的信号
     */
    void onBackClicked();

signals:
    /**
     * @brief 返回主界面信号
     * @details 当用户点击返回按钮时发送此信号
     */
    void backToMainInterface();
    
    /**
     * @brief 数据源状态改变信号
     * @param sourceType 新的数据源类型
     * @details 当数据源从模拟切换到真实数据时发送此信号
     */
    void dataSourceChanged(DataSourceType sourceType);

protected:

    
    /**
     * @brief 窗口大小改变事件处理函数
     * @param event 大小改变事件指针
     * @details 重写QWidget的resizeEvent，响应窗口大小改变
     */
    void resizeEvent(QResizeEvent *event) override;

private slots:
    /**
     * @brief 模拟数据生成定时器槽函数
     * @details 定时生成模拟的电机运行数据，更新各通道数据
     */
    void generateSimulationData();
    
    /**
     * @brief 波形更新定时器槽函数
     * @details 定时更新波形显示，重绘界面
     */
    void updateWaveforms();

private:
    /**
     * @brief 初始化用户界面
     * @details 设置UI组件的初始状态和连接信号槽
     */
    void initializeUI();
    
    /**
     * @brief 初始化通道数据
     * @details 设置所有电机数据通道的初始参数和属性
     */
    void initializeChannels();
    
    /**
     * @brief 设置信号槽连接
     * @details 连接UI组件的信号到相应的槽函数
     */
    void setupConnections();
    
    /**
     * @brief 更新数据源状态显示
     * @param sourceType 当前数据源类型
     * @details 更新界面上的数据源状态指示
     */
    void updateDataSourceStatus(DataSourceType sourceType);
    
    /**
     * @brief 生成指定通道的模拟数据
     * @param channel 通道类型
     * @param timestamp 时间戳
     * @return 生成的数据值
     * @details 根据通道类型生成相应的模拟电机数据
     */
    double generateChannelData(MotorDataChannel channel, double timestamp);
    
    /**
     * @brief 添加数据点到指定通道
     * @param channel 通道类型
     * @param value 数据值
     * @param timestamp 时间戳
     * @details 将新数据点添加到通道缓冲区，管理缓冲区大小
     */
    void addDataPoint(MotorDataChannel channel, double value, double timestamp);
    


private:
    Ui::OscilloscopeInterface *ui;              ///< UI界面指针
    
    // 定时器
    QTimer *simulationTimer;                    ///< 模拟数据生成定时器
    QTimer *updateTimer;                        ///< 波形更新定时器
    
    // 数据管理
    QVector<ChannelData> channels;              ///< 所有通道数据
    QMutex dataMutex;                          ///< 数据访问互斥锁
    DataSourceType currentDataSource;          ///< 当前数据源类型
    bool isConnected;                          ///< 连接状态
    
    // 显示参数
    double timeScale;                          ///< 时间刻度 (秒/格)
    double baseTimeScale;                      ///< 基础时间刻度 (用于缩放计算)
    double timeOffset;                         ///< 时间偏移 (用于拖动)
    int sampleRate;                            ///< 采样率 (Hz)
    int updateRate;                            ///< 更新频率 (Hz)
    bool isPaused;                             ///< 是否暂停显示
    int maxDataPoints;                         ///< 最大数据点数
    
    // 触发参数
    TriggerMode triggerMode;                   ///< 触发模式
    double triggerLevel;                       ///< 触发电平
    
    // 模拟数据参数
    double simulationTime;                     ///< 模拟时间计数器
    double motorSpeed;                         ///< 模拟电机转速
    double motorLoad;                          ///< 模拟电机负载
    
    // 绘图参数
    QRect plotArea;                            ///< 波形绘制区域
    int gridDivisions;                         ///< 网格分割数

    // 设备管理相关
    CANFDDeviceManager* m_deviceManager;       ///< CANFD设备管理器
    DataSourceType m_currentDataSource;        ///< 当前数据源类型
    bool m_useRealData;                        ///< 是否使用真实数据

public:
    /**
     * @brief 检测设备连接并显示选择对话框
     * @return 用户是否选择继续
     */
    bool checkDeviceConnectionAndPrompt();

private slots:
    /**
     * @brief 接收到电机状态数据槽函数
     * @param motorId 电机ID
     * @param statusFrame 状态数据帧
     */
    void onMotorStatusReceived(int motorId, const MotorStatusFrame& statusFrame);

    /**
     * @brief 设备连接状态改变槽函数
     * @param status 新的连接状态
     */
    void onConnectionStatusChanged(CANFDConnectionStatus status);

private:
    /**
     * @brief 切换数据源
     * @param dataSource 新的数据源类型
     */
    void switchDataSource(DataSourceType dataSource);
};

#endif // OSCILLOSCOPEINTERFACE_H
