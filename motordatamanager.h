/**
 * @file motordatamanager.h
 * @brief 多电机数据管理器类头文件
 * @details 定义了多电机数据管理器类MotorDataManager，负责管理6个电机的
 *          实时数据、历史数据、控制参数等，为上位机提供统一的数据接口。
 *          支持数据缓存、统计分析、示波器数据提供等功能。
 * <AUTHOR>
 * @date 2025-01-28
 * @version 1.0
 */

#ifndef MOTORDATAMANAGER_H
#define MOTORDATAMANAGER_H

#include <QObject>
#include <QMutex>
#include <QTimer>
#include <QDateTime>
#include <QVector>
#include <QQueue>
#include <QMap>
#include "motorprotocol.h"
#include "common_datastructures.h"

// 使用命名空间中的定义（通过完全限定名避免冲突）

/**
 * @brief 电机数据统计信息
 * @details 包含电机运行的统计数据
 */
struct MotorStatistics {
    // 基本统计
    quint32 total_runtime;          ///< 总运行时间(秒)
    quint32 total_commands;         ///< 总命令数
    quint32 total_errors;           ///< 总错误数
    quint32 communication_errors;   ///< 通信错误数
    
    // 性能统计
    double avg_speed;               ///< 平均速度
    double max_speed;               ///< 最大速度
    double avg_current;             ///< 平均电流
    double max_current;             ///< 最大电流
    double avg_temperature;         ///< 平均温度
    double max_temperature;         ///< 最大温度
    
    // 时间统计
    QDateTime first_start_time;     ///< 首次启动时间
    QDateTime last_update_time;     ///< 最后更新时间
    
    /**
     * @brief 构造函数
     */
    MotorStatistics() {
        memset(this, 0, sizeof(MotorStatistics));
    }
};

/**
 * @brief 单个电机数据结构
 * @details 包含单个电机的所有相关数据
 */
struct SingleMotorData {
    // === 基本信息 ===
    quint8 motor_id;                        ///< 电机ID (1-6)
    bool is_connected;                      ///< 是否连接
    bool is_enabled;                        ///< 是否使能
    
    // === 实时状态数据 ===
    MotorStatusFrame current_status;        ///< 当前状态帧
    MotorControlFrame last_command;         ///< 最后发送的命令
    
    // === 历史数据缓冲区 (暂时注释掉，避免崩溃) ===
    // MotorData::CircularBuffer<MotorData::DataPoint> speed_history;     ///< 速度历史 → 蓝色通道
    // MotorData::CircularBuffer<MotorData::DataPoint> current_history;   ///< 电流历史 → 红色通道
    // MotorData::CircularBuffer<MotorData::DataPoint> voltage_history;   ///< 电压历史 → 绿色通道
    // MotorData::CircularBuffer<MotorData::DataPoint> temp_history;      ///< 温度历史 → 黄色通道
    // MotorData::CircularBuffer<MotorData::DataPoint> position_history;  ///< 位置历史
    // MotorData::CircularBuffer<MotorData::DataPoint> torque_history;    ///< 转矩历史
    
    // === 通信状态 (暂时简化，避免QDateTime导致的崩溃) ===
    // QDateTime last_command_time;            ///< 最后命令时间
    // QDateTime last_status_time;             ///< 最后状态更新时间
    quint8 last_sequence;                   ///< 最后序列号
    bool communication_timeout;             ///< 通信超时标志
    
    // === 统计信息 (暂时注释掉，避免崩溃) ===
    // MotorStatistics statistics;             ///< 统计数据
    
    /**
     * @brief 构造函数
     * @param id 电机ID
     */
    explicit SingleMotorData(quint8 id = 0)
        : motor_id(id), is_connected(false), is_enabled(false),
          last_sequence(0), communication_timeout(false) {
        // 初始化状态帧和控制帧为默认值
        memset(&current_status, 0, sizeof(current_status));
        memset(&last_command, 0, sizeof(last_command));
        // CircularBuffer初始化已注释掉
    }
};

/**
 * @brief 多电机数据管理器类
 * @details 管理6个电机的所有数据，提供统一的数据访问接口
 */
class MotorDataManager : public QObject
{
    Q_OBJECT

public:
    /**
     * @brief 构造函数
     * @param parent 父对象指针
     */
    explicit MotorDataManager(QObject *parent = nullptr);
    
    /**
     * @brief 析构函数
     */
    ~MotorDataManager();
    
    // === 数据访问接口 ===
    
    /**
     * @brief 获取电机数据
     * @param motor_id 电机ID (1-6)
     * @return 电机数据引用
     */
    const SingleMotorData& getMotorData(quint8 motor_id) const;

    /**
     * @brief 获取所有电机数据
     * @return 所有电机数据的向量
     */
    QVector<SingleMotorData> getAllMotorData() const;
    
    /**
     * @brief 获取电机当前状态
     * @param motor_id 电机ID (1-6)
     * @return 状态帧
     */
    MotorStatusFrame getMotorStatus(quint8 motor_id) const;
    
    /**
     * @brief 获取电机统计信息
     * @param motor_id 电机ID (1-6)
     * @return 统计信息
     */
    MotorStatistics getMotorStatistics(quint8 motor_id) const;
    
    // === 数据更新接口 ===
    
    /**
     * @brief 更新电机状态
     * @param motor_id 电机ID (1-6)
     * @param status 状态帧
     */
    void updateMotorStatus(quint8 motor_id, const MotorStatusFrame& status);
    
    /**
     * @brief 记录发送的控制命令
     * @param motor_id 电机ID (1-6)
     * @param command 控制命令帧
     */
    void recordControlCommand(quint8 motor_id, const MotorControlFrame& command);
    
    /**
     * @brief 设置电机连接状态
     * @param motor_id 电机ID (1-6)
     * @param connected 是否连接
     */
    void setMotorConnected(quint8 motor_id, bool connected);
    
    /**
     * @brief 设置电机使能状态
     * @param motor_id 电机ID (1-6)
     * @param enabled 是否使能
     */
    void setMotorEnabled(quint8 motor_id, bool enabled);
    
    // === 示波器数据接口 ===
    
    /**
     * @brief 获取示波器数据
     * @param motor_id 电机ID (1-6)
     * @param channel 数据通道类型
     * @param count 数据点数量
     * @return 数据点向量
     */
    QVector<MotorData::DataPoint> getOscilloscopeData(quint8 motor_id, int channel, int count = 1000) const;

    /**
     * @brief 获取最新的示波器数据点
     * @param motor_id 电机ID (1-6)
     * @param channel 数据通道类型
     * @return 最新数据点
     */
    MotorData::DataPoint getLatestDataPoint(quint8 motor_id, int channel) const;
    
    // === 数据管理接口 ===
    
    /**
     * @brief 清空所有历史数据
     */
    void clearAllHistory();
    
    /**
     * @brief 清空指定电机的历史数据
     * @param motor_id 电机ID (1-6)
     */
    void clearMotorHistory(quint8 motor_id);
    
    /**
     * @brief 重置统计信息
     */
    void resetStatistics();
    
    /**
     * @brief 重置指定电机的统计信息
     * @param motor_id 电机ID (1-6)
     */
    void resetMotorStatistics(quint8 motor_id);
    
    // === 通信状态管理 ===
    
    /**
     * @brief 检查通信超时
     * @param timeout_ms 超时时间(毫秒)
     * @return 超时的电机ID列表
     */
    QVector<quint8> checkCommunicationTimeout(int timeout_ms = 1000);
    
    /**
     * @brief 获取连接的电机数量
     * @return 连接的电机数量
     */
    int getConnectedMotorCount() const;
    
    /**
     * @brief 获取使能的电机数量
     * @return 使能的电机数量
     */
    int getEnabledMotorCount() const;

public slots:
    /**
     * @brief 定时检查通信状态
     * @details 定时检查各电机的通信状态，更新超时标志
     */
    void checkCommunicationStatus();

signals:
    /**
     * @brief 电机状态更新信号
     * @param motor_id 电机ID
     * @param status 状态帧
     */
    void motorStatusUpdated(quint8 motor_id, const MotorStatusFrame& status);
    
    /**
     * @brief 电机连接状态改变信号
     * @param motor_id 电机ID
     * @param connected 是否连接
     */
    void motorConnectionChanged(quint8 motor_id, bool connected);
    
    /**
     * @brief 电机使能状态改变信号
     * @param motor_id 电机ID
     * @param enabled 是否使能
     */
    void motorEnabledChanged(quint8 motor_id, bool enabled);
    
    /**
     * @brief 通信超时信号
     * @param motor_id 电机ID
     */
    void communicationTimeout(quint8 motor_id);
    
    /**
     * @brief 电机错误信号
     * @param motor_id 电机ID
     * @param error_code 错误代码
     */
    void motorError(quint8 motor_id, quint8 error_code);
    
    /**
     * @brief 新数据点可用信号（给示波器使用）
     * @param motor_id 电机ID
     * @param channel 数据通道
     * @param data_point 数据点
     */
    void newDataPointAvailable(quint8 motor_id, int channel, const MotorData::DataPoint& data_point);

private:
    /**
     * @brief 验证电机ID
     * @param motor_id 电机ID
     * @return 是否有效
     */
    bool isValidMotorId(quint8 motor_id) const;
    
    /**
     * @brief 更新统计信息
     * @param motor_id 电机ID
     * @param status 状态帧
     */
    void updateStatistics(quint8 motor_id, const MotorStatusFrame& status);
    
    /**
     * @brief 添加历史数据点
     * @param motor_id 电机ID
     * @param status 状态帧
     */
    void addHistoryDataPoints(quint8 motor_id, const MotorStatusFrame& status);

private:
    QVector<SingleMotorData> m_motors;        ///< 电机数据数组 (索引0-5对应电机1-6)
    mutable QMutex m_mutex;             ///< 数据访问互斥锁
    QTimer* m_checkTimer;               ///< 通信状态检查定时器
    
    // 数据通道枚举（与示波器对应）
    enum DataChannel {
        CHANNEL_SPEED = 0,      ///< 速度通道 (蓝色)
        CHANNEL_CURRENT = 1,    ///< 电流通道 (红色)
        CHANNEL_VOLTAGE = 2,    ///< 电压通道 (绿色)
        CHANNEL_TEMPERATURE = 3, ///< 温度通道 (黄色)
        CHANNEL_POSITION = 4,   ///< 位置通道
        CHANNEL_TORQUE = 5      ///< 转矩通道
    };
};

#endif // MOTORDATAMANAGER_H
