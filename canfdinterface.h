/**
 * @file canfdinterface.h
 * @brief CANFD通信接口类头文件
 * @details 定义了CANFD通信接口类CANFDInterface，提供完整的CANFD协议通信功能，
 *          包括设备连接、数据收发、频率控制、心跳功能、帧类型切换等。
 *          支持CANFD协议的长短帧交替发送和高速数据传输。
 * <AUTHOR>
 * @date 2025-07-02
 * @version 1.0
 */

#ifndef CANFDINTERFACE_H
#define CANFDINTERFACE_H

#include <QWidget>          // Qt窗口基类
#include <QTimer>           // Qt定时器类
#include <QTime>            // Qt时间类
#include <QMessageBox>      // Qt消息框类
#include <QFileDialog>      // Qt文件对话框类
#include <QTextCursor>      // Qt文本光标类
#include <QDateTime>        // Qt日期时间类
#include <QLibrary>         // Qt动态库加载类
#include "canconfig.h"      // CAN配置数据结构
#include "zlgcan.h"         // ZLGCAN API头文件
#include "basecaninterface.h" // CAN接口基类

// Qt命名空间标记
QT_BEGIN_NAMESPACE
namespace Ui { class CANFDInterface; }  // UI类前向声明
QT_END_NAMESPACE

/**
 * @class CANFDInterface
 * @brief CANFD通信接口类
 * @details 继承自BaseCANInterface，提供完整的CANFD协议通信功能界面和逻辑。
 *          该类支持CANFD协议的高速数据传输、长短帧交替发送、
 *          BRS和ESI标志控制等CANFD特有功能。
 */
class CANFDInterface : public BaseCANInterface
{
    Q_OBJECT  // Qt元对象系统宏，支持信号槽机制

public:
    /**
     * @brief 构造函数
     * @param parent 父窗口指针，默认为nullptr
     * @details 初始化CANFD通信接口，创建UI界面和设置各种功能
     */
    CANFDInterface(QWidget *parent = nullptr);

    /**
     * @brief 析构函数
     * @details 清理资源，断开设备连接，释放内存
     */
    ~CANFDInterface();

    /**
     * @brief 设置配置数据
     * @param config CAN配置数据结构
     * @details 应用用户配置的CANFD参数到通信接口
     */
    void setConfigData(const CANConfigData &config) override;

    // 实现基类的纯虚函数
    QTextEdit* getTargetTextEdit(bool isReceived) override;
    bool tryConnectRealDevice() override;
    void updateControlState() override;

    // 重写基类的虚函数
    void startCommunication() override;
    void stopCommunication() override;
    void appendLog(const QString &text, bool isReceived) override;

private slots:
    /**
     * @brief 连接设备槽函数
     * @details 响应连接按钮点击，初始化并连接CANFD设备
     */
    void onConnectDevice();

    /**
     * @brief 断开设备槽函数
     * @details 响应断开按钮点击，断开CANFD设备连接
     */
    void onDisconnectDevice();

    /**
     * @brief 发送数据槽函数
     * @details 响应发送按钮点击，发送单次CANFD数据帧
     */
    void onSendData();

    /**
     * @brief 连续发送槽函数
     * @details 响应连续发送按钮点击，启动或停止连续发送
     */
    void onContinuousSend();

    /**
     * @brief 发送心跳槽函数
     * @details 响应心跳按钮点击，启动或停止心跳发送
     */
    void onSendHeartbeat();

    /**
     * @brief 清空发送区域槽函数
     * @details 响应清空发送按钮点击，清空发送数据显示区域
     */
    void onClearSendArea();

    /**
     * @brief 清空接收区域槽函数
     * @details 响应清空接收按钮点击，清空接收数据显示区域
     */
    void onClearReceiveArea();

    /**
     * @brief 保存数据槽函数
     * @details 响应保存按钮点击，将接收数据保存到文件
     */
    void onSaveData();

    /**
     * @brief 返回按钮点击槽函数
     * @details 响应返回按钮点击，发出返回信号
     */
    void onBackClicked();

    /**
     * @brief 心跳定时器槽函数
     * @details 心跳定时器超时时调用，发送心跳数据帧
     */
    void onHeartbeatTimer();

    /**
     * @brief 接收定时器槽函数
     * @details 接收定时器超时时调用，检查并处理接收到的数据
     */
    void onReceiveTimer();

    /**
     * @brief 发送频率改变槽函数
     * @details 响应发送频率SpinBox值变化，更新连续发送频率
     */
    void onSendFrequencyChanged();

    /**
     * @brief 接收频率改变槽函数
     * @details 响应接收频率SpinBox值变化，更新接收检查频率
     */
    void onReceiveFrequencyChanged();

    /**
     * @brief 发送频率预设改变槽函数
     * @details 响应发送频率预设ComboBox选择变化，快速设置发送频率
     */
    void onSendFrequencyPresetChanged();

    /**
     * @brief 接收频率预设改变槽函数
     * @details 响应接收频率预设ComboBox选择变化，快速设置接收频率
     */
    void onReceiveFrequencyPresetChanged();

    /**
     * @brief FD模式改变槽函数
     * @details 响应CANFD模式控件变化，切换长短帧发送模式
     */
    void onFdModeChanged();

    /**
     * @brief 连续发送定时器槽函数
     * @details 连续发送定时器超时时调用，执行连续发送操作
     */
    void onContinuousSendTimer();

private:
    /**
     * @brief 设置信号槽连接
     * @details 连接UI控件的信号到相应的槽函数
     */
    void setupConnections();

    // updateControlState函数已在public部分声明为override函数

    /**
     * @brief 提取频率数值
     * @param text 包含频率信息的文本字符串
     * @return int 提取出的频率数值（毫秒）
     * @details 从频率文本中提取数值，用于定时器设置
     */
    int extractFrequencyValue(const QString &text);

    // appendLog, startCommunication, stopCommunication函数已在public部分声明为override函数



    /**
     * @brief 获取仲裁域波特率配置值
     * @param baudText 波特率文本（如"500kbps 87.5%"）
     * @return UINT 对应的硬件配置值
     * @details 将用户选择的仲裁域波特率转换为硬件配置值
     */
    UINT getArbitrationBaudConfig(const QString &baudText);

    /**
     * @brief 获取数据域波特率配置值
     * @param baudText 波特率文本（如"1Mbps 87.5%"）
     * @return UINT 对应的硬件配置值
     * @details 将用户选择的数据域波特率转换为硬件配置值
     */
    UINT getDataBaudConfig(const QString &baudText);

    // === UI界面成员变量 ===
    /**
     * @brief UI界面指针
     * @details 指向Qt Designer生成的UI类实例
     */
    Ui::CANFDInterface *ui;

    // === 定时器成员变量 ===
    /**
     * @brief 心跳定时器
     * @details 用于定时发送心跳数据帧
     */
    QTimer *heartbeatTimer;

    /**
     * @brief 接收定时器
     * @details 用于定时检查和处理接收到的数据
     */
    QTimer *receiveTimer;

    /**
     * @brief 连续发送定时器
     * @details 用于定时执行连续发送操作，支持长短帧交替
     */
    QTimer *continuousSendTimer;

    // 状态变量、配置数据、ZLGCAN相关成员变量已在BaseCANInterface基类中定义
};

#endif // CANFDINTERFACE_H
