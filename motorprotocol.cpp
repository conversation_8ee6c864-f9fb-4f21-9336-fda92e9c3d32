/**
 * @file motorprotocol.cpp
 * @brief 六电机控制系统CANFD协议工具函数实现
 * @details 实现了协议相关的工具函数，包括CRC校验、时间戳生成、
 *          错误描述获取等功能。
 * <AUTHOR>
 * @date 2025-01-28
 * @version 1.0
 */

#include "motorprotocol.h"
#include <QDateTime>
#include <QObject>
#include <cstring>  // for memset, memcmp

// ================================
// CRC16校验实现
// ================================

/// CRC16多项式 (CRC-16-CCITT)
static const quint16 CRC16_POLY = 0x1021;

/**
 * @brief CRC16校验计算
 * @param data 数据指针
 * @param length 数据长度
 * @return CRC16校验值
 * @details 使用CRC-16-CCITT算法计算校验值
 */
quint16 calculateCRC16(const quint8* data, quint32 length)
{
    if (!data || length == 0) {
        return 0;
    }
    
    quint16 crc = 0xFFFF;  // 初始值
    
    for (quint32 i = 0; i < length; i++) {
        crc ^= (quint16)(data[i] << 8);
        
        for (int j = 0; j < 8; j++) {
            if (crc & 0x8000) {
                crc = (crc << 1) ^ CRC16_POLY;
            } else {
                crc <<= 1;
            }
        }
    }
    
    return crc;
}

/**
 * @brief 验证CRC16校验
 * @param data 数据指针
 * @param length 数据长度
 * @param expected_crc 期望的CRC值
 * @return 校验是否正确
 */
bool verifyCRC16(const quint8* data, quint32 length, quint16 expected_crc)
{
    quint16 calculated_crc = calculateCRC16(data, length);
    return (calculated_crc == expected_crc);
}

// ================================
// 时间戳相关函数
// ================================

/**
 * @brief 获取当前时间戳(毫秒)
 * @return 时间戳低16位
 * @details 返回当前时间的毫秒数的低16位，用于帧时间戳
 */
quint16 getCurrentTimestamp()
{
    qint64 msecs = QDateTime::currentMSecsSinceEpoch();
    return static_cast<quint16>(msecs & 0xFFFF);
}

// ================================
// 描述字符串获取函数
// ================================

/**
 * @brief 获取错误描述字符串
 * @param error_code 错误代码
 * @return 错误描述
 */
QString getErrorDescription(ErrorCode error_code)
{
    switch (error_code) {
        case ERR_NO_ERROR:          return QObject::tr("无错误");
        
        // 通信错误
        case ERR_COMM_TIMEOUT:      return QObject::tr("通信超时");
        case ERR_CHECKSUM_FAIL:     return QObject::tr("校验和错误");
        case ERR_INVALID_CMD:       return QObject::tr("无效命令");
        case ERR_INVALID_PARAM:     return QObject::tr("无效参数");
        case ERR_SEQUENCE_ERROR:    return QObject::tr("序列号错误");
        
        // 电机硬件错误
        case ERR_MOTOR_FAULT:       return QObject::tr("电机故障");
        case ERR_OVERCURRENT:       return QObject::tr("过流保护");
        case ERR_OVERVOLTAGE:       return QObject::tr("过压保护");
        case ERR_UNDERVOLTAGE:      return QObject::tr("欠压保护");
        case ERR_OVERTEMP:          return QObject::tr("过温保护");
        case ERR_ENCODER_FAULT:     return QObject::tr("编码器故障");
        case ERR_HALL_FAULT:        return QObject::tr("霍尔传感器故障");
        
        // 运动控制错误
        case ERR_POSITION_LIMIT:    return QObject::tr("位置限制");
        case ERR_SPEED_LIMIT:       return QObject::tr("速度限制");
        case ERR_FOLLOWING_ERROR:   return QObject::tr("跟随误差过大");
        case ERR_HOME_TIMEOUT:      return QObject::tr("回零超时");
        case ERR_NOT_HOMED:         return QObject::tr("未回零");
        
        // 系统错误
        case ERR_SYSTEM_FAULT:      return QObject::tr("系统故障");
        case ERR_EMERGENCY_STOP:    return QObject::tr("紧急停止");
        case ERR_POWER_FAULT:       return QObject::tr("电源故障");
        case ERR_WATCHDOG_RESET:    return QObject::tr("看门狗复位");
        
        default:                    return QObject::tr("未知错误 (0x%1)").arg(error_code, 2, 16, QChar('0'));
    }
}

/**
 * @brief 获取命令描述字符串
 * @param command 命令类型
 * @return 命令描述
 */
QString getCommandDescription(CommandType command)
{
    switch (command) {
        case CMD_STOP:              return QObject::tr("停止电机");
        case CMD_START:             return QObject::tr("启动电机");
        case CMD_RESET:             return QObject::tr("复位控制器");
        case CMD_ENABLE:            return QObject::tr("使能电机");
        case CMD_DISABLE:           return QObject::tr("失能电机");
        case CMD_SET_SPEED:         return QObject::tr("设置速度");
        case CMD_SET_POSITION:      return QObject::tr("设置位置");
        case CMD_JOG_FORWARD:       return QObject::tr("正向点动");
        case CMD_JOG_REVERSE:       return QObject::tr("反向点动");
        case CMD_HOME:              return QObject::tr("回零操作");
        case CMD_CLEAR_ERROR:       return QObject::tr("清除错误");
        default:                    return QObject::tr("未知命令 (0x%1)").arg(command, 2, 16, QChar('0'));
    }
}

/**
 * @brief 获取控制模式描述字符串
 * @param mode 控制模式
 * @return 模式描述
 */
QString getControlModeDescription(ControlMode mode)
{
    switch (mode) {
        case MODE_POSITION:         return QObject::tr("位置控制");
        case MODE_SPEED:            return QObject::tr("速度控制");
        case MODE_TORQUE:           return QObject::tr("转矩控制");
        case MODE_PROFILE_POS:      return QObject::tr("轮廓位置");
        case MODE_PROFILE_VEL:      return QObject::tr("轮廓速度");
        case MODE_HOMING:           return QObject::tr("回零模式");
        default:                    return QObject::tr("未知模式 (0x%1)").arg(mode, 2, 16, QChar('0'));
    }
}

// ================================
// 数据帧操作辅助函数
// ================================

/**
 * @brief 填充电机控制命令帧的校验和
 * @param frame 控制命令帧指针
 * @details 计算并填充帧的CRC16校验和
 */
void fillControlFrameChecksum(MotorControlFrame* frame)
{
    if (!frame) return;
    
    // 先清零校验和字段
    frame->checksum = 0;
    
    // 计算除校验和字段外的所有数据的CRC
    quint16 crc = calculateCRC16(reinterpret_cast<const quint8*>(frame), 
                                 sizeof(MotorControlFrame) - sizeof(frame->checksum));
    frame->checksum = crc;
}

/**
 * @brief 验证电机控制命令帧的校验和
 * @param frame 控制命令帧指针
 * @return 校验是否正确
 */
bool verifyControlFrameChecksum(const MotorControlFrame* frame)
{
    if (!frame) return false;
    
    quint16 expected_crc = frame->checksum;
    
    // 创建临时副本用于校验
    MotorControlFrame temp_frame = *frame;
    temp_frame.checksum = 0;
    
    quint16 calculated_crc = calculateCRC16(reinterpret_cast<const quint8*>(&temp_frame), 
                                           sizeof(MotorControlFrame) - sizeof(temp_frame.checksum));
    
    return (calculated_crc == expected_crc);
}

/**
 * @brief 填充电机状态反馈帧的校验和
 * @param frame 状态反馈帧指针
 */
void fillStatusFrameChecksum(MotorStatusFrame* frame)
{
    if (!frame) return;
    
    frame->checksum = 0;
    quint16 crc = calculateCRC16(reinterpret_cast<const quint8*>(frame), 
                                 sizeof(MotorStatusFrame) - sizeof(frame->checksum));
    frame->checksum = crc;
}

/**
 * @brief 验证电机状态反馈帧的校验和
 * @param frame 状态反馈帧指针
 * @return 校验是否正确
 */
bool verifyStatusFrameChecksum(const MotorStatusFrame* frame)
{
    if (!frame) return false;
    
    quint16 expected_crc = frame->checksum;
    
    MotorStatusFrame temp_frame = *frame;
    temp_frame.checksum = 0;
    
    quint16 calculated_crc = calculateCRC16(reinterpret_cast<const quint8*>(&temp_frame), 
                                           sizeof(MotorStatusFrame) - sizeof(temp_frame.checksum));
    
    return (calculated_crc == expected_crc);
}

// ================================
// 数据转换辅助函数
// ================================

/**
 * @brief 将电机状态帧转换为字节数组
 * @param frame 状态帧
 * @return 字节数组
 */
QByteArray statusFrameToByteArray(const MotorStatusFrame& frame)
{
    return QByteArray(reinterpret_cast<const char*>(&frame), sizeof(MotorStatusFrame));
}

/**
 * @brief 将字节数组转换为电机状态帧
 * @param data 字节数组
 * @param frame 输出的状态帧
 * @return 转换是否成功
 */
bool byteArrayToStatusFrame(const QByteArray& data, MotorStatusFrame& frame)
{
    if (data.size() != sizeof(MotorStatusFrame)) {
        return false;
    }
    
    memcpy(&frame, data.constData(), sizeof(MotorStatusFrame));
    return verifyStatusFrameChecksum(&frame);
}

/**
 * @brief 将控制命令帧转换为字节数组
 * @param frame 控制帧
 * @return 字节数组
 */
QByteArray controlFrameToByteArray(const MotorControlFrame& frame)
{
    return QByteArray(reinterpret_cast<const char*>(&frame), sizeof(MotorControlFrame));
}

/**
 * @brief 将字节数组转换为控制命令帧
 * @param data 字节数组
 * @param frame 输出的控制帧
 * @return 转换是否成功
 */
bool byteArrayToControlFrame(const QByteArray& data, MotorControlFrame& frame)
{
    if (data.size() != sizeof(MotorControlFrame)) {
        return false;
    }
    
    memcpy(&frame, data.constData(), sizeof(MotorControlFrame));
    return verifyControlFrameChecksum(&frame);
}

// ================================
// 调试输出函数
// ================================

/**
 * @brief 打印控制命令帧信息（调试用）
 * @param frame 控制命令帧
 * @return 格式化的字符串
 */
QString debugControlFrame(const MotorControlFrame& frame)
{
    QString result;
    result += QString("=== 电机控制命令帧 ===\n");
    result += QString("电机ID: %1\n").arg(frame.motor_id);
    result += QString("序列号: %1\n").arg(frame.sequence);
    result += QString("命令: %1\n").arg(getCommandDescription(static_cast<CommandType>(frame.command)));
    result += QString("控制模式: %1\n").arg(getControlModeDescription(static_cast<ControlMode>(frame.control_mode)));
    result += QString("目标速度: %1 RPM\n").arg(frame.target_speed);
    result += QString("目标位置: %1\n").arg(frame.target_position);
    result += QString("加速度: %1 RPM/s\n").arg(frame.acceleration);
    result += QString("减速度: %1 RPM/s\n").arg(frame.deceleration);
    result += QString("校验和: 0x%1\n").arg(frame.checksum, 4, 16, QChar('0'));
    result += QString("时间戳: %1\n").arg(frame.timestamp);
    
    return result;
}

/**
 * @brief 打印状态反馈帧信息（调试用）
 * @param frame 状态反馈帧
 * @return 格式化的字符串
 */
QString debugStatusFrame(const MotorStatusFrame& frame)
{
    QString result;
    result += QString("=== 电机状态反馈帧 ===\n");
    result += QString("电机ID: %1\n").arg(frame.motor_id);
    result += QString("序列号: %1\n").arg(frame.sequence);
    result += QString("状态标志: 0x%1\n").arg(frame.status_flags, 2, 16, QChar('0'));
    result += QString("当前速度: %1 RPM\n").arg(frame.current_speed);
    result += QString("当前位置: %1\n").arg(frame.current_position);
    result += QString("电机电流: %1 mA\n").arg(frame.motor_current);
    result += QString("电机电压: %1 mV\n").arg(frame.motor_voltage);
    result += QString("温度: %1 °C\n").arg(frame.temperature / 10.0);
    result += QString("转矩: %1 Nm\n").arg(frame.torque / 10.0);
    result += QString("编码器位置: %1\n").arg(frame.encoder_position);
    result += QString("跟随误差: %1\n").arg(frame.following_error);
    result += QString("负载率: %1%%\n").arg(frame.load_ratio / 10.0);
    result += QString("控制模式: %1\n").arg(getControlModeDescription(static_cast<ControlMode>(frame.control_mode)));
    result += QString("错误代码: %1\n").arg(getErrorDescription(static_cast<ErrorCode>(frame.error_code)));
    result += QString("警告代码: 0x%1\n").arg(frame.warning_code, 4, 16, QChar('0'));
    result += QString("运行时间: %1 秒\n").arg(frame.runtime);
    result += QString("校验和: 0x%1\n").arg(frame.checksum, 4, 16, QChar('0'));
    result += QString("时间戳: %1\n").arg(frame.timestamp);
    
    return result;
}
