/**
 * @file oscilloscopeinterface.cpp
 * @brief 虚拟示波器接口类实现文件
 * @details 实现了虚拟示波器的所有功能，包括波形绘制、数据管理、模拟数据生成等
 * <AUTHOR>
 * @date 2025-07-03
 * @version 1.0
 */

#include "oscilloscopeinterface.h"
#include "ui_oscilloscopeinterface.h"
#include <QMouseEvent>
#include <QWheelEvent>

// PlotWidget类实现
PlotWidget::PlotWidget(QWidget *parent) : QWidget(parent), oscilloscope(nullptr),
    isDragging(false), timeOffset(0.0), zoomFactor(1.0)
{
    setMinimumSize(600, 400);
    setAttribute(Qt::WA_OpaquePaintEvent);
    setMouseTracking(true);  // 启用鼠标跟踪
}

void PlotWidget::setOscilloscopeInterface(OscilloscopeInterface *interface)
{
    oscilloscope = interface;
}

void PlotWidget::paintEvent(QPaintEvent *event)
{
    Q_UNUSED(event)

    if (!oscilloscope) return;

    QPainter painter(this);
    painter.setRenderHint(QPainter::Antialiasing, true);

    // 获取绘制区域 - 为坐标轴标签留出足够空间
    QRect drawRect = rect().adjusted(80, 25, -180, -50);

    // 清除背景 - 改为白色背景
    painter.fillRect(rect(), QColor(255, 255, 255));

    // 绘制网格
    oscilloscope->drawGrid(&painter, drawRect);

    // 绘制波形
    oscilloscope->drawAllChannels(&painter, drawRect);

    // 绘制通道信息
    oscilloscope->drawChannelInfo(&painter, drawRect);
}

void PlotWidget::focusInEvent(QFocusEvent *event)
{
    QWidget::focusInEvent(event);
    // 窗口重新获得焦点时强制重绘
    update();
}

void PlotWidget::showEvent(QShowEvent *event)
{
    QWidget::showEvent(event);
    // 窗口显示时强制重绘
    update();
}

void PlotWidget::mousePressEvent(QMouseEvent *event)
{
    if (event->button() == Qt::LeftButton) {
        isDragging = true;
        lastMousePos = event->pos();
        setCursor(Qt::ClosedHandCursor);
    }
    QWidget::mousePressEvent(event);
}

void PlotWidget::mouseMoveEvent(QMouseEvent *event)
{
    if (isDragging && oscilloscope) {
        QPoint delta = event->pos() - lastMousePos;

        // 计算时间偏移（水平拖动）
        double timeWindow = oscilloscope->getTimeScale() * 10.0; // 10个时间格
        double timeDelta = (delta.x() / (double)width()) * timeWindow;
        timeOffset -= timeDelta; // 向右拖动显示更早的数据

        // 更新示波器的时间偏移
        oscilloscope->setTimeOffset(timeOffset);

        lastMousePos = event->pos();
        update(); // 触发重绘
    }
    QWidget::mouseMoveEvent(event);
}

void PlotWidget::mouseReleaseEvent(QMouseEvent *event)
{
    if (event->button() == Qt::LeftButton) {
        isDragging = false;
        setCursor(Qt::ArrowCursor);
    }
    QWidget::mouseReleaseEvent(event);
}

void PlotWidget::wheelEvent(QWheelEvent *event)
{
    if (oscilloscope) {
        // 获取滚轮滚动量
        int delta = event->angleDelta().y();

        // 计算缩放因子变化
        double scaleFactor = 1.0 + (delta > 0 ? 0.1 : -0.1);
        zoomFactor *= scaleFactor;

        // 限制缩放范围
        if (zoomFactor < 0.1) zoomFactor = 0.1;
        if (zoomFactor > 10.0) zoomFactor = 10.0;

        // 更新示波器的时间刻度
        double newTimeScale = oscilloscope->getBaseTimeScale() / zoomFactor;
        oscilloscope->setTimeScale(newTimeScale);

        update(); // 触发重绘
    }
    QWidget::wheelEvent(event);
}
#include <QtMath>
#include <QRandomGenerator>
#include <QDebug>

/**
 * @brief 构造函数实现
 * @param parent 父窗口指针
 * @details 初始化所有组件和参数
 */
OscilloscopeInterface::OscilloscopeInterface(QWidget *parent)
    : QWidget(parent)
    , ui(new Ui::OscilloscopeInterface)
    , simulationTimer(new QTimer(this))
    , updateTimer(new QTimer(this))
    , currentDataSource(DataSourceType::SIMULATION)
    , isConnected(false)  // 初始化连接状态为未连接
    , timeScale(0.1)  // 100ms/div
    , baseTimeScale(0.1)  // 基础时间刻度
    , timeOffset(0.0)     // 时间偏移
    , sampleRate(100)  // 100Hz
    , updateRate(20)   // 20Hz更新频率
    , isPaused(false)
    , maxDataPoints(1000)
    , triggerMode(TriggerMode::AUTO)
    , triggerLevel(0.0)
    , simulationTime(0.0)
    , motorSpeed(1500.0)  // 1500 RPM
    , motorLoad(0.5)      // 50% 负载
    , gridDivisions(10)
    , m_deviceManager(nullptr)
    , m_currentDataSource(DataSourceType::SIMULATION)
    , m_useRealData(false)
{
    ui->setupUi(this);

    // 获取设备管理器实例
    m_deviceManager = CANFDDeviceManager::getInstance();

    // 连接设备管理器信号槽
    connect(m_deviceManager, &CANFDDeviceManager::motorStatusReceived,
            this, &OscilloscopeInterface::onMotorStatusReceived);
    connect(m_deviceManager, &CANFDDeviceManager::connectionStatusChanged,
            this, &OscilloscopeInterface::onConnectionStatusChanged);

    // 初始化各个组件
    initializeUI();
    initializeChannels();
    setupConnections();
    
    // 设置定时器
    simulationTimer->setInterval(1000 / sampleRate);  // 根据采样率设置间隔
    updateTimer->setInterval(1000 / updateRate);  // 根据更新频率设置间隔
    
    // 启动更新定时器
    updateTimer->start();
    
    // 更新数据源状态
    updateDataSourceStatus(currentDataSource);
}

/**
 * @brief 析构函数实现
 * @details 清理资源
 */
OscilloscopeInterface::~OscilloscopeInterface()
{
    // 停止所有定时器
    if (simulationTimer->isActive()) {
        simulationTimer->stop();
    }
    if (updateTimer->isActive()) {
        updateTimer->stop();
    }
    
    delete ui;
}

/**
 * @brief 初始化用户界面
 * @details 设置UI组件的初始状态
 */
void OscilloscopeInterface::initializeUI()
{
    // 设置时间刻度下拉框的当前值
    ui->timeScaleComboBox->setCurrentText("100ms/div");
    
    // 设置采样率
    ui->sampleRateSpinBox->setValue(sampleRate);
    
    // 设置触发模式
    ui->triggerModeComboBox->setCurrentText("自动");
    
    // 设置触发电平
    ui->triggerLevelSpinBox->setValue(triggerLevel);
    
    // 设置状态标签
    ui->connectionStatusLabel->setText("连接状态: 未连接");
    ui->samplingRateLabel->setText(QString("采样率: %1 Hz").arg(sampleRate));
    ui->dataCountLabel->setText("数据点: 0");

    // 设置按钮初始状态
    ui->startSimulationButton->setText("开始模拟");
    ui->startSimulationButton->setChecked(false);
    ui->pauseButton->setText("暂停");
    ui->pauseButton->setChecked(false);
    ui->clearButton->setEnabled(true);  // 初始状态下清除按钮可用
    
    // 设置波形显示区域的最小尺寸
    ui->plotWidget->setMinimumSize(600, 400);

    // 创建自定义绘图widget并替换原来的plotWidget
    PlotWidget *customPlotWidget = new PlotWidget(this);
    customPlotWidget->setOscilloscopeInterface(this);

    // 替换UI中的plotWidget
    QWidget *oldPlotWidget = ui->plotWidget;
    QLayout *layout = oldPlotWidget->parentWidget()->layout();
    if (layout) {
        layout->replaceWidget(oldPlotWidget, customPlotWidget);
        oldPlotWidget->deleteLater();
        ui->plotWidget = customPlotWidget;
    }

    // 设置默认选中的通道复选框
    ui->currentACheckBox->setChecked(true);
    ui->currentBCheckBox->setChecked(true);
    ui->currentCCheckBox->setChecked(false);
    ui->voltageCheckBox->setChecked(false);
    ui->speedCheckBox->setChecked(true);
    ui->torqueCheckBox->setChecked(false);
    ui->positionCheckBox->setChecked(false);
    ui->vibrationCheckBox->setChecked(false);
    ui->motorTempCheckBox->setChecked(false);
    ui->controllerTempCheckBox->setChecked(false);
}

/**
 * @brief 初始化通道数据
 * @details 设置所有电机数据通道的初始参数
 */
void OscilloscopeInterface::initializeChannels()
{
    channels.resize(static_cast<int>(MotorDataChannel::CHANNEL_COUNT));
    
    // 电气参数通道 - 适合白色背景的深色波形
    ChannelData &currentA = channels[static_cast<int>(MotorDataChannel::CURRENT_A)];
    currentA.channel = MotorDataChannel::CURRENT_A;
    currentA.name = "电流A相";
    currentA.unit = "A";
    currentA.color = QColor(220, 53, 69);  // 红色 #dc3545
    currentA.enabled = true;
    currentA.scale = 1.0;
    currentA.offset = 0.0;
    currentA.minValue = -15.0;  // 设置初始范围
    currentA.maxValue = 15.0;

    ChannelData &currentB = channels[static_cast<int>(MotorDataChannel::CURRENT_B)];
    currentB.channel = MotorDataChannel::CURRENT_B;
    currentB.name = "电流B相";
    currentB.unit = "A";
    currentB.color = QColor(40, 167, 69);  // 绿色 #28a745
    currentB.enabled = true;
    currentB.scale = 1.0;
    currentB.offset = 0.0;
    currentB.minValue = -15.0;
    currentB.maxValue = 15.0;

    ChannelData &currentC = channels[static_cast<int>(MotorDataChannel::CURRENT_C)];
    currentC.channel = MotorDataChannel::CURRENT_C;
    currentC.name = "电流C相";
    currentC.unit = "A";
    currentC.color = QColor(0, 123, 255);  // 蓝色 #007bff
    currentC.enabled = true;
    currentC.scale = 1.0;
    currentC.offset = 0.0;

    ChannelData &voltageBus = channels[static_cast<int>(MotorDataChannel::VOLTAGE_BUS)];
    voltageBus.channel = MotorDataChannel::VOLTAGE_BUS;
    voltageBus.name = "母线电压";
    voltageBus.unit = "V";
    voltageBus.color = QColor(255, 193, 7);  // 黄色 #ffc107
    voltageBus.enabled = true;
    voltageBus.scale = 1.0;
    voltageBus.offset = 0.0;
    
    // 机械参数通道
    ChannelData &speed = channels[static_cast<int>(MotorDataChannel::SPEED)];
    speed.channel = MotorDataChannel::SPEED;
    speed.name = "转速";
    speed.unit = "RPM";
    speed.color = QColor(253, 126, 20);  // 橙色 #fd7e14
    speed.enabled = true;
    speed.scale = 1.0;
    speed.offset = 0.0;
    speed.minValue = 0.0;
    speed.maxValue = 3000.0;

    ChannelData &torque = channels[static_cast<int>(MotorDataChannel::TORQUE)];
    torque.channel = MotorDataChannel::TORQUE;
    torque.name = "转矩";
    torque.unit = "N·m";
    torque.color = QColor(32, 201, 151);  // 青绿色 #20c997
    torque.enabled = true;
    torque.scale = 1.0;
    torque.offset = 0.0;

    ChannelData &position = channels[static_cast<int>(MotorDataChannel::POSITION)];
    position.channel = MotorDataChannel::POSITION;
    position.name = "位置";
    position.unit = "°";
    position.color = QColor(23, 162, 184);  // 青色 #17a2b8
    position.enabled = false;
    position.scale = 1.0;
    position.offset = 0.0;

    ChannelData &vibration = channels[static_cast<int>(MotorDataChannel::VIBRATION)];
    vibration.channel = MotorDataChannel::VIBRATION;
    vibration.name = "振动";
    vibration.unit = "g";
    vibration.color = QColor(111, 66, 193);  // 紫色 #6f42c1
    vibration.enabled = false;
    vibration.scale = 1.0;
    vibration.offset = 0.0;
    
    // 温度参数通道
    ChannelData &motorTemp = channels[static_cast<int>(MotorDataChannel::MOTOR_TEMP)];
    motorTemp.channel = MotorDataChannel::MOTOR_TEMP;
    motorTemp.name = "电机温度";
    motorTemp.unit = "°C";
    motorTemp.color = QColor(220, 53, 69);  // 红色 #dc3545
    motorTemp.enabled = false;
    motorTemp.scale = 1.0;
    motorTemp.offset = 0.0;

    ChannelData &controllerTemp = channels[static_cast<int>(MotorDataChannel::CONTROLLER_TEMP)];
    controllerTemp.channel = MotorDataChannel::CONTROLLER_TEMP;
    controllerTemp.name = "控制器温度";
    controllerTemp.unit = "°C";
    controllerTemp.color = QColor(102, 16, 242);  // 靛蓝色 #6610f2
    controllerTemp.enabled = false;
    controllerTemp.scale = 1.0;
    controllerTemp.offset = 0.0;

    // 控制参数通道
    ChannelData &pwmDuty = channels[static_cast<int>(MotorDataChannel::PWM_DUTY)];
    pwmDuty.channel = MotorDataChannel::PWM_DUTY;
    pwmDuty.name = "PWM占空比";
    pwmDuty.unit = "%";
    pwmDuty.color = QColor(192, 192, 192);
    pwmDuty.enabled = false;
    pwmDuty.scale = 1.0;
    pwmDuty.offset = 0.0;

    ChannelData &frequency = channels[static_cast<int>(MotorDataChannel::FREQUENCY)];
    frequency.channel = MotorDataChannel::FREQUENCY;
    frequency.name = "频率";
    frequency.unit = "Hz";
    frequency.color = QColor(253, 126, 20);  // 橙色 #fd7e14
    frequency.enabled = false;
    frequency.scale = 1.0;
    frequency.offset = 0.0;

    ChannelData &efficiency = channels[static_cast<int>(MotorDataChannel::EFFICIENCY)];
    efficiency.channel = MotorDataChannel::EFFICIENCY;
    efficiency.name = "效率";
    efficiency.unit = "%";
    efficiency.color = QColor(40, 167, 69);  // 绿色 #28a745
    efficiency.enabled = false;
    efficiency.scale = 1.0;
    efficiency.offset = 0.0;
}

/**
 * @brief 设置信号槽连接
 * @details 连接UI组件的信号到相应的槽函数
 */
void OscilloscopeInterface::setupConnections()
{
    // 数据源控制按钮
    connect(ui->startSimulationButton, &QPushButton::clicked, this, &OscilloscopeInterface::startSimulation);
    
    // 通道控制复选框
    connect(ui->currentACheckBox, &QCheckBox::toggled, this, &OscilloscopeInterface::onChannelEnabledChanged);
    connect(ui->currentBCheckBox, &QCheckBox::toggled, this, &OscilloscopeInterface::onChannelEnabledChanged);
    connect(ui->currentCCheckBox, &QCheckBox::toggled, this, &OscilloscopeInterface::onChannelEnabledChanged);
    connect(ui->voltageCheckBox, &QCheckBox::toggled, this, &OscilloscopeInterface::onChannelEnabledChanged);
    connect(ui->speedCheckBox, &QCheckBox::toggled, this, &OscilloscopeInterface::onChannelEnabledChanged);
    connect(ui->torqueCheckBox, &QCheckBox::toggled, this, &OscilloscopeInterface::onChannelEnabledChanged);
    connect(ui->positionCheckBox, &QCheckBox::toggled, this, &OscilloscopeInterface::onChannelEnabledChanged);
    connect(ui->vibrationCheckBox, &QCheckBox::toggled, this, &OscilloscopeInterface::onChannelEnabledChanged);
    connect(ui->motorTempCheckBox, &QCheckBox::toggled, this, &OscilloscopeInterface::onChannelEnabledChanged);
    connect(ui->controllerTempCheckBox, &QCheckBox::toggled, this, &OscilloscopeInterface::onChannelEnabledChanged);
    
    // 时间轴和采样率控制
    connect(ui->timeScaleComboBox, QOverload<const QString &>::of(&QComboBox::currentTextChanged),
            this, &OscilloscopeInterface::onTimeScaleChanged);
    connect(ui->sampleRateSpinBox, QOverload<int>::of(&QSpinBox::valueChanged),
            this, &OscilloscopeInterface::onSampleRateChanged);
    connect(ui->updateRateSpinBox, QOverload<int>::of(&QSpinBox::valueChanged),
            this, &OscilloscopeInterface::onUpdateRateChanged);
    
    // 触发设置
    connect(ui->triggerModeComboBox, QOverload<const QString &>::of(&QComboBox::currentTextChanged),
            this, &OscilloscopeInterface::onTriggerModeChanged);
    
    // 控制按钮
    connect(ui->pauseButton, &QPushButton::clicked, this, &OscilloscopeInterface::togglePause);
    connect(ui->clearButton, &QPushButton::clicked, this, &OscilloscopeInterface::clearAllData);
    connect(ui->backButton, &QPushButton::clicked, this, &OscilloscopeInterface::onBackClicked);
    
    // 定时器连接
    connect(simulationTimer, &QTimer::timeout, this, &OscilloscopeInterface::generateSimulationData);
    connect(updateTimer, &QTimer::timeout, this, &OscilloscopeInterface::updateWaveforms);

    // 应用程序焦点事件连接 - 防止截图等操作导致波形消失
    connect(qApp, &QApplication::focusChanged, this, [this](QWidget *old, QWidget *now) {
        Q_UNUSED(old)
        if (now && now->window() == this->window()) {
            // 当应用程序重新获得焦点时，强制重绘波形
            QTimer::singleShot(100, this, [this]() {
                if (ui->plotWidget) {
                    ui->plotWidget->update();
                }
            });
        }
    });
}

/**
 * @brief 切换模拟数据生成状态
 * @details 启动或停止模拟数据定时器
 */
void OscilloscopeInterface::startSimulation()
{
    if (!simulationTimer->isActive()) {
        // 开始模拟
        simulationTime = 0.0;  // 重置模拟时间
        simulationTimer->start();
        updateTimer->start();  // 启动波形更新定时器
        ui->startSimulationButton->setChecked(true);
        ui->startSimulationButton->setText("停止模拟");
        isPaused = false;  // 重置暂停状态
        ui->pauseButton->setChecked(false);
        ui->pauseButton->setText("暂停");
        ui->clearButton->setEnabled(true);  // 确保清除按钮可用
        updateDataSourceStatus(DataSourceType::SIMULATION);
    } else {
        // 停止模拟
        simulationTimer->stop();
        updateTimer->stop();
        ui->startSimulationButton->setChecked(false);
        ui->startSimulationButton->setText("开始模拟");
        isPaused = false;  // 重置暂停状态
        ui->pauseButton->setChecked(false);
        ui->pauseButton->setText("暂停");
        ui->clearButton->setEnabled(true);  // 确保清除按钮可用

        qDebug() << "stopSimulation: isPaused=" << isPaused
                 << ", simulationActive=" << simulationTimer->isActive()
                 << ", clearButtonEnabled=" << ui->clearButton->isEnabled();
    }
}

/**
 * @brief 停止模拟数据生成
 * @details 停止模拟数据定时器（外部调用）
 */
void OscilloscopeInterface::stopSimulation()
{
    if (simulationTimer->isActive()) {
        simulationTimer->stop();
        updateTimer->stop();  // 停止波形更新定时器
        ui->startSimulationButton->setChecked(false);
        ui->startSimulationButton->setText("开始模拟");
        isPaused = false;  // 重置暂停状态
        ui->pauseButton->setChecked(false);
        ui->pauseButton->setText("暂停");
        ui->clearButton->setEnabled(true);  // 确保清除按钮可用
    }
}

/**
 * @brief 暂停/恢复数据显示
 * @details 暂停或恢复波形的实时更新显示，同时控制清除按钮的可用状态
 */
void OscilloscopeInterface::togglePause()
{
    isPaused = !isPaused;
    ui->pauseButton->setText(isPaused ? "恢复" : "暂停");
    ui->pauseButton->setChecked(isPaused);

    // 控制清除按钮的可用状态
    // 只有在暂停状态下才禁用清除按钮
    // 如果模拟已停止，则无论暂停状态如何都应该可用
    bool shouldEnableClear = !isPaused || !simulationTimer->isActive();
    ui->clearButton->setEnabled(shouldEnableClear);

    qDebug() << "togglePause: isPaused=" << isPaused
             << ", simulationActive=" << simulationTimer->isActive()
             << ", clearButtonEnabled=" << shouldEnableClear;
}

/**
 * @brief 清除所有波形数据
 * @details 清空所有通道的数据缓冲区，在任何状态下都可以执行
 */
void OscilloscopeInterface::clearAllData()
{
    // 使用互斥锁保护数据访问
    QMutexLocker locker(&dataMutex);

    // 清空所有通道数据
    for (auto &channel : channels) {
        channel.dataBuffer.clear();
        channel.minValue = 0.0;
        channel.maxValue = 0.0;
        channel.currentValue = 0.0;
    }

    // 重置模拟时间（如果正在模拟）
    if (simulationTimer->isActive()) {
        simulationTime = 0.0;
    }

    // 更新UI显示
    ui->dataCountLabel->setText("数据点: 0");

    // 强制重绘界面
    update();
}

/**
 * @brief 通道启用状态改变槽函数
 * @details 响应通道复选框的状态改变
 */
void OscilloscopeInterface::onChannelEnabledChanged()
{
    // 更新通道启用状态
    channels[static_cast<int>(MotorDataChannel::CURRENT_A)].enabled = ui->currentACheckBox->isChecked();
    channels[static_cast<int>(MotorDataChannel::CURRENT_B)].enabled = ui->currentBCheckBox->isChecked();
    channels[static_cast<int>(MotorDataChannel::CURRENT_C)].enabled = ui->currentCCheckBox->isChecked();
    channels[static_cast<int>(MotorDataChannel::VOLTAGE_BUS)].enabled = ui->voltageCheckBox->isChecked();
    channels[static_cast<int>(MotorDataChannel::SPEED)].enabled = ui->speedCheckBox->isChecked();
    channels[static_cast<int>(MotorDataChannel::TORQUE)].enabled = ui->torqueCheckBox->isChecked();
    channels[static_cast<int>(MotorDataChannel::POSITION)].enabled = ui->positionCheckBox->isChecked();
    channels[static_cast<int>(MotorDataChannel::VIBRATION)].enabled = ui->vibrationCheckBox->isChecked();
    channels[static_cast<int>(MotorDataChannel::MOTOR_TEMP)].enabled = ui->motorTempCheckBox->isChecked();
    channels[static_cast<int>(MotorDataChannel::CONTROLLER_TEMP)].enabled = ui->controllerTempCheckBox->isChecked();
}

/**
 * @brief 时间刻度改变槽函数
 * @details 响应时间刻度下拉框的改变
 */
void OscilloscopeInterface::onTimeScaleChanged()
{
    QString scaleText = ui->timeScaleComboBox->currentText();
    
    if (scaleText == "1s/div") {
        timeScale = 1.0;
    } else if (scaleText == "500ms/div") {
        timeScale = 0.5;
    } else if (scaleText == "200ms/div") {
        timeScale = 0.2;
    } else if (scaleText == "100ms/div") {
        timeScale = 0.1;
    } else if (scaleText == "50ms/div") {
        timeScale = 0.05;
    }
}

/**
 * @brief 采样率改变槽函数
 * @details 响应采样率设置的改变
 */
void OscilloscopeInterface::onSampleRateChanged()
{
    sampleRate = ui->sampleRateSpinBox->value();
    simulationTimer->setInterval(1000 / sampleRate);
    ui->samplingRateLabel->setText(QString("采样率: %1 Hz").arg(sampleRate));
}

void OscilloscopeInterface::onUpdateRateChanged()
{
    updateRate = ui->updateRateSpinBox->value();

    // 更新波形显示定时器间隔
    updateTimer->setInterval(1000 / updateRate);
}

/**
 * @brief 触发模式改变槽函数
 * @details 响应触发模式设置的改变
 */
void OscilloscopeInterface::onTriggerModeChanged()
{
    QString modeText = ui->triggerModeComboBox->currentText();
    
    if (modeText == "自动") {
        triggerMode = TriggerMode::AUTO;
    } else if (modeText == "正常") {
        triggerMode = TriggerMode::NORMAL;
    } else if (modeText == "单次") {
        triggerMode = TriggerMode::SINGLE;
    }
}

/**
 * @brief 返回主界面槽函数
 * @details 发送返回主界面的信号
 */
void OscilloscopeInterface::onBackClicked()
{
    // 停止所有活动
    stopSimulation();

    // 发送返回信号
    emit backToMainInterface();
}

/**
 * @brief 更新数据源状态显示
 * @param sourceType 当前数据源类型
 * @details 更新界面上的数据源状态指示
 */
void OscilloscopeInterface::updateDataSourceStatus(DataSourceType sourceType)
{
    currentDataSource = sourceType;

    QString statusText;
    QString statusColor;

    switch (sourceType) {
    case DataSourceType::SIMULATION:
        statusText = "当前数据源: 模拟数据";
        statusColor = "#ffa500";  // 橙色
        ui->connectionStatusLabel->setText("连接状态: 模拟模式");
        break;
    case DataSourceType::REAL_DATA:
        statusText = "当前数据源: 真实CANFD数据";
        statusColor = "#00ff00";  // 绿色
        ui->connectionStatusLabel->setText("连接状态: CANFD已连接");
        break;
    case DataSourceType::MIXED:
        statusText = "当前数据源: 混合数据";
        statusColor = "#ffff00";  // 黄色
        ui->connectionStatusLabel->setText("连接状态: 混合模式");
        break;
    }

    ui->dataSourceLabel->setText(statusText);
    ui->dataSourceLabel->setStyleSheet(QString("QLabel { color: %1; font-weight: bold; font-size: 12px; }").arg(statusColor));

    emit dataSourceChanged(sourceType);
}

/**
 * @brief 模拟数据生成定时器槽函数
 * @details 定时生成模拟的电机运行数据
 */
void OscilloscopeInterface::generateSimulationData()
{
    if (isPaused) return;

    double currentTime = simulationTime;
    simulationTime += 1.0 / sampleRate;  // 增加时间步长

    QMutexLocker locker(&dataMutex);

    // 为每个启用的通道生成数据
    for (int i = 0; i < channels.size(); ++i) {
        if (channels[i].enabled) {
            double value = generateChannelData(static_cast<MotorDataChannel>(i), currentTime);
            addDataPoint(static_cast<MotorDataChannel>(i), value, currentTime);
        }
    }

    // 更新数据点计数
    int totalDataPoints = 0;
    for (const auto &channel : channels) {
        if (channel.enabled) {
            totalDataPoints += channel.dataBuffer.size();
        }
    }
    ui->dataCountLabel->setText(QString("数据点: %1").arg(totalDataPoints));
}

/**
 * @brief 生成指定通道的模拟数据
 * @param channel 通道类型
 * @param timestamp 时间戳
 * @return 生成的数据值
 * @details 根据通道类型生成相应的模拟电机数据
 */
double OscilloscopeInterface::generateChannelData(MotorDataChannel channel, double timestamp)
{
    double value = 0.0;
    double frequency = 50.0;  // 基础频率 50Hz
    double amplitude = 1.0;
    double offset = 0.0;
    double noise = (QRandomGenerator::global()->generateDouble() - 0.5) * 0.1;  // 10%噪声

    switch (channel) {
    case MotorDataChannel::CURRENT_A:
        // A相电流：正弦波，幅值根据负载变化
        amplitude = 10.0 * motorLoad;  // 0-10A
        value = amplitude * qSin(2 * M_PI * frequency * timestamp) + noise;
        break;

    case MotorDataChannel::CURRENT_B:
        // B相电流：相位差120度
        amplitude = 10.0 * motorLoad;
        value = amplitude * qSin(2 * M_PI * frequency * timestamp - 2 * M_PI / 3) + noise;
        break;

    case MotorDataChannel::CURRENT_C:
        // C相电流：相位差240度
        amplitude = 10.0 * motorLoad;
        value = amplitude * qSin(2 * M_PI * frequency * timestamp - 4 * M_PI / 3) + noise;
        break;

    case MotorDataChannel::VOLTAGE_BUS:
        // 母线电压：相对稳定，有小幅波动
        offset = 400.0;  // 400V直流母线
        amplitude = 20.0;
        value = offset + amplitude * qSin(2 * M_PI * 5 * timestamp) + noise * 5;
        break;

    case MotorDataChannel::SPEED:
        // 转速：目标转速附近波动
        offset = motorSpeed;
        amplitude = 50.0;  // ±50 RPM波动
        value = offset + amplitude * qSin(2 * M_PI * 0.5 * timestamp) + noise * 10;
        break;

    case MotorDataChannel::TORQUE:
        // 转矩：与负载相关，有波动
        offset = 20.0 * motorLoad;  // 基础转矩
        amplitude = 5.0;
        value = offset + amplitude * qSin(2 * M_PI * 2 * timestamp) + noise * 2;
        break;

    case MotorDataChannel::POSITION:
        // 位置：连续增加（旋转）
        value = fmod(motorSpeed * timestamp / 60.0 * 360.0, 360.0);  // 转换为角度
        break;

    case MotorDataChannel::VIBRATION:
        // 振动：随机噪声为主
        amplitude = 0.5;
        value = amplitude * qSin(2 * M_PI * 100 * timestamp) + noise * 0.2;
        break;

    case MotorDataChannel::MOTOR_TEMP:
        // 电机温度：缓慢变化
        offset = 60.0;  // 基础温度60°C
        amplitude = 10.0;
        value = offset + amplitude * qSin(2 * M_PI * 0.01 * timestamp) + noise;
        break;

    case MotorDataChannel::CONTROLLER_TEMP:
        // 控制器温度：比电机温度稍高
        offset = 45.0;
        amplitude = 8.0;
        value = offset + amplitude * qSin(2 * M_PI * 0.02 * timestamp) + noise;
        break;

    case MotorDataChannel::PWM_DUTY:
        // PWM占空比：与负载相关
        offset = 50.0 * motorLoad;  // 基础占空比
        amplitude = 10.0;
        value = offset + amplitude * qSin(2 * M_PI * 1 * timestamp) + noise;
        value = qMax(0.0, qMin(100.0, value));  // 限制在0-100%
        break;

    case MotorDataChannel::FREQUENCY:
        // 频率：PWM开关频率
        offset = 10000.0;  // 10kHz
        amplitude = 500.0;
        value = offset + amplitude * qSin(2 * M_PI * 0.1 * timestamp) + noise * 50;
        break;

    case MotorDataChannel::EFFICIENCY:
        // 效率：与负载和转速相关
        offset = 85.0;  // 基础效率85%
        amplitude = 10.0;
        value = offset + amplitude * qSin(2 * M_PI * 0.3 * timestamp) + noise;
        value = qMax(0.0, qMin(100.0, value));  // 限制在0-100%
        break;

    default:
        value = 0.0;
        break;
    }

    return value;
}

/**
 * @brief 添加数据点到指定通道
 * @param channel 通道类型
 * @param value 数据值
 * @param timestamp 时间戳
 * @details 将新数据点添加到通道缓冲区，管理缓冲区大小
 */
void OscilloscopeInterface::addDataPoint(MotorDataChannel channel, double value, double timestamp)
{
    int channelIndex = static_cast<int>(channel);
    if (channelIndex < 0 || channelIndex >= channels.size()) return;

    ChannelData &channelData = channels[channelIndex];

    // 使用优化的环形缓冲区添加新数据点
    channelData.dataBuffer.push(Oscilloscope::DataPoint(timestamp, value, true));

    // 更新统计信息
    channelData.currentValue = value;
    if (channelData.dataBuffer.size() == 1) {
        channelData.minValue = channelData.maxValue = value;
    } else {
        channelData.minValue = qMin(channelData.minValue, value);
        channelData.maxValue = qMax(channelData.maxValue, value);
    }

    // 环形缓冲区自动管理大小，无需手动限制
}

/**
 * @brief 波形更新定时器槽函数
 * @details 定时更新波形显示，重绘界面
 */
void OscilloscopeInterface::updateWaveforms()
{
    if (!isPaused) {
        // 更新绘图区域
        plotArea = ui->plotWidget->rect().adjusted(50, 30, -30, -50);  // 留出边距用于标签

        // 优化重绘性能 - 只使用update()，避免强制立即重绘
        // repaint()会立即重绘，消耗大量CPU资源，update()会在下一个事件循环中重绘
        ui->plotWidget->update();

        // 限制刷新率，避免过度重绘
        static qint64 lastUpdateTime = 0;
        qint64 currentTime = QDateTime::currentMSecsSinceEpoch();
        if (currentTime - lastUpdateTime < 16) {  // 限制为60FPS (1000ms/60 ≈ 16ms)
            return;
        }
        lastUpdateTime = currentTime;
    }
}



/**
 * @brief 窗口大小改变事件处理函数
 * @param event 大小改变事件指针
 * @details 重写QWidget的resizeEvent，响应窗口大小改变
 */
void OscilloscopeInterface::resizeEvent(QResizeEvent *event)
{
    QWidget::resizeEvent(event);

    // 更新绘图区域
    if (ui->plotWidget) {
        plotArea = ui->plotWidget->rect().adjusted(50, 30, -30, -50);
    }
}

/**
 * @brief 绘制波形网格
 * @param painter 绘图对象指针
 * @param rect 绘制区域
 * @details 绘制示波器的背景网格线和坐标轴标签
 */
void OscilloscopeInterface::drawGrid(QPainter *painter, const QRect &rect)
{
    painter->save();

    // 设置网格线样式 - 适合白色背景的颜色
    QPen gridPen(QColor(220, 220, 220), 1, Qt::SolidLine);  // 浅灰色网格线
    QPen majorGridPen(QColor(180, 180, 180), 1, Qt::SolidLine);  // 深一点的主网格线

    // 绘制垂直网格线
    int verticalDivisions = gridDivisions;
    for (int i = 0; i <= verticalDivisions; ++i) {
        int x = rect.left() + (rect.width() * i) / verticalDivisions;

        if (i == 0 || i == verticalDivisions || i == verticalDivisions / 2) {
            painter->setPen(majorGridPen);
        } else {
            painter->setPen(gridPen);
        }

        painter->drawLine(x, rect.top(), x, rect.bottom());
    }

    // 绘制水平网格线
    int horizontalDivisions = 8;
    for (int i = 0; i <= horizontalDivisions; ++i) {
        int y = rect.top() + (rect.height() * i) / horizontalDivisions;

        if (i == 0 || i == horizontalDivisions || i == horizontalDivisions / 2) {
            painter->setPen(majorGridPen);
        } else {
            painter->setPen(gridPen);
        }

        painter->drawLine(rect.left(), y, rect.right(), y);
    }

    // 绘制边框 - 红色边框
    painter->setPen(QPen(QColor(220, 53, 69), 2));  // #dc3545
    painter->drawRect(rect);

    // 绘制坐标轴标签 - 深色文字
    painter->setPen(QColor(51, 51, 51));  // #333333
    QFont labelFont("Microsoft YaHei", 9);
    painter->setFont(labelFont);

    // 计算时间窗口和数值范围
    double timeWindow = timeScale * gridDivisions;
    double currentTime = simulationTime + timeOffset;
    double startTime = currentTime - timeWindow;

    // X轴标签 (时间轴) - 只显示关键刻度避免重叠
    for (int i = 0; i <= verticalDivisions; ++i) {
        int x = rect.left() + (rect.width() * i) / verticalDivisions;

        // 绘制刻度线
        if (i == 0 || i == verticalDivisions) {
            painter->drawLine(x, rect.bottom(), x, rect.bottom() + 8);
        } else {
            painter->drawLine(x, rect.bottom(), x, rect.bottom() + 4);
        }

        // 只在关键位置显示时间标签，避免重叠
        if (i == 0 || i == verticalDivisions/2 || i == verticalDivisions) {
            double timeValue = startTime + (timeWindow * i) / verticalDivisions;
            QString timeLabel;

            if (timeValue >= 1.0) {
                timeLabel = QString::number(timeValue, 'f', 1) + "s";
            } else if (timeValue >= 0.001) {
                timeLabel = QString::number(timeValue * 1000, 'f', 0) + "ms";
            } else {
                timeLabel = QString::number(timeValue * 1000000, 'f', 0) + "μs";
            }

            // 绘制时间标签，增加垂直间距
            QRect textRect(x - 35, rect.bottom() + 12, 70, 20);
            painter->drawText(textRect, Qt::AlignCenter, timeLabel);
        }
    }

    // Y轴标签 (数值轴) - 根据启用的通道动态显示
    QMutexLocker locker(&dataMutex);

    // 找到第一个启用的通道来确定Y轴范围
    const ChannelData* activeChannel = nullptr;
    for (const auto &channelData : channels) {
        if (channelData.enabled && !channelData.dataBuffer.empty()) {
            activeChannel = &channelData;
            break;
        }
    }

    if (activeChannel) {
        double valueRange = activeChannel->maxValue - activeChannel->minValue;
        if (valueRange < 0.001) valueRange = 20.0;

        for (int i = 0; i <= horizontalDivisions; ++i) {
            int y = rect.top() + (rect.height() * i) / horizontalDivisions;

            // 绘制刻度线
            if (i == 0 || i == horizontalDivisions) {
                painter->drawLine(rect.left() - 8, y, rect.left(), y);
            } else {
                painter->drawLine(rect.left() - 4, y, rect.left(), y);
            }

            // 计算数值标签 (Y轴从上到下，所以需要反转)
            double valueMid = (activeChannel->minValue + activeChannel->maxValue) / 2;
            double valueOffset = (valueRange / 2) * (1.0 - 2.0 * i / horizontalDivisions);
            double value = valueMid + valueOffset;

            QString valueLabel;
            if (abs(value) >= 1000) {
                valueLabel = QString::number(value / 1000, 'f', 1) + "k";
            } else if (abs(value) >= 1) {
                valueLabel = QString::number(value, 'f', 1);
            } else {
                valueLabel = QString::number(value, 'f', 3);
            }

            // 绘制数值标签
            QRect textRect(rect.left() - 65, y - 10, 55, 20);
            painter->drawText(textRect, Qt::AlignRight | Qt::AlignVCenter, valueLabel);
        }

        // 绘制Y轴单位标签
        QString unitLabel = getChannelUnit(static_cast<MotorDataChannel>(
            std::distance(channels.begin(),
                         std::find_if(channels.begin(), channels.end(),
                                    [activeChannel](const ChannelData& ch) {
                                        return &ch == activeChannel;
                                    }))));

        QRect unitRect(rect.left() - 65, rect.top() - 25, 55, 20);
        painter->drawText(unitRect, Qt::AlignCenter, unitLabel);
    } else {
        // 没有启用通道时显示默认标签
        for (int i = 0; i <= horizontalDivisions; ++i) {
            int y = rect.top() + (rect.height() * i) / horizontalDivisions;

            if (i == 0 || i == horizontalDivisions) {
                painter->drawLine(rect.left() - 8, y, rect.left(), y);
            } else {
                painter->drawLine(rect.left() - 4, y, rect.left(), y);
            }
        }
    }

    painter->restore();
}

/**
 * @brief 获取通道单位
 * @param channel 通道类型
 * @return 通道单位字符串
 * @details 返回指定通道的测量单位
 */
QString OscilloscopeInterface::getChannelUnit(MotorDataChannel channel) const
{
    switch (channel) {
    case MotorDataChannel::CURRENT_A:
    case MotorDataChannel::CURRENT_B:
    case MotorDataChannel::CURRENT_C:
        return "A";  // 安培
    case MotorDataChannel::VOLTAGE_BUS:
        return "V";  // 伏特
    case MotorDataChannel::SPEED:
        return "RPM";  // 转/分
    case MotorDataChannel::TORQUE:
        return "N·m";  // 牛顿米
    case MotorDataChannel::POSITION:
        return "°";  // 度
    case MotorDataChannel::VIBRATION:
        return "g";  // 重力加速度
    case MotorDataChannel::MOTOR_TEMP:
    case MotorDataChannel::CONTROLLER_TEMP:
        return "°C";  // 摄氏度
    case MotorDataChannel::PWM_DUTY:
    case MotorDataChannel::EFFICIENCY:
        return "%";  // 百分比
    case MotorDataChannel::FREQUENCY:
        return "Hz";  // 赫兹
    default:
        return "";
    }
}

/**
 * @brief 绘制所有启用的通道
 * @param painter 绘图对象指针
 * @param rect 绘制区域
 * @details 绘制所有启用通道的波形
 */
void OscilloscopeInterface::drawAllChannels(QPainter *painter, const QRect &rect)
{
    QMutexLocker locker(&dataMutex);

    int enabledChannels = 0;
    for (const auto &channelData : channels) {
        if (channelData.enabled) {
            enabledChannels++;
            if (!channelData.dataBuffer.empty()) {
                drawChannelWaveform(painter, rect, channelData);
            }
        }
    }

    // 如果没有启用的通道，显示提示信息
    if (enabledChannels == 0) {
        painter->setPen(QColor(108, 117, 125));  // 灰色文字 #6c757d
        QFont font = painter->font();
        font.setPointSize(16);
        painter->setFont(font);
        painter->drawText(rect, Qt::AlignCenter, "请启用至少一个通道");
    }
}

/**
 * @brief 绘制通道波形
 * @param painter 绘图对象指针
 * @param rect 绘制区域
 * @param channelData 通道数据
 * @details 绘制指定通道的波形曲线
 */
void OscilloscopeInterface::drawChannelWaveform(QPainter *painter, const QRect &rect, const ChannelData &channelData)
{
    if (channelData.dataBuffer.empty()) {
        return;
    }

    painter->save();

    // 设置画笔 - 使用更亮的颜色
    QColor brightColor = channelData.color.lighter(150);  // 增亮50%
    QPen wavePen(brightColor, 3, Qt::SolidLine);  // 增加线宽
    painter->setPen(wavePen);

    // 计算时间窗口（考虑时间偏移）
    double timeWindow = timeScale * gridDivisions;  // 总时间窗口
    double currentTime = simulationTime + timeOffset;  // 应用时间偏移
    double startTime = currentTime - timeWindow;

    // 计算垂直缩放 - 充分利用显示区域
    double valueRange = channelData.maxValue - channelData.minValue;
    if (valueRange < 0.001) valueRange = 20.0;  // 设置默认范围

    // 使用90%的高度，留出上下边距
    double verticalScale = rect.height() * 0.9 / valueRange;
    double verticalCenter = rect.center().y();

    // 绘制波形
    QVector<QPointF> points;
    int validPoints = 0;

    // 使用索引循环替代范围for循环，避免迭代器问题
    for (size_t i = 0; i < channelData.dataBuffer.size(); ++i) {
        const Oscilloscope::DataPoint &dataPoint = channelData.dataBuffer[i];

        // 只显示在当前时间窗口内的数据点
        if (dataPoint.timestamp >= startTime && dataPoint.timestamp <= currentTime) {
            // 计算X坐标（时间轴）
            double timeRatio = (dataPoint.timestamp - startTime) / timeWindow;
            // 确保时间比例在有效范围内
            timeRatio = qMax(0.0, qMin(1.0, timeRatio));

            int x = rect.left() + static_cast<int>(timeRatio * rect.width());

            // 计算Y坐标（数值轴）- 改进算法确保波形居中显示
            double valueMid = (channelData.minValue + channelData.maxValue) / 2;
            double valueOffset = dataPoint.value - valueMid;
            int y = verticalCenter - static_cast<int>(valueOffset * verticalScale);

            // 确保Y坐标在绘制区域内，留出5%的边距
            int topMargin = rect.top() + rect.height() * 0.05;
            int bottomMargin = rect.bottom() - rect.height() * 0.05;
            y = qMax(topMargin, qMin(bottomMargin, y));

            points.append(QPointF(x, y));
            validPoints++;
        }
    }

    // 绘制连线
    if (points.size() > 1) {
        for (int i = 1; i < points.size(); ++i) {
            painter->drawLine(points[i-1], points[i]);
        }
    } else if (points.size() == 1) {
        // 如果只有一个点，绘制一个小圆点
        painter->drawEllipse(points[0], 3, 3);
    }

    painter->restore();
}

/**
 * @brief 绘制通道信息
 * @param painter 绘图对象指针
 * @param rect 绘制区域
 * @details 绘制通道名称、当前值等信息
 */
void OscilloscopeInterface::drawChannelInfo(QPainter *painter, const QRect &rect)
{
    painter->save();

    // 设置字体
    QFont font("Microsoft YaHei", 10);
    painter->setFont(font);

    int yOffset = 30;
    int lineHeight = 25;  // 增加行高
    int sectionSpacing = 15;  // 段落间距

    // 设置右侧信息区域的字体 - 更大更清晰
    QFont infoFont("Microsoft YaHei UI", 12);  // 增大字体
    infoFont.setBold(true);
    painter->setFont(infoFont);

    int rightPanelX = rect.right() + 20;  // 右侧面板起始X位置，稍微右移

    // 系统信息标题 - 更大更醒目
    QFont titleFont("Microsoft YaHei UI", 14);
    titleFont.setBold(true);
    painter->setFont(titleFont);
    painter->setPen(QColor(220, 53, 69));  // 红色标题
    painter->drawText(rightPanelX, rect.top() + yOffset, "📊 系统信息");
    yOffset += lineHeight + 5;

    // 恢复信息字体
    painter->setFont(infoFont);
    painter->setPen(QColor(51, 51, 51));  // 深色文字
    // 显示更新频率
    painter->drawText(rightPanelX, rect.top() + yOffset, QString("🔄 更新频率: %1 Hz").arg(updateRate));
    yOffset += lineHeight;

    // 显示采样率
    painter->drawText(rightPanelX, rect.top() + yOffset, QString("📊 采样率: %1 Hz").arg(sampleRate));
    yOffset += lineHeight;

    // 显示时间刻度
    painter->drawText(rightPanelX, rect.top() + yOffset, QString("⏱️ 时间刻度: %1 s/div").arg(timeScale, 0, 'f', 3));
    yOffset += lineHeight;

    // 显示时间范围
    double timeWindow = timeScale * gridDivisions;
    painter->drawText(rightPanelX, rect.top() + yOffset, QString("🕐 时间窗口: %1 s").arg(timeWindow, 0, 'f', 2));
    yOffset += lineHeight;

    // 显示电压范围
    painter->drawText(rightPanelX, rect.top() + yOffset, QString("⚡ 电压范围: ±5.0V"));
    yOffset += lineHeight;

    // 显示绘图区域尺寸
    painter->drawText(rightPanelX, rect.top() + yOffset, QString("📐 显示区域: %1×%2").arg(rect.width()).arg(rect.height()));
    yOffset += lineHeight + sectionSpacing;

    // 通道信息标题
    painter->setFont(titleFont);
    painter->setPen(QColor(220, 53, 69));  // 红色标题
    painter->drawText(rightPanelX, rect.top() + yOffset, "📈 通道信息");
    yOffset += lineHeight + 5;

    // 恢复信息字体
    painter->setFont(infoFont);

    // 绘制启用通道的信息
    for (const auto &channelData : channels) {
        if (channelData.enabled) {
            // 设置文字颜色为通道颜色，但确保在白色背景下可见
            painter->setPen(channelData.color);

            // 绘制通道名称和当前值 - 更清晰的格式
            QString infoText = QString("● %1: %2 %3")
                              .arg(channelData.name)
                              .arg(channelData.currentValue, 0, 'f', 2)
                              .arg(channelData.unit);

            painter->drawText(rightPanelX, rect.top() + yOffset, infoText);
            yOffset += lineHeight;
        }
    }

    yOffset += sectionSpacing;

    // 数据源信息标题
    painter->setFont(titleFont);
    painter->setPen(QColor(220, 53, 69));  // 红色标题
    painter->drawText(rightPanelX, rect.top() + yOffset, "🔗 数据源");
    yOffset += lineHeight + 5;

    // 恢复信息字体
    painter->setFont(infoFont);
    painter->setPen(QColor(51, 51, 51));  // 深色文字

    QString dataSourceText, dataSourceIcon;
    switch (currentDataSource) {
        case DataSourceType::SIMULATION:
            dataSourceText = "模拟数据";
            dataSourceIcon = "🎲";
            break;
        case DataSourceType::REAL_DATA:
            dataSourceText = "CANFD 实时数据";
            dataSourceIcon = "📡";
            break;
        case DataSourceType::MIXED:
            dataSourceText = "混合数据";
            dataSourceIcon = "🔀";
            break;
        default:
            dataSourceText = "无数据源";
            dataSourceIcon = "❌";
            break;
    }

    painter->drawText(rightPanelX, rect.top() + yOffset, QString("%1 %2").arg(dataSourceIcon).arg(dataSourceText));
    yOffset += lineHeight;

    // 添加连接状态信息
    painter->drawText(rightPanelX, rect.top() + yOffset, QString("🔌 连接状态: %1").arg(isConnected ? "已连接" : "未连接"));
    yOffset += lineHeight;

    // 添加数据点统计
    int totalDataPoints = 0;
    for (const auto &channelData : channels) {
        if (channelData.enabled) {
            totalDataPoints += channelData.dataBuffer.size();
        }
    }
    painter->drawText(rightPanelX, rect.top() + yOffset, QString("📊 数据点数: %1").arg(totalDataPoints));
    yOffset += lineHeight;

    // 添加运行时间
    painter->drawText(rightPanelX, rect.top() + yOffset, QString("⏰ 运行时间: %1 s").arg(simulationTime, 0, 'f', 1));

    painter->restore();
}

void OscilloscopeInterface::setTimeScale(double scale)
{
    QMutexLocker locker(&dataMutex);
    timeScale = scale;

    // 更新UI显示
    QString scaleText;
    if (scale >= 1.0) {
        scaleText = QString("%1s/div").arg(scale);
    } else {
        scaleText = QString("%1ms/div").arg(scale * 1000);
    }

    // 更新下拉框选择（如果匹配的话）
    int index = ui->timeScaleComboBox->findText(scaleText);
    if (index >= 0) {
        ui->timeScaleComboBox->setCurrentIndex(index);
    }
}

void OscilloscopeInterface::setTimeOffset(double offset)
{
    QMutexLocker locker(&dataMutex);
    timeOffset = offset;
}

// ================================
// 设备连接检测和数据源管理
// ================================

/**
 * @brief 检测设备连接并显示选择对话框
 */
bool OscilloscopeInterface::checkDeviceConnectionAndPrompt()
{
    // 创建设备连接检测对话框
    DeviceConnectionDialog dialog("虚拟示波器", this);

    // 显示对话框
    int result = dialog.exec();

    if (result == QDialog::Accepted) {
        UserChoice choice = dialog.getUserChoice();
        DataSourceType dataSource = dialog.getDataSourceType();

        qDebug() << QString("用户选择: %1, 数据源: %2")
                    .arg(static_cast<int>(choice))
                    .arg(static_cast<int>(dataSource));

        // 根据用户选择切换数据源
        switchDataSource(dataSource);

        return true;
    } else {
        qDebug() << "用户取消了虚拟示波器";
        return false;
    }
}

/**
 * @brief 切换数据源
 */
void OscilloscopeInterface::switchDataSource(DataSourceType dataSource)
{
    m_currentDataSource = dataSource;
    m_useRealData = (dataSource == DataSourceType::REAL_DATA);

    qDebug() << QString("虚拟示波器切换到%1数据源")
                .arg(m_useRealData ? "真实" : "模拟");

    // 如果使用真实数据，启动设备监控
    if (m_useRealData) {
        m_deviceManager->startStatusMonitoring();
        isConnected = true;
        currentDataSource = DataSourceType::REAL_DATA;
    } else {
        m_deviceManager->stopStatusMonitoring();
        isConnected = false;
        currentDataSource = DataSourceType::SIMULATION;
    }

    // 清空现有数据缓冲区
    QMutexLocker locker(&dataMutex);
    for (auto& channel : channels) {
        channel.dataBuffer.clear();
    }

    // 重新启动数据生成
    if (simulationTimer->isActive()) {
        simulationTimer->stop();
    }
    simulationTimer->start(50); // 20Hz数据生成
}

/**
 * @brief 接收到电机状态数据槽函数
 */
void OscilloscopeInterface::onMotorStatusReceived(int motorId, const MotorStatusFrame& statusFrame)
{
    if (!m_useRealData || motorId < 1 || motorId > 6) {
        return;
    }

    // 将真实电机数据添加到相应通道
    QMutexLocker locker(&dataMutex);

    double currentTime = QDateTime::currentMSecsSinceEpoch() / 1000.0;

    // 通道0: 电机速度
    if (motorId <= static_cast<int>(channels.size())) {
        int channelIndex = motorId - 1;
        if (channelIndex < channels.size()) {
            Oscilloscope::DataPoint speedPoint;
            speedPoint.timestamp = currentTime;
            speedPoint.value = statusFrame.current_speed / 1000.0; // 转换为合适的显示范围
            speedPoint.isValid = true;

            channels[channelIndex].dataBuffer.push(speedPoint);

            // 缓冲区大小由CircularBuffer自动管理
        }
    }

    qDebug() << QString("示波器接收到电机%1真实数据: 速度=%2")
                .arg(motorId)
                .arg(statusFrame.current_speed);
}

/**
 * @brief 设备连接状态改变槽函数
 */
void OscilloscopeInterface::onConnectionStatusChanged(CANFDConnectionStatus status)
{
    QString statusText;
    switch (status) {
    case CANFDConnectionStatus::DISCONNECTED:
        statusText = "设备已断开";
        isConnected = false;
        break;
    case CANFDConnectionStatus::CONNECTING:
        statusText = "设备连接中";
        break;
    case CANFDConnectionStatus::CONNECTED:
        statusText = "设备已连接";
        isConnected = true;
        break;
    case CANFDConnectionStatus::ACTIVE:
        statusText = "设备活跃";
        isConnected = true;
        break;
    }

    qDebug() << QString("虚拟示波器 - CANFD设备状态: %1").arg(statusText);

    // 如果设备断开且当前使用真实数据，可以选择切换到模拟数据
    if (status == CANFDConnectionStatus::DISCONNECTED && m_useRealData) {
        // 自动切换到模拟数据模式
        switchDataSource(DataSourceType::SIMULATION);
        qDebug() << "设备断开，自动切换到模拟数据模式";
    }
}
