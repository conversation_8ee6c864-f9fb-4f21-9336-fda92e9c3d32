/**
 * @file canform.cpp
 * @brief CAN协议表单类实现文件
 * @details 实现了CANForm类的所有功能，包括基于Qt Designer设计的CAN协议
 *          通信界面功能。该类集成ZLGCAN硬件SDK，提供完整的CAN设备管理、
 *          数据通信、心跳功能等。与CANInterface类不同，该类使用.ui文件
 *          定义界面布局。
 * <AUTHOR>
 * @date 2025-07-02
 * @version 1.0
 */

#include "canform.h"            // CAN协议表单类声明
#include "ui_canform.h"         // Qt Designer生成的UI类
#include "cancommform.h"        // CAN通信表单类下
#include <QMessageBox>          // Qt消息框类
#include <QDebug>               // Qt调试输出类
#include <QTime>                // Qt时间类
#include <QStringList>          // Qt字符串列表类
#include <cstring>              // C标准库字符串函数

/**
 * @brief CANForm构造函数实现
 * @param parent 父窗口指针
 * @details 初始化CAN协议表单的所有组件和设置：
 *          1. 初始化成员变量和状态
 *          2. 设置UI界面
 *          3. 创建和配置定时器
 *          4. 建立信号槽连接
 *          5. 加载设备信息和动态库
 *          6. 初始化配置对话框
 */
CANForm::CANForm(QWidget *parent) :
    QWidget(parent),                                              // 调用基类构造函数
    ui(new Ui::CANForm),                                         // 创建UI实例
    deviceHandle(INVALID_DEVICE_HANDLE),                         // 初始化设备句柄为无效值
    channelHandle(INVALID_CHANNEL_HANDLE),                       // 初始化通道句柄为无效值
    isDeviceOpened(false),                                       // 初始化设备打开状态
    isChannelStarted(false),                                     // 初始化通道启动状态
    isHeartbeatRunning(false),                                   // 初始化心跳运行状态
    deviceType(ZCAN_USBCANFD_100U),                             // 默认使用USBCANFD-100U设备类型
    deviceIndex(0),                                              // 默认设备索引为0
    channelIndex(0),                                             // 默认通道索引为0
    zlgcanLib(nullptr),                                          // 初始化动态库指针
    configDialog(nullptr),                                       // 初始化配置对话框指针
    commForm(nullptr)                                            // 初始化通信表单指针
{
    // === 第一步：设置UI界面 ===
    ui->setupUi(this);                                           // 加载由Qt Designer设计的界面布局

    // === 第二步：初始化心跳包定时器 ===
    heartbeatTimer = new QTimer(this);                           // 创建心跳定时器
    heartbeatTimer->setInterval(1000);                           // 设置1秒间隔
    connect(heartbeatTimer, &QTimer::timeout, this, &CANForm::onHeartbeatTimeout);  // 连接超时信号

    // === 第三步：初始化接收数据定时器 ===
    receiveTimer = new QTimer(this);                             // 创建接收定时器
    receiveTimer->setInterval(50);                               // 设置50ms间隔检查接收数据
    connect(receiveTimer, &QTimer::timeout, this, &CANForm::onReceiveData);  // 连接超时信号

    // === 第四步：执行初始化步骤 ===
    qDebug() << "CANForm 构造函数：开始初始化";
    initUI();                                                    // 初始化UI控件和参数
    qDebug() << "CANForm 构造函数：initUI 完成";
    setupConnections();                                          // 建立信号槽连接
    qDebug() << "CANForm 构造函数：setupConnections 完成";
    loadDeviceInfo();                                            // 加载设备信息
    qDebug() << "CANForm 构造函数：loadDeviceInfo 完成";

    // === 第五步：加载ZLGCAN动态库 ===
    if (!loadZLGCANLibrary()) {
        qDebug() << "警告: ZLGCAN库加载失败，CAN功能将不可用";
    }

    // === 第六步：初始化配置对话框 ===
    configDialog = new CANConfig(this);                         // 创建CAN配置对话框实例
}

/**
 * @brief CANForm析构函数实现
 * @details 清理资源和关闭设备连接：
 *          1. 检查设备状态并关闭设备
 *          2. 卸载和释放动态库资源
 *          3. 释放UI资源
 */
CANForm::~CANForm()
{
    // === 第一步：关闭设备连接 ===
    if (isDeviceOpened) {
        closeDevice();                                           // 关闭已打开的设备
    }

    // === 第二步：清理动态库资源 ===
    if (zlgcanLib) {
        zlgcanLib->unload();                                     // 卸载动态库
        delete zlgcanLib;                                        // 释放动态库对象
    }

    // === 第三步：释放UI资源 ===
    delete ui;                                                   // 释放UI对象
}

/**
 * @brief 初始化UI界面函数实现
 * @details 设置UI控件的初始状态和选项：
 *          1. 初始化协议选择下拉框
 *          2. 初始化波特率选择下拉框
 *          3. 更新控件状态
 */
void CANForm::initUI()
{
    // === 第一步：初始化协议选择下拉框 ===
    ui->protocolComboBox->addItem("CAN");                       // 添加CAN协议选项
    ui->protocolComboBox->addItem("CANFD");                     // 添加CANFD协议选项

    // === 第二步：初始化波特率下拉框 ===
    ui->baudrateComboBox->addItem("500Kbps");                   // 添加500K波特率选项
    ui->baudrateComboBox->addItem("1Mbps");                     // 添加1M波特率选项

    // === 第三步：初始化其他UI控件状态 ===
    updateControlState();                                        // 更新控件启用/禁用状态
}

/**
 * @brief 建立信号槽连接函数实现
 * @details 建立UI控件与槽函数之间的信号槽连接：
 *          1. 设备控制按钮连接
 *          2. 通道控制按钮连接
 *          3. 调试输出连接状态
 */
void CANForm::setupConnections()
{
    qDebug() << "setupConnections() 被调用";

    // === 第一步：设备控制按钮连接 ===
    qDebug() << "连接 openButton 到 onShowConfigDialog";
    connect(ui->openButton, &QPushButton::clicked, this, &CANForm::onShowConfigDialog);      // 打开按钮显示配置对话框
    connect(ui->closeButton, &QPushButton::clicked, this, &CANForm::onCloseDevice);          // 关闭按钮关闭设备
    connect(ui->refreshButton, &QPushButton::clicked, this, &CANForm::onRefreshDevices);     // 刷新按钮刷新设备列表

    // === 第二步：通道控制按钮连接 ===
    connect(ui->startButton, &QPushButton::clicked, this, &CANForm::onStartCAN);             // 启动按钮启动CAN通道
    connect(ui->stopButton, &QPushButton::clicked, this, &CANForm::onStopCAN);               // 停止按钮停止CAN通道
    connect(ui->resetButton, &QPushButton::clicked, this, &CANForm::onResetCAN);             // 重置按钮重置CAN通道

    // === 第三步：数据收发按钮连接 ===
    connect(ui->sendButton, &QPushButton::clicked, this, &CANForm::onSendData);              // 发送按钮发送数据
    connect(ui->clearSendButton, &QPushButton::clicked, this, &CANForm::onClearSendArea);    // 清空发送区域按钮
    connect(ui->clearReceiveButton, &QPushButton::clicked, this, &CANForm::onClearReceiveArea);  // 清空接收区域按钮

    // === 第四步：心跳包控制按钮连接 ===
    connect(ui->startHeartbeatButton, &QPushButton::clicked, this, &CANForm::onStartHeartbeat);  // 启动心跳按钮
    connect(ui->stopHeartbeatButton, &QPushButton::clicked, this, &CANForm::onStopHeartbeat);    // 停止心跳按钮

    // === 第五步：配置相关控件连接 ===
    connect(ui->protocolComboBox, QOverload<int>::of(&QComboBox::currentIndexChanged),
            this, &CANForm::onProtocolChanged);                                              // 协议选择下拉框
    connect(ui->baudrateComboBox, QOverload<int>::of(&QComboBox::currentIndexChanged),
            this, &CANForm::onBaudrateChanged);                                              // 波特率选择下拉框
    connect(ui->terminalResistanceCheckBox, &QCheckBox::toggled,
            this, &CANForm::onTerminalResistanceChanged);                                    // 终端电阻复选框

    // === 第六步：返回按钮连接 ===
    connect(ui->backButton, &QPushButton::clicked, this, &CANForm::onBackButtonClicked);     // 返回按钮
}

/**
 * @brief 加载设备信息函数实现
 * @details 设置默认的CAN设备配置参数：
 *          1. 设置默认设备类型为USBCANFD-100U
 *          2. 设置默认设备索引和通道索引
 *          3. 输出调试信息
 */
void CANForm::loadDeviceInfo()
{
    // === 设置默认设备配置参数 ===
    deviceType = ZCAN_USBCANFD_100U;                             // 设备类型：USBCANFD-100U
    deviceIndex = 0;                                             // 设备索引：0（第一个设备）
    channelIndex = 0;                                            // 通道索引：0（第一个通道）

    // === 输出调试信息 ===
    qDebug() << "默认设备配置:";
    qDebug() << "  设备类型: USBCANFD-100U";
    qDebug() << "  设备索引:" << deviceIndex;
    qDebug() << "  通道索引:" << channelIndex;
}

/**
 * @brief 打开CAN设备函数实现
 * @return 设备打开成功返回true，失败返回false
 * @details 执行CAN设备打开操作：
 *          1. 检查ZLGCAN动态库是否已加载
 *          2. 关闭已打开的设备（如果存在）
 *          3. 调用ZLGCAN API打开设备
 *          4. 验证设备句柄有效性
 *          5. 更新设备状态和UI控件
 */
bool CANForm::openDevice()
{
    // === 第一步：检查动态库是否加载 ===
    if (!ZCAN_OpenDevice) {
        qDebug() << "ZLGCAN库未加载，无法打开设备";
        return false;
    }

    // === 第二步：如果设备已经打开，先关闭 ===
    if (deviceHandle != INVALID_DEVICE_HANDLE) {
        closeDevice();                                           // 关闭已打开的设备
    }

    // === 第三步：调用ZLGCAN API打开设备 ===
    deviceHandle = ZCAN_OpenDevice(deviceType, deviceIndex, 0);  // 打开指定类型和索引的设备
    if (deviceHandle == INVALID_DEVICE_HANDLE) {
        qDebug() << "设备打开失败！";
        return false;
    }

    qDebug() << "设备打开成功，句柄:" << deviceHandle;

    // 获取设备信息
    ZCAN_DEVICE_INFO deviceInfo;
    if (ZCAN_GetDeviceInf && ZCAN_GetDeviceInf(deviceHandle, &deviceInfo) == STATUS_OK) {
        qDebug() << "设备信息:";
        qDebug() << "  硬件版本:" << QString("V%1.%2").arg((deviceInfo.hw_Version >> 8) & 0xFF).arg(deviceInfo.hw_Version & 0xFF);
        qDebug() << "  固件版本:" << QString("V%1.%2").arg((deviceInfo.fw_Version >> 8) & 0xFF).arg(deviceInfo.fw_Version & 0xFF);
        qDebug() << "  驱动版本:" << QString("V%1.%2").arg((deviceInfo.dr_Version >> 8) & 0xFF).arg(deviceInfo.dr_Version & 0xFF);
        qDebug() << "  接口版本:" << QString("V%1.%2").arg((deviceInfo.in_Version >> 8) & 0xFF).arg(deviceInfo.in_Version & 0xFF);
        qDebug() << "  中断号:" << deviceInfo.irq_Num;
        qDebug() << "  CAN通道数:" << deviceInfo.can_Num;
        qDebug() << "  序列号:" << QString::fromLocal8Bit(reinterpret_cast<const char*>(deviceInfo.str_Serial_Num));
        qDebug() << "  硬件类型:" << QString::fromLocal8Bit(reinterpret_cast<const char*>(deviceInfo.str_hw_Type));
    }

    return true;
}

void CANForm::closeDevice()
{
    // 停止心跳包和接收定时器
    if (isHeartbeatRunning) {
        onStopHeartbeat();
    }
    if (receiveTimer->isActive()) {
        receiveTimer->stop();
    }

    // 停止通道
    if (channelHandle != INVALID_CHANNEL_HANDLE && isChannelStarted && ZCAN_ResetCAN) {
        ZCAN_ResetCAN(channelHandle);
        channelHandle = INVALID_CHANNEL_HANDLE;
        isChannelStarted = false;
    }

    // 关闭设备
    if (deviceHandle != INVALID_DEVICE_HANDLE && ZCAN_CloseDevice) {
        ZCAN_CloseDevice(deviceHandle);
        deviceHandle = INVALID_DEVICE_HANDLE;
        isDeviceOpened = false;
        qDebug() << "设备已关闭";
    }

    updateControlState();
}

void CANForm::updateControlState()
{
    // 根据设备状态更新UI控件状态
    ui->openButton->setEnabled(!isDeviceOpened);
    ui->closeButton->setEnabled(isDeviceOpened);
    ui->startButton->setEnabled(isDeviceOpened && !isChannelStarted);
    ui->stopButton->setEnabled(isDeviceOpened && isChannelStarted);
    ui->resetButton->setEnabled(isDeviceOpened && isChannelStarted);
    ui->sendButton->setEnabled(isDeviceOpened && isChannelStarted);
    ui->startHeartbeatButton->setEnabled(isDeviceOpened && isChannelStarted && !isHeartbeatRunning);
    ui->stopHeartbeatButton->setEnabled(isDeviceOpened && isChannelStarted && isHeartbeatRunning);
}

void CANForm::onOpenDeviceOld()
{
    if (openDevice()) {
        isDeviceOpened = true;
        updateControlState();
        QMessageBox::information(this, "提示", "设备打开成功！");
    } else {
        QMessageBox::warning(this, "错误", "设备打开失败！");
    }
}

void CANForm::onCloseDevice()
{
    closeDevice();
    QMessageBox::information(this, "提示", "设备已关闭！");
}

void CANForm::onStartHeartbeat()
{
    if (!isHeartbeatRunning) {
        heartbeatTimer->start();
        isHeartbeatRunning = true;
        updateControlState();
    }
}

void CANForm::onStopHeartbeat()
{
    if (isHeartbeatRunning) {
        heartbeatTimer->stop();
        isHeartbeatRunning = false;
        updateControlState();
    }
}

void CANForm::onHeartbeatTimeout()
{
    // 发送心跳包数据：00 11 22 33 44 55 66 77
    if (!isDeviceOpened || !isChannelStarted || channelHandle == INVALID_CHANNEL_HANDLE) {
        return;
    }

    QString protocol = ui->protocolComboBox->currentText();

    if (protocol == "CAN") {
        ZCAN_Transmit_Data frame;
        memset(&frame, 0, sizeof(frame));

        frame.frame.can_id = 0x100;  // 心跳包ID
        frame.frame.can_dlc = 8;     // 8字节数据
        frame.transmit_type = 0;     // 正常发送

        // 设置心跳包数据
        uint8_t heartbeatData[8] = {0x00, 0x11, 0x22, 0x33, 0x44, 0x55, 0x66, 0x77};
        memcpy(frame.frame.data, heartbeatData, 8);

        // 发送心跳包
        if (ZCAN_Transmit(channelHandle, &frame, 1) == 1) {
            appendLog("00 11 22 33 44 55 66 77 (心跳包)", false);
            qDebug() << "心跳包发送成功";
        } else {
            qDebug() << "心跳包发送失败";
        }
    } else {
        // CANFD心跳包
        ZCAN_TransmitFD_Data frame;
        memset(&frame, 0, sizeof(frame));

        frame.frame.can_id = 0x100;  // 心跳包ID
        frame.frame.len = 8;         // 8字节数据
        frame.frame.flags = 0;       // 标准CANFD帧
        frame.transmit_type = 0;     // 正常发送

        // 设置心跳包数据
        uint8_t heartbeatData[8] = {0x00, 0x11, 0x22, 0x33, 0x44, 0x55, 0x66, 0x77};
        memcpy(frame.frame.data, heartbeatData, 8);

        // 发送心跳包
        if (ZCAN_TransmitFD(channelHandle, &frame, 1) == 1) {
            appendLog("00 11 22 33 44 55 66 77 (心跳包)", false);
            qDebug() << "CANFD心跳包发送成功";
        } else {
            qDebug() << "CANFD心跳包发送失败";
        }
    }
}

void CANForm::onBackButtonClicked()
{
    if (isDeviceOpened) {
        closeDevice();
    }
    emit backToProtocolSelect();
}

void CANForm::onRefreshDevices()
{
    qDebug() << "刷新设备列表";

    // 检查当前设备是否在线
    if (deviceHandle != INVALID_DEVICE_HANDLE && ZCAN_IsDeviceOnLine) {
        UINT isOnline = ZCAN_IsDeviceOnLine(deviceHandle);
        if (isOnline) {
            QMessageBox::information(this, "设备状态", "设备在线");
            qDebug() << "设备在线";
        } else {
            QMessageBox::warning(this, "设备状态", "设备离线");
            qDebug() << "设备离线";
        }
    } else {
        QMessageBox::information(this, "设备状态", "未连接设备");
    }
}

bool CANForm::loadZLGCANLibrary()
{
    // 创建QLibrary对象
    zlgcanLib = new QLibrary("zlgcan.dll", this);

    // 尝试加载库
    if (!zlgcanLib->load()) {
        qDebug() << "无法加载zlgcan.dll:" << zlgcanLib->errorString();
        delete zlgcanLib;
        zlgcanLib = nullptr;
        return false;
    }

    // 获取函数指针
    ZCAN_OpenDevice = (ZCAN_OpenDevice_t)zlgcanLib->resolve("ZCAN_OpenDevice");
    ZCAN_CloseDevice = (ZCAN_CloseDevice_t)zlgcanLib->resolve("ZCAN_CloseDevice");
    ZCAN_GetDeviceInf = (ZCAN_GetDeviceInf_t)zlgcanLib->resolve("ZCAN_GetDeviceInf");
    ZCAN_InitCAN = (ZCAN_InitCAN_t)zlgcanLib->resolve("ZCAN_InitCAN");
    ZCAN_StartCAN = (ZCAN_StartCAN_t)zlgcanLib->resolve("ZCAN_StartCAN");
    ZCAN_ResetCAN = (ZCAN_ResetCAN_t)zlgcanLib->resolve("ZCAN_ResetCAN");
    ZCAN_Transmit = (ZCAN_Transmit_t)zlgcanLib->resolve("ZCAN_Transmit");
    ZCAN_Receive = (ZCAN_Receive_t)zlgcanLib->resolve("ZCAN_Receive");
    ZCAN_TransmitFD = (ZCAN_TransmitFD_t)zlgcanLib->resolve("ZCAN_TransmitFD");
    ZCAN_ReceiveFD = (ZCAN_ReceiveFD_t)zlgcanLib->resolve("ZCAN_ReceiveFD");
    ZCAN_IsDeviceOnLine = (ZCAN_IsDeviceOnLine_t)zlgcanLib->resolve("ZCAN_IsDeviceOnLine");
    ZCAN_GetReceiveNum = (ZCAN_GetReceiveNum_t)zlgcanLib->resolve("ZCAN_GetReceiveNum");
    ZCAN_ClearBuffer = (ZCAN_ClearBuffer_t)zlgcanLib->resolve("ZCAN_ClearBuffer");

    // 检查关键函数是否成功加载
    if (!ZCAN_OpenDevice || !ZCAN_CloseDevice || !ZCAN_InitCAN || !ZCAN_StartCAN) {
        qDebug() << "关键ZLGCAN函数加载失败";
        zlgcanLib->unload();
        delete zlgcanLib;
        zlgcanLib = nullptr;
        return false;
    }

    qDebug() << "ZLGCAN库加载成功";
    return true;
}

void CANForm::onStartCAN()
{
    if (isDeviceOpened && !isChannelStarted) {
        // 初始化通道
        if (!initializeChannel()) {
            QMessageBox::warning(this, "错误", "通道初始化失败！");
            return;
        }

        // 启动通道
        if (!ZCAN_StartCAN || ZCAN_StartCAN(channelHandle) != STATUS_OK) {
            QMessageBox::warning(this, "错误", "通道启动失败！");
            return;
        }

        isChannelStarted = true;
        updateControlState();

        // 启动接收数据定时器
        receiveTimer->start();

        QMessageBox::information(this, "提示", "通道启动成功！");
        qDebug() << "CAN通道启动成功";
    }
}

void CANForm::onStopCAN()
{
    if (isDeviceOpened && isChannelStarted) {
        // 停止接收定时器
        if (receiveTimer->isActive()) {
            receiveTimer->stop();
        }

        // 停止心跳包
        if (isHeartbeatRunning) {
            onStopHeartbeat();
        }

        // 复位通道
        if (channelHandle != INVALID_CHANNEL_HANDLE) {
            ZCAN_ResetCAN(channelHandle);
            channelHandle = INVALID_CHANNEL_HANDLE;
        }

        isChannelStarted = false;
        updateControlState();
        QMessageBox::information(this, "提示", "通道已停止！");
        qDebug() << "CAN通道已停止";
    }
}

void CANForm::onResetCAN()
{
    if (isDeviceOpened && isChannelStarted) {
        // 复位通道
        if (channelHandle != INVALID_CHANNEL_HANDLE) {
            if (ZCAN_ResetCAN && ZCAN_ResetCAN(channelHandle) == STATUS_OK) {
                // 清空缓冲区
                if (ZCAN_ClearBuffer) {
                    ZCAN_ClearBuffer(channelHandle);
                }
                QMessageBox::information(this, "提示", "通道已复位！");
                qDebug() << "CAN通道复位成功";
            } else {
                QMessageBox::warning(this, "错误", "通道复位失败！");
                qDebug() << "CAN通道复位失败";  
            }
        }
    }
}

void CANForm::onSendData()
{
    if (!isDeviceOpened || !isChannelStarted) {
        QMessageBox::warning(this, "警告", "设备未打开或通道未启动！");
        return;
    }

    QString data = ui->sendTextEdit->toPlainText().trimmed();
    if (data.isEmpty()) {
        QMessageBox::warning(this, "警告", "发送数据不能为空！");
        return;
    }

    // 解析十六进制数据
    QStringList hexList = data.split(' ', Qt::SkipEmptyParts);
    if (hexList.size() > 8) {
        QMessageBox::warning(this, "警告", "CAN数据长度不能超过8字节！");
        return;
    }

    QString protocol = ui->protocolComboBox->currentText();

    if (protocol == "CAN") {
        // 发送CAN数据
        ZCAN_Transmit_Data frame;
        memset(&frame, 0, sizeof(frame));

        frame.frame.can_id = 0x123;  // 默认ID，可以后续添加ID输入框
        frame.frame.can_dlc = hexList.size();
        frame.transmit_type = 0;  // 正常发送   

        // 填充数据
        for (int i = 0; i < hexList.size(); i++) {
            bool ok;
            frame.frame.data[i] = hexList[i].toUInt(&ok, 16);
            if (!ok) {
                QMessageBox::warning(this, "警告", QString("无效的十六进制数据: %1").arg(hexList[i]));
                return;
            }
        }

        // 发送数据
        if (ZCAN_Transmit(channelHandle, &frame, 1) == 1) {
            appendLog(data, false);
            qDebug() << "CAN数据发送成功:" << data;
        } else {
            QMessageBox::warning(this, "错误", "CAN数据发送失败！");
            qDebug() << "CAN数据发送失败";
        }
    } else {
        // 发送CANFD数据
        if (hexList.size() > 64) {
            QMessageBox::warning(this, "警告", "CANFD数据长度不能超过64字节！");
            return;
        }

        ZCAN_TransmitFD_Data frame;
        memset(&frame, 0, sizeof(frame));

        frame.frame.can_id = 0x123;  // 默认ID
        frame.frame.len = hexList.size();
        frame.frame.flags = 0;  // 标准CANFD帧
        frame.transmit_type = 0;  // 正常发送

        // 填充数据
        for (int i = 0; i < hexList.size(); i++) {
            bool ok;
            frame.frame.data[i] = hexList[i].toUInt(&ok, 16);
            if (!ok) {
                QMessageBox::warning(this, "警告", QString("无效的十六进制数据: %1").arg(hexList[i]));
                return;
            }
        }

        // 发送数据
        if (ZCAN_TransmitFD(channelHandle, &frame, 1) == 1) {   
            appendLog(data, false);
            qDebug() << "CANFD数据发送成功:" << data;
        } else {
            QMessageBox::warning(this, "错误", "CANFD数据发送失败！");
            qDebug() << "CANFD数据发送失败";
        }
    }
}

void CANForm::onReceiveData()
{
    if (!isDeviceOpened || !isChannelStarted || channelHandle == INVALID_CHANNEL_HANDLE) {
        return;
    }

    QString protocol = ui->protocolComboBox->currentText();

    if (protocol == "CAN") {
        // 接收CAN数据
        if (!ZCAN_GetReceiveNum || !ZCAN_Receive) return;
        UINT receiveNum = ZCAN_GetReceiveNum(channelHandle, TYPE_CAN);
        if (receiveNum > 0) {
            ZCAN_Receive_Data receiveData[100];  // 一次最多接收100帧
            UINT actualNum = ZCAN_Receive(channelHandle, receiveData,
                                        qMin(receiveNum, 100U), 0);

            for (UINT i = 0; i < actualNum; i++) {
                QString dataStr;
                for (int j = 0; j < receiveData[i].frame.can_dlc; j++) {
                    dataStr += QString("%1 ").arg(receiveData[i].frame.data[j], 2, 16, QChar('0')).toUpper();
                }
                dataStr = dataStr.trimmed();

                QString logStr = QString("ID:0x%1 DLC:%2 Data:%3")
                    .arg(receiveData[i].frame.can_id & 0x1FFFFFFF, 0, 16)
                    .arg(receiveData[i].frame.can_dlc)
                    .arg(dataStr);

                appendLog(logStr, true);
            }
        }
    } else {
        // 接收CANFD数据
        if (!ZCAN_GetReceiveNum || !ZCAN_ReceiveFD) return;
        UINT receiveNum = ZCAN_GetReceiveNum(channelHandle, TYPE_CANFD);
        if (receiveNum > 0) {
            ZCAN_ReceiveFD_Data receiveData[100];  // 一次最多接收100帧
            UINT actualNum = ZCAN_ReceiveFD(channelHandle, receiveData,
                                          qMin(receiveNum, 100U), 0);

            for (UINT i = 0; i < actualNum; i++) {
                QString dataStr;
                for (int j = 0; j < receiveData[i].frame.len; j++) {
                    dataStr += QString("%1 ").arg(receiveData[i].frame.data[j], 2, 16, QChar('0')).toUpper();
                }
                dataStr = dataStr.trimmed();

                QString logStr = QString("ID:0x%1 LEN:%2 Data:%3")
                    .arg(receiveData[i].frame.can_id & 0x1FFFFFFF, 0, 16)
                    .arg(receiveData[i].frame.len)
                    .arg(dataStr);

                appendLog(logStr, true);
            }
        }
    }
}

void CANForm::onClearSendArea()
{
    ui->sendTextEdit->clear();
}

void CANForm::onClearReceiveArea()
{
    ui->receiveTextEdit->clear();
}

void CANForm::onProtocolChanged(int index)
{
    QString protocol = ui->protocolComboBox->currentText();
    qDebug() << "协议切换为:" << protocol;
    
    // 根据协议类型更新波特率选项
    ui->baudrateComboBox->clear();
    if (protocol == "CAN") {
        ui->baudrateComboBox->addItem("500Kbps");
        ui->baudrateComboBox->addItem("1Mbps");
    } else { // CANFD
        ui->baudrateComboBox->addItem("500Kbps 80%");
        ui->baudrateComboBox->addItem("1Mbps 80%");
        ui->baudrateComboBox->addItem("2Mbps 80%");
    }
}

void CANForm::onBaudrateChanged(int index)
{
    QString baudrate = ui->baudrateComboBox->currentText();
    qDebug() << "波特率切换为:" << baudrate;
    
    if (isDeviceOpened && isChannelStarted) {
        // TODO: 实现波特率切换逻辑
    }
}

void CANForm::onTerminalResistanceChanged(bool enabled)
{
    qDebug() << "终端电阻状态:" << (enabled ? "启用" : "禁用");
    
    if (isDeviceOpened) {
        // TODO: 实现终端电阻控制逻辑
    }
}

void CANForm::appendLog(const QString &text, bool isReceived)
{
    QString timeStr = QTime::currentTime().toString("hh:mm:ss.zzz");
    QString prefix = isReceived ? "接收" : "发送";
    QString logText = QString("[%1] %2: %3").arg(timeStr).arg(prefix).arg(text);
    
    QTextEdit *textEdit = isReceived ? ui->receiveTextEdit : ui->sendTextEdit;
    textEdit->append(logText);
}

bool CANForm::initializeChannel()
{
    if (deviceHandle == INVALID_DEVICE_HANDLE) {
        qDebug() << "设备未打开，无法初始化通道";
        return false;
    }

    if (!ZCAN_InitCAN) {
        qDebug() << "ZLGCAN库未加载，无法初始化通道";
        return false;
    }

    // 配置通道初始化参数
    ZCAN_CHANNEL_INIT_CONFIG initConfig;
    memset(&initConfig, 0, sizeof(initConfig));

    QString protocol = ui->protocolComboBox->currentText();
    QString baudrate = ui->baudrateComboBox->currentText();

    if (protocol == "CAN") {
        // CAN配置
        initConfig.can_type = TYPE_CAN;
        initConfig.can.acc_code = 0x00000000;
        initConfig.can.acc_mask = 0xFFFFFFFF;
        initConfig.can.filter = 1;  // 接收所有帧
        initConfig.can.mode = 0;    // 正常模式

        // 根据波特率设置timing参数
        if (baudrate == "500Kbps") {
            initConfig.can.timing0 = 0x00;
            initConfig.can.timing1 = 0x1C;
        } else if (baudrate == "1Mbps") {
            initConfig.can.timing0 = 0x00;
            initConfig.can.timing1 = 0x14;
        } else {
            // 默认500Kbps
            initConfig.can.timing0 = 0x00;
            initConfig.can.timing1 = 0x1C;
        }
    } else {
        // CANFD配置
        initConfig.can_type = TYPE_CANFD;
        initConfig.canfd.acc_code = 0x00000000;
        initConfig.canfd.acc_mask = 0xFFFFFFFF;
        initConfig.canfd.filter = 1;  // 接收所有帧
        initConfig.canfd.mode = 0;    // 正常模式



        // CANFD波特率配置（仲裁段和数据段）
        if (baudrate.contains("500Kbps")) {
            initConfig.canfd.abit_timing = 0x00001C00;  // 仲裁段500Kbps
            initConfig.canfd.dbit_timing = 0x00000A00;  // 数据段2Mbps
        } else if (baudrate.contains("1Mbps")) {
            initConfig.canfd.abit_timing = 0x00001400;  // 仲裁段1Mbps
            initConfig.canfd.dbit_timing = 0x00000500;  // 数据段4Mbps
        } else {
            // 默认配置
            initConfig.canfd.abit_timing = 0x00001C00;
            initConfig.canfd.dbit_timing = 0x00000A00;
        }
    }

    // 初始化通道
    channelHandle = ZCAN_InitCAN(deviceHandle, channelIndex, &initConfig);
    if (channelHandle == INVALID_CHANNEL_HANDLE) {
        qDebug() << "通道初始化失败";
        return false;
    }

    qDebug() << "通道初始化成功，句柄:" << channelHandle;
    return true;
}

// 新增配置相关函数实现
void CANForm::onShowConfigDialog()
{
    qDebug() << "onShowConfigDialog() 被调用";

    // 先显示一个简单的消息框来确认函数被调用
    QMessageBox::information(this, "调试", "onShowConfigDialog() 函数被调用了！");

    if (!configDialog) {
        qDebug() << "configDialog 为空，重新创建";
        configDialog = new CANConfig(this);
    }

    qDebug() << "显示配置对话框";
    if (configDialog->exec() == QDialog::Accepted) {
        qDebug() << "配置对话框被确认";
        onConfigConfirmed();
    } else {
        qDebug() << "配置对话框被取消";
    }
}

void CANForm::onConfigConfirmed()
{
    currentConfig = configDialog->getConfigData();

    // 创建并显示通信界面
    if (commForm) {
        delete commForm;
    }

    commForm = new CANCommForm();
    commForm->setConfigData(currentConfig);

    // 连接返回信号
    connect(commForm, &CANCommForm::backToProtocolSelect,
            this, &CANForm::backToProtocolSelect);

    // 隐藏当前窗口，显示通信界面
    this->hide();
    commForm->show();
}