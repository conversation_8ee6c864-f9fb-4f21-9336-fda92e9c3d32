#include "baudratecalculator_new.h"
#include "ui_baudratecalculator_new.h"
#include <QApplication>
#include <QClipboard>
#include <QMessageBox>
#include <QDebug>
#include <QInputDialog>
#include <QSettings>
#include <QPushButton>
#include <QVBoxLayout>
#include <QFormLayout>
#include <QTableWidgetItem>
#include <QHeaderView>
#include <cmath>
#include <algorithm>

/**
 * @brief 构造函数 - 按照ZCANPro设计重新实现
 * @param parent 父窗口指针
 * @details 初始化波特率计算器界面，复刻ZCANPro的设计和功能
 */
BaudrateCalculator::BaudrateCalculator(QWidget *parent)
    : QDialog(parent)
    , ui(new Ui::BaudrateCalculator)
    , clockFrequency(80000000)  // 默认80MHz，与ZCANPro一致
{
    ui->setupUi(this);

    // 设置窗口属性
    setFixedSize(1000, 900);
    setWindowFlags(Qt::Dialog | Qt::WindowCloseButtonHint);
    setModal(true);

    // 初始化界面
    initializeUI();

    // 连接信号槽
    connectSignals();

    // 计算初始值
    calculateBaudrates();
}

/**
 * @brief 析构函数
 */
BaudrateCalculator::~BaudrateCalculator()
{
    delete ui;
}

/**
 * @brief 初始化UI界面
 * @details 设置表格、默认值等，复刻ZCANPro的界面布局
 */
void BaudrateCalculator::initializeUI()
{
    // 设置仲裁段表格
    setupArbitrationTable();
    
    // 设置数据段表格
    setupDataTable();
    
    // 设置默认值
    ui->clockCombo->setCurrentText("40");
    ui->baudCombo->setCurrentText("500");
    ui->dataBaudCombo->setCurrentText("4000");
    ui->sjwCombo->setCurrentText("0");
    ui->dataSjwCombo->setCurrentText("0");
    ui->diffLineEdit->setText("0.05");
    ui->dataDiffLineEdit->setText("0.05");

    // 设置Diff输入框的验证器，只允许输入0.00-10.00的数字
    QDoubleValidator *diffValidator = new QDoubleValidator(0.00, 10.00, 2, this);
    diffValidator->setNotation(QDoubleValidator::StandardNotation);
    ui->diffLineEdit->setValidator(diffValidator);

    QDoubleValidator *dataDiffValidator = new QDoubleValidator(0.00, 10.00, 2, this);
    dataDiffValidator->setNotation(QDoubleValidator::StandardNotation);
    ui->dataDiffLineEdit->setValidator(dataDiffValidator);
    
    // 设置结果显示区域初始内容
    updateResultDisplay();
}

/**
 * @brief 设置仲裁段表格
 * @details 配置仲裁段参数表格的列宽和样式
 */
void BaudrateCalculator::setupArbitrationTable()
{
    ui->arbitrationTable->setRowCount(8);
    ui->arbitrationTable->setColumnCount(7);
    
    // 设置表头
    QStringList headers = {"Value", "BRP", "TSEG1", "TSEG2", "SMP", "Baud", "Diff"};
    ui->arbitrationTable->setHorizontalHeaderLabels(headers);
    
    // 设置列宽
    ui->arbitrationTable->setColumnWidth(0, 80);  // Value
    ui->arbitrationTable->setColumnWidth(1, 60);  // BRP
    ui->arbitrationTable->setColumnWidth(2, 60);  // TSEG1
    ui->arbitrationTable->setColumnWidth(3, 60);  // TSEG2
    ui->arbitrationTable->setColumnWidth(4, 80);  // SMP
    ui->arbitrationTable->setColumnWidth(5, 120); // Baud
    ui->arbitrationTable->setColumnWidth(6, 80);  // Diff
    
    // 设置表格属性
    ui->arbitrationTable->setSelectionBehavior(QAbstractItemView::SelectRows);
    ui->arbitrationTable->setAlternatingRowColors(true);
    ui->arbitrationTable->verticalHeader()->setVisible(false);
    
    // 填充示例数据（模拟ZCANPro的显示）
    fillArbitrationTableData();
}

/**
 * @brief 设置数据段表格
 * @details 配置数据段参数表格的列宽和样式
 */
void BaudrateCalculator::setupDataTable()
{
    ui->dataTable->setRowCount(8);
    ui->dataTable->setColumnCount(7);
    
    // 设置表头
    QStringList headers = {"Value", "BRP", "TSEG1", "TSEG2", "SMP", "Baud", "Diff"};
    ui->dataTable->setHorizontalHeaderLabels(headers);
    
    // 设置列宽
    ui->dataTable->setColumnWidth(0, 80);  // Value
    ui->dataTable->setColumnWidth(1, 60);  // BRP
    ui->dataTable->setColumnWidth(2, 60);  // TSEG1
    ui->dataTable->setColumnWidth(3, 60);  // TSEG2
    ui->dataTable->setColumnWidth(4, 80);  // SMP
    ui->dataTable->setColumnWidth(5, 120); // Baud
    ui->dataTable->setColumnWidth(6, 80);  // Diff
    
    // 设置表格属性
    ui->dataTable->setSelectionBehavior(QAbstractItemView::SelectRows);
    ui->dataTable->setAlternatingRowColors(true);
    ui->dataTable->verticalHeader()->setVisible(false);
    
    // 填充示例数据
    fillDataTableData();
}

/**
 * @brief 连接信号槽
 * @details 连接UI控件的信号到相应的槽函数
 */
void BaudrateCalculator::connectSignals()
{
    // 设备配置变化
    connect(ui->clockCombo, QOverload<const QString &>::of(&QComboBox::currentTextChanged),
            this, &BaudrateCalculator::onClockChanged);
    

    // 仲裁段配置变化
    connect(ui->baudCombo, QOverload<const QString &>::of(&QComboBox::currentTextChanged),
            this, &BaudrateCalculator::onArbitrationBaudChanged);
    connect(ui->sjwCombo, QOverload<const QString &>::of(&QComboBox::currentTextChanged),
            this, &BaudrateCalculator::onArbitrationParameterChanged);
    connect(ui->diffLineEdit, &QLineEdit::textChanged,
            this, &BaudrateCalculator::onArbitrationParameterChanged);

    // 数据段配置变化
    connect(ui->dataBaudCombo, QOverload<const QString &>::of(&QComboBox::currentTextChanged),
            this, &BaudrateCalculator::onDataBaudChanged);
    connect(ui->dataSjwCombo, QOverload<const QString &>::of(&QComboBox::currentTextChanged),
            this, &BaudrateCalculator::onDataParameterChanged);
    connect(ui->dataDiffLineEdit, &QLineEdit::textChanged,
            this, &BaudrateCalculator::onDataParameterChanged);
    
    // 表格选择变化
    connect(ui->arbitrationTable, &QTableWidget::itemSelectionChanged,
            this, &BaudrateCalculator::onArbitrationSelectionChanged);
    connect(ui->dataTable, &QTableWidget::itemSelectionChanged,
            this, &BaudrateCalculator::onDataSelectionChanged);
    
    // 表格过滤输入框
    connect(ui->arbitrationBrpLineEdit, &QLineEdit::textChanged,
            this, &BaudrateCalculator::onArbitrationBrpFilterChanged);
    connect(ui->arbitrationTseg1LineEdit, &QLineEdit::textChanged,
            this, &BaudrateCalculator::onArbitrationTseg1FilterChanged);
    connect(ui->arbitrationTseg2LineEdit, &QLineEdit::textChanged,
            this, &BaudrateCalculator::onArbitrationTseg2FilterChanged);
    connect(ui->dataBrpLineEdit, &QLineEdit::textChanged,
            this, &BaudrateCalculator::onDataBrpFilterChanged);
    connect(ui->dataTseg1LineEdit, &QLineEdit::textChanged,
            this, &BaudrateCalculator::onDataTseg1FilterChanged);
    connect(ui->dataTseg2LineEdit, &QLineEdit::textChanged,
            this, &BaudrateCalculator::onDataTseg2FilterChanged);

    // 清除按钮
    connect(ui->arbitrationClearButton, &QPushButton::clicked,
            this, &BaudrateCalculator::onArbitrationClearClicked);
    connect(ui->dataClearButton, &QPushButton::clicked,
            this, &BaudrateCalculator::onDataClearClicked);

    // 底部按钮
    connect(ui->copyResultButton, &QPushButton::clicked,
            this, &BaudrateCalculator::onCopyResult);
    connect(ui->returnButton, &QPushButton::clicked,
            this, &BaudrateCalculator::onReturnClicked);
}

/**
 * @brief 填充仲裁段表格数据
 * @details 根据当前配置计算并显示仲裁段的各种可能配置
 */
void BaudrateCalculator::fillArbitrationTableData()
{
    // 获取当前设置
    int targetBaud = ui->baudCombo->currentText().toInt() * 1000; // 转换为bps
    double maxDiff = ui->diffLineEdit->text().isEmpty() ? 5.0 : ui->diffLineEdit->text().toDouble();

    // 计算所有可能的配置
    QVector<BaudrateResult> results = calculatePossibleConfigurations(targetBaud, maxDiff, false);

    // 清空表格
    ui->arbitrationTable->setRowCount(0);

    // 填充计算结果
    for (int i = 0; i < results.size() && i < 20; ++i) { // 最多显示20行
        const auto& result = results[i];

        ui->arbitrationTable->insertRow(i);

        // 使用当前选择的SJW值
        int currentSjw = ui->sjwCombo->currentText().toInt();
        if (currentSjw == 0) currentSjw = 1; // 确保SJW至少为1

        // 计算Value值 (根据CAN协议格式)
        QString value = calculateValueHex(result.brp, result.tseg1, result.tseg2, currentSjw);

        // 设置表格项
        ui->arbitrationTable->setItem(i, 0, new QTableWidgetItem(value));
        ui->arbitrationTable->setItem(i, 1, new QTableWidgetItem(QString::number(result.brp)));
        ui->arbitrationTable->setItem(i, 2, new QTableWidgetItem(QString::number(result.tseg1)));
        ui->arbitrationTable->setItem(i, 3, new QTableWidgetItem(QString::number(result.tseg2)));
        ui->arbitrationTable->setItem(i, 4, new QTableWidgetItem(QString("%1 %").arg(result.samplePoint, 0, 'f', 2)));
        ui->arbitrationTable->setItem(i, 5, new QTableWidgetItem(QString("%1 bps").arg(result.actualBaud)));
        ui->arbitrationTable->setItem(i, 6, new QTableWidgetItem(QString("%1 %").arg(result.error, 0, 'f', 2)));

        // 设置行高亮（如果误差为0或最小）
        if (qAbs(result.error) < 0.01) {
            for (int col = 0; col < 7; ++col) {
                ui->arbitrationTable->item(i, col)->setBackground(QColor(173, 216, 230)); // 浅蓝色
            }
        }
    }

    // 默认选择第一行
    if (ui->arbitrationTable->rowCount() > 0) {
        ui->arbitrationTable->selectRow(0);
    }
}

/**
 * @brief 填充数据段表格数据
 * @details 根据当前配置计算并显示数据段的各种可能配置
 */
void BaudrateCalculator::fillDataTableData()
{
    // 获取当前设置
    int targetBaud = ui->dataBaudCombo->currentText().toInt() * 1000; // 转换为bps
    double maxDiff = ui->dataDiffLineEdit->text().isEmpty() ? 5.0 : ui->dataDiffLineEdit->text().toDouble();

    // 计算所有可能的配置
    QVector<BaudrateResult> results = calculatePossibleConfigurations(targetBaud, maxDiff, true);

    // 清空表格
    ui->dataTable->setRowCount(0);

    // 填充计算结果
    for (int i = 0; i < results.size() && i < 20; ++i) { // 最多显示20行
        const auto& result = results[i];

        ui->dataTable->insertRow(i);

        // 计算Value值 (根据CANFD数据段格式)
        QString value = calculateValueHex(result.brp, result.tseg1, result.tseg2, result.sjw);

        // 设置表格项
        ui->dataTable->setItem(i, 0, new QTableWidgetItem(value));
        ui->dataTable->setItem(i, 1, new QTableWidgetItem(QString::number(result.brp)));
        ui->dataTable->setItem(i, 2, new QTableWidgetItem(QString::number(result.tseg1)));
        ui->dataTable->setItem(i, 3, new QTableWidgetItem(QString::number(result.tseg2)));
        ui->dataTable->setItem(i, 4, new QTableWidgetItem(QString("%1 %").arg(result.samplePoint, 0, 'f', 2)));
        ui->dataTable->setItem(i, 5, new QTableWidgetItem(QString("%1 bps").arg(result.actualBaud)));
        ui->dataTable->setItem(i, 6, new QTableWidgetItem(QString("%1 %").arg(result.error, 0, 'f', 2)));

        // 设置行高亮（如果误差为0或最小）
        if (qAbs(result.error) < 0.01) {
            for (int col = 0; col < 7; ++col) {
                ui->dataTable->item(i, col)->setBackground(QColor(173, 216, 230)); // 浅蓝色
            }
        }
    }

    // 默认选择第一行
    if (ui->dataTable->rowCount() > 0) {
        ui->dataTable->selectRow(0);
    }
}

/**
 * @brief 计算波特率配置
 * @details 根据当前设置计算仲裁段和数据段的波特率配置
 */
void BaudrateCalculator::calculateBaudrates()
{
    // 重新计算并填充表格数据
    fillArbitrationTableData();
    fillDataTableData();

    // 更新结果显示
    updateResultDisplay();
}

/**
 * @brief 时钟频率变化处理
 * @details 当用户改变时钟频率时重新计算所有配置
 */
void BaudrateCalculator::onClockChanged()
{
    QString clockText = ui->clockCombo->currentText();
    clockFrequency = clockText.toInt() * 1000000; // 转换为Hz

    // 重新计算波特率配置
    calculateBaudrates();
}



/**
 * @brief 仲裁段波特率变化处理
 */
void BaudrateCalculator::onArbitrationBaudChanged()
{
    calculateBaudrates();
}

/**
 * @brief 仲裁段参数变化处理
 */
void BaudrateCalculator::onArbitrationParameterChanged()
{
    calculateBaudrates();
}

/**
 * @brief 数据段波特率变化处理
 */
void BaudrateCalculator::onDataBaudChanged()
{
    calculateBaudrates();
}

/**
 * @brief 数据段参数变化处理
 */
void BaudrateCalculator::onDataParameterChanged()
{
    calculateBaudrates();
}

/**
 * @brief 仲裁段表格选择变化处理
 */
void BaudrateCalculator::onArbitrationSelectionChanged()
{
    updateResultDisplay();
}

/**
 * @brief 数据段表格选择变化处理
 */
void BaudrateCalculator::onDataSelectionChanged()
{
    updateResultDisplay();
}



/**
 * @brief 复制配置结果按钮处理
 * @details 将当前配置结果复制到剪贴板
 */
void BaudrateCalculator::onCopyResult()
{
    QString configText = ui->resultLineEdit->text();
    QApplication::clipboard()->setText(configText);
    QMessageBox::information(this, "复制配置", "配置已复制到剪贴板");
}

/**
 * @brief 返回按钮处理
 * @details 关闭对话框并返回
 */
void BaudrateCalculator::onReturnClicked()
{
    reject(); // 关闭对话框
}

/**
 * @brief 更新结果显示
 * @details 根据当前选择的配置更新结果显示区域，格式与官方ZCANPro一致
 */
void BaudrateCalculator::updateResultDisplay()
{
    int arbitrationRow = ui->arbitrationTable->currentRow();
    int dataRow = ui->dataTable->currentRow();

    if (arbitrationRow >= 0 && dataRow >= 0) {
        // 获取选中的配置值
        QString arbitrationValue = ui->arbitrationTable->item(arbitrationRow, 0)->text();
        QString dataValue = ui->dataTable->item(dataRow, 0)->text();
        QString arbitrationBaud = ui->arbitrationTable->item(arbitrationRow, 5)->text();
        QString dataBaud = ui->dataTable->item(dataRow, 5)->text();
        QString arbitrationSmp = ui->arbitrationTable->item(arbitrationRow, 4)->text();
        QString dataSmp = ui->dataTable->item(dataRow, 4)->text();

        // 提取波特率数值和采样点百分比
        QString arbBaudStr = arbitrationBaud.split(" ")[0]; // 如 "500000"
        QString dataBaudStr = dataBaud.split(" ")[0];        // 如 "4000000"

        // 转换为Kbps和Mbps格式
        int arbBaudInt = arbBaudStr.toInt();
        int dataBaudInt = dataBaudStr.toInt();
        QString arbBaudDisplay = QString("%1Kbps").arg(arbBaudInt / 1000);
        QString dataBaudDisplay = QString("%1Mbps").arg(dataBaudInt / 1000000.0, 0, 'f', 1);

        // 提取采样点百分比（去掉%符号）
        QString arbSmpStr = arbitrationSmp.replace("%", "");
        QString dataSmpStr = dataSmp.replace("%", "");

        // 获取时钟频率
        QString clockMHz = ui->clockCombo->currentText();

        // 生成官方格式的配置字符串：500Kbps(75%),4.0Mbps(80%),(40,04C00001,00400002)
        currentConfigString = QString("%1(%2%%),%3(%4%%),(%5,%6,%7)")
                             .arg(arbBaudDisplay)
                             .arg(arbSmpStr)
                             .arg(dataBaudDisplay)
                             .arg(dataSmpStr)
                             .arg(clockMHz)
                             .arg(arbitrationValue.toUpper())
                             .arg(dataValue.toUpper());

        // 更新结果输入框
        ui->resultLineEdit->setText(currentConfigString);
    } else {
        // 显示默认配置
        QString clockMHz = ui->clockCombo->currentText();

        // 默认配置字符串
        currentConfigString = QString("500Kbps(75%%),4.0Mbps(80%%),(%1,04C00001,00400002)").arg(clockMHz);

        // 更新结果输入框
        ui->resultLineEdit->setText(currentConfigString);
    }
}

/**
 * @brief 仲裁段BRP输入框文本变化处理
 * @details 根据输入的BRP值过滤和高亮显示匹配的行
 */
void BaudrateCalculator::onArbitrationBrpFilterChanged()
{
    QString brpText = ui->arbitrationBrpLineEdit->text();
    QString tseg1Text = ui->arbitrationTseg1LineEdit->text();
    QString tseg2Text = ui->arbitrationTseg2LineEdit->text();

    int brpValue = brpText.isEmpty() ? -1 : brpText.toInt();
    int tseg1Value = tseg1Text.isEmpty() ? -1 : tseg1Text.toInt();
    int tseg2Value = tseg2Text.isEmpty() ? -1 : tseg2Text.toInt();

    filterTableData(ui->arbitrationTable, brpValue, tseg1Value, tseg2Value);
}

/**
 * @brief 仲裁段TSEG1输入框文本变化处理
 * @details 根据输入的TSEG1值过滤和高亮显示匹配的行
 */
void BaudrateCalculator::onArbitrationTseg1FilterChanged()
{
    QString brpText = ui->arbitrationBrpLineEdit->text();
    QString tseg1Text = ui->arbitrationTseg1LineEdit->text();
    QString tseg2Text = ui->arbitrationTseg2LineEdit->text();

    int brpValue = brpText.isEmpty() ? -1 : brpText.toInt();
    int tseg1Value = tseg1Text.isEmpty() ? -1 : tseg1Text.toInt();
    int tseg2Value = tseg2Text.isEmpty() ? -1 : tseg2Text.toInt();

    filterTableData(ui->arbitrationTable, brpValue, tseg1Value, tseg2Value);
}

/**
 * @brief 仲裁段TSEG2输入框文本变化处理
 * @details 根据输入的TSEG2值过滤和高亮显示匹配的行
 */
void BaudrateCalculator::onArbitrationTseg2FilterChanged()
{
    QString brpText = ui->arbitrationBrpLineEdit->text();
    QString tseg1Text = ui->arbitrationTseg1LineEdit->text();
    QString tseg2Text = ui->arbitrationTseg2LineEdit->text();

    int brpValue = brpText.isEmpty() ? -1 : brpText.toInt();
    int tseg1Value = tseg1Text.isEmpty() ? -1 : tseg1Text.toInt();
    int tseg2Value = tseg2Text.isEmpty() ? -1 : tseg2Text.toInt();

    filterTableData(ui->arbitrationTable, brpValue, tseg1Value, tseg2Value);
}

/**
 * @brief 数据段BRP输入框文本变化处理
 * @details 根据输入的BRP值过滤和高亮显示匹配的行
 */
void BaudrateCalculator::onDataBrpFilterChanged()
{
    QString brpText = ui->dataBrpLineEdit->text();
    QString tseg1Text = ui->dataTseg1LineEdit->text();
    QString tseg2Text = ui->dataTseg2LineEdit->text();

    int brpValue = brpText.isEmpty() ? -1 : brpText.toInt();
    int tseg1Value = tseg1Text.isEmpty() ? -1 : tseg1Text.toInt();
    int tseg2Value = tseg2Text.isEmpty() ? -1 : tseg2Text.toInt();

    filterTableData(ui->dataTable, brpValue, tseg1Value, tseg2Value);
}

/**
 * @brief 数据段TSEG1输入框文本变化处理
 * @details 根据输入的TSEG1值过滤和高亮显示匹配的行
 */
void BaudrateCalculator::onDataTseg1FilterChanged()
{
    QString brpText = ui->dataBrpLineEdit->text();
    QString tseg1Text = ui->dataTseg1LineEdit->text();
    QString tseg2Text = ui->dataTseg2LineEdit->text();

    int brpValue = brpText.isEmpty() ? -1 : brpText.toInt();
    int tseg1Value = tseg1Text.isEmpty() ? -1 : tseg1Text.toInt();
    int tseg2Value = tseg2Text.isEmpty() ? -1 : tseg2Text.toInt();

    filterTableData(ui->dataTable, brpValue, tseg1Value, tseg2Value);
}

/**
 * @brief 数据段TSEG2输入框文本变化处理
 * @details 根据输入的TSEG2值过滤和高亮显示匹配的行
 */
void BaudrateCalculator::onDataTseg2FilterChanged()
{
    QString brpText = ui->dataBrpLineEdit->text();
    QString tseg1Text = ui->dataTseg1LineEdit->text();
    QString tseg2Text = ui->dataTseg2LineEdit->text();

    int brpValue = brpText.isEmpty() ? -1 : brpText.toInt();
    int tseg1Value = tseg1Text.isEmpty() ? -1 : tseg1Text.toInt();
    int tseg2Value = tseg2Text.isEmpty() ? -1 : tseg2Text.toInt();

    filterTableData(ui->dataTable, brpValue, tseg1Value, tseg2Value);
}

/**
 * @brief 清除仲裁段过滤条件
 * @details 清空所有仲裁段输入框并恢复表格显示
 */
void BaudrateCalculator::onArbitrationClearClicked()
{
    ui->arbitrationBrpLineEdit->clear();
    ui->arbitrationTseg1LineEdit->clear();
    ui->arbitrationTseg2LineEdit->clear();

    // 恢复所有行的显示和背景色
    for (int row = 0; row < ui->arbitrationTable->rowCount(); ++row) {
        ui->arbitrationTable->setRowHidden(row, false);
        for (int col = 0; col < ui->arbitrationTable->columnCount(); ++col) {
            QTableWidgetItem* item = ui->arbitrationTable->item(row, col);
            if (item) {
                item->setBackground(QBrush());
            }
        }
    }
}

/**
 * @brief 清除数据段过滤条件
 * @details 清空所有数据段输入框并恢复表格显示
 */
void BaudrateCalculator::onDataClearClicked()
{
    ui->dataBrpLineEdit->clear();
    ui->dataTseg1LineEdit->clear();
    ui->dataTseg2LineEdit->clear();

    // 恢复所有行的显示和背景色
    for (int row = 0; row < ui->dataTable->rowCount(); ++row) {
        ui->dataTable->setRowHidden(row, false);
        for (int col = 0; col < ui->dataTable->columnCount(); ++col) {
            QTableWidgetItem* item = ui->dataTable->item(row, col);
            if (item) {
                item->setBackground(QBrush());
            }
        }
    }
}

/**
 * @brief 过滤表格数据
 * @details 根据输入的BRP、TSEG1、TSEG2值过滤和高亮显示匹配的行
 * @param table 要过滤的表格
 * @param brpValue BRP过滤值（-1表示不过滤）
 * @param tseg1Value TSEG1过滤值（-1表示不过滤）
 * @param tseg2Value TSEG2过滤值（-1表示不过滤）
 */
void BaudrateCalculator::filterTableData(QTableWidget* table, int brpValue, int tseg1Value, int tseg2Value)
{
    if (!table) return;

    // 如果所有过滤条件都为空，显示所有行并清除高亮
    if (brpValue == -1 && tseg1Value == -1 && tseg2Value == -1) {
        for (int row = 0; row < table->rowCount(); ++row) {
            table->setRowHidden(row, false);
            for (int col = 0; col < table->columnCount(); ++col) {
                QTableWidgetItem* item = table->item(row, col);
                if (item) {
                    item->setBackground(QBrush());
                }
            }
        }
        return;
    }

    // 遍历所有行进行过滤
    for (int row = 0; row < table->rowCount(); ++row) {
        bool matchBrp = true;
        bool matchTseg1 = true;
        bool matchTseg2 = true;

        // 检查BRP匹配
        if (brpValue != -1) {
            QTableWidgetItem* brpItem = table->item(row, 1); // BRP在第2列（索引1）
            if (brpItem) {
                int tableBrp = brpItem->text().toInt();
                matchBrp = (tableBrp == brpValue);
            } else {
                matchBrp = false;
            }
        }

        // 检查TSEG1匹配
        if (tseg1Value != -1) {
            QTableWidgetItem* tseg1Item = table->item(row, 2); // TSEG1在第3列（索引2）
            if (tseg1Item) {
                int tableTseg1 = tseg1Item->text().toInt();
                matchTseg1 = (tableTseg1 == tseg1Value);
            } else {
                matchTseg1 = false;
            }
        }

        // 检查TSEG2匹配
        if (tseg2Value != -1) {
            QTableWidgetItem* tseg2Item = table->item(row, 3); // TSEG2在第4列（索引3）
            if (tseg2Item) {
                int tableTseg2 = tseg2Item->text().toInt();
                matchTseg2 = (tableTseg2 == tseg2Value);
            } else {
                matchTseg2 = false;
            }
        }

        // 判断是否匹配所有条件
        bool isMatch = matchBrp && matchTseg1 && matchTseg2;

        // 根据匹配结果设置行的显示状态和背景色
        if (isMatch) {
            table->setRowHidden(row, false);
            // 高亮匹配的行
            for (int col = 0; col < table->columnCount(); ++col) {
                QTableWidgetItem* item = table->item(row, col);
                if (item) {
                    item->setBackground(QBrush(QColor(255, 255, 0, 100))); // 淡黄色高亮
                }
            }
        } else {
            // 如果有任何过滤条件，隐藏不匹配的行
            if (brpValue != -1 || tseg1Value != -1 || tseg2Value != -1) {
                table->setRowHidden(row, true);
            } else {
                table->setRowHidden(row, false);
                // 清除背景色
                for (int col = 0; col < table->columnCount(); ++col) {
                    QTableWidgetItem* item = table->item(row, col);
                    if (item) {
                        item->setBackground(QBrush());
                    }
                }
            }
        }
    }
}

/**
 * @brief 计算所有可能的波特率配置
 * @param targetBaud 目标波特率 (bps)
 * @param maxDiff 最大允许误差 (%)
 * @param isDataSegment 是否为数据段 (影响参数范围)
 * @return 可能的配置列表，按误差排序
 */
QVector<BaudrateResult> BaudrateCalculator::calculatePossibleConfigurations(int targetBaud, double maxDiff, bool isDataSegment)
{
    QVector<BaudrateResult> results;

    // 根据是否为数据段设置不同的参数范围
    int maxBrp = isDataSegment ? 32 : 64;      // BRP范围：仲裁段1-64，数据段1-32
    int maxTseg1 = isDataSegment ? 16 : 16;    // TSEG1范围：1-16
    int maxTseg2 = isDataSegment ? 8 : 8;      // TSEG2范围：1-8
    int maxSjw = isDataSegment ? 4 : 4;        // SJW范围：1-4

    // 遍历所有可能的参数组合
    for (int brp = 1; brp <= maxBrp; ++brp) {
        for (int tseg1 = 1; tseg1 <= maxTseg1; ++tseg1) {
            for (int tseg2 = 1; tseg2 <= maxTseg2; ++tseg2) {
                for (int sjw = 1; sjw <= qMin(maxSjw, tseg2); ++sjw) {
                    // 计算实际波特率
                    int tq = clockFrequency / brp;  // 时间量子频率
                    int bitTime = 1 + tseg1 + tseg2; // 总位时间
                    int actualBaud = tq / bitTime;

                    // 计算误差
                    double error = ((double)(actualBaud - targetBaud) / targetBaud) * 100.0;

                    // 只保留误差在允许范围内的配置
                    if (qAbs(error) <= maxDiff) {
                        BaudrateResult result;
                        result.brp = brp;
                        result.tseg1 = tseg1;
                        result.tseg2 = tseg2;
                        result.sjw = sjw;
                        result.actualBaud = actualBaud;
                        result.error = error;
                        result.samplePoint = ((double)(1 + tseg1) / bitTime) * 100.0;

                        results.append(result);
                    }
                }
            }
        }
    }

    // 按误差绝对值排序
    std::sort(results.begin(), results.end(), [](const BaudrateResult& a, const BaudrateResult& b) {
        return qAbs(a.error) < qAbs(b.error);
    });

    return results;
}

/**
 * @brief 计算Value十六进制值
 * @param brp 波特率预分频器
 * @param tseg1 时间段1
 * @param tseg2 时间段2
 * @param sjw 同步跳转宽度
 * @return 十六进制Value字符串
 */
QString BaudrateCalculator::calculateValueHex(int brp, int tseg1, int tseg2, int sjw)
{
    // 根据CAN协议计算Value值
    // 这里使用简化的计算方法，实际的Value值格式可能因硬件而异

    // 将参数组合成32位值
    quint32 value = 0;

    // BRP占低8位 (减1因为寄存器值从0开始)
    value |= (brp - 1) & 0xFF;

    // TSEG1占次低8位 (减1因为寄存器值从0开始)
    value |= ((tseg1 - 1) & 0xFF) << 8;

    // TSEG2占第三个8位 (减1因为寄存器值从0开始)
    value |= ((tseg2 - 1) & 0xFF) << 16;

    // SJW占最高8位 (减1因为寄存器值从0开始)
    value |= ((sjw - 1) & 0xFF) << 24;

    // 转换为8位十六进制字符串
    return QString("%1").arg(value, 8, 16, QChar('0')).toUpper();
}
