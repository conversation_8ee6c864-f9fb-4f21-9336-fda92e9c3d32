/**
 * @file caninterface.cpp
 * @brief CAN通信接口类实现文件
 * @details 实现了CANInterface类的所有功能，包括设备连接管理、数据收发、
 *          定时器控制、频率调节、日志记录等完整的CAN通信功能。
 *          集成ZLGCAN SDK实现与硬件设备的通信。
 * <AUTHOR>
 * @date 2025-07-02
 * @version 1.0
 */

#include "caninterface.h"        // CAN接口类声明
#include "ui_caninterface.h"     // Qt Designer生成的UI类
#include "messagebox_utils.h"    // 统一消息框工具类
#include <QDebug>                // Qt调试输出类
#include <QRegularExpression>    // Qt正则表达式类
#include <QTimer>                // Qt定时器类
#include <stdexcept>             // C++标准异常类

/**
 * @brief CANInterface构造函数实现
 * @param parent 父窗口指针
 * @details 初始化CAN通信接口的所有组件和设置：
 *          1. 初始化成员变量和状态
 *          2. 设置UI界面
 *          3. 建立信号槽连接
 *          4. 创建和配置定时器
 *          5. 更新控件状态
 */
CANInterface::CANInterface(QWidget *parent)
    : BaseCANInterface(parent)                          // 调用基类构造函数
    , ui(new Ui::CANInterface)                          // 创建UI实例
{
    qDebug() << "CANInterface: 开始构造函数";

    try {
        // 设置UI界面，加载由Qt Designer设计的界面布局
        ui->setupUi(this);
        qDebug() << "CANInterface: UI设置完成";

        // 设置信号槽连接
        setupConnections();
        qDebug() << "CANInterface: 连接设置完成";

        // 更新控件状态
        updateControlState();
        qDebug() << "CANInterface: 控制状态更新完成";

        // === 设置文本区域字体 ===
        // 确保发送和接收文本区域使用合适的字体大小
        QFont textFont("Consolas", 14);
        ui->sendTextEdit->setFont(textFont);
        ui->receiveTextEdit->setFont(textFont);
        qDebug() << "CANInterface: 文本区域字体设置完成";

        // === 定时器已在基类中初始化，这里只需要连接特定的槽函数 ===
        // 添加空指针检查，确保定时器已正确初始化
        if (continuousSendTimer) {
            connect(continuousSendTimer, &QTimer::timeout, this, &CANInterface::onContinuousSendTimer);
        } else {
            qDebug() << "CANInterface: 警告 - continuousSendTimer为空，重新初始化定时器";
            // 如果定时器为空，重新初始化
            initializeTimers();
            connectTimerSignals();
            if (continuousSendTimer) {
                connect(continuousSendTimer, &QTimer::timeout, this, &CANInterface::onContinuousSendTimer);
            }
        }

        qDebug() << "CANInterface: 构造函数完成";
    } catch (...) {
        qDebug() << "CANInterface: 构造函数中发生异常";
    }
}

/**
 * @brief CANInterface析构函数实现
 * @details 清理资源和断开连接：
 *          1. 检查设备连接状态
 *          2. 如果已连接则停止通信
 *          3. 释放UI资源
 */
CANInterface::~CANInterface()
{
    // 如果设备仍处于连接状态，先停止通信
    if (isConnected) {
        stopCommunication();
    }
    // 释放UI资源
    delete ui;
}

/**
 * @brief 设置信号槽连接
 * @details 连接UI控件的信号到相应的槽函数，建立用户交互响应机制：
 *          1. 连接设备控制按钮
 *          2. 连接数据操作按钮
 *          3. 连接频率控制组件
 *          4. 连接预设选择组件
 */
void CANInterface::setupConnections()
{
    qDebug() << "CANInterface: 开始设置连接";

    try {
        // === 设备控制按钮连接 ===
        connect(ui->connectButton, &QPushButton::clicked, this, &CANInterface::onConnectDevice);
        connect(ui->disconnectButton, &QPushButton::clicked, this, &CANInterface::onDisconnectDevice);

        // === 数据操作按钮连接 ===
        connect(ui->sendButton, &QPushButton::clicked, this, &CANInterface::onSendData);
        connect(ui->continuousSendButton, &QPushButton::clicked, this, &CANInterface::onContinuousSend);
        connect(ui->heartbeatButton, &QPushButton::clicked, this, &CANInterface::onSendHeartbeat);

        // === 界面控制按钮连接 ===
        connect(ui->clearSendButton, &QPushButton::clicked, this, &CANInterface::onClearSendArea);
        connect(ui->clearReceiveButton, &QPushButton::clicked, this, &CANInterface::onClearReceiveArea);
        connect(ui->saveDataButton, &QPushButton::clicked, this, &CANInterface::onSaveData);
        connect(ui->backButton, &QPushButton::clicked, this, &CANInterface::onBackClicked);

        // === 频率控制SpinBox连接 ===
        // 连接发送频率SpinBox的值变化信号
        connect(ui->sendFrequencySpinBox, QOverload<int>::of(&QSpinBox::valueChanged),
                this, &CANInterface::onSendFrequencyChanged);
        // 连接接收频率SpinBox的值变化信号
        connect(ui->receiveFrequencySpinBox, QOverload<int>::of(&QSpinBox::valueChanged),
                this, &CANInterface::onReceiveFrequencyChanged);

        // === 频率预设ComboBox连接 ===
        // 连接发送频率预设下拉框的文本变化信号
        connect(ui->sendFrequencyPresetCombo, &QComboBox::currentTextChanged,
                this, &CANInterface::onSendFrequencyPresetChanged);
        // 连接接收频率预设下拉框的文本变化信号
        connect(ui->receiveFrequencyPresetCombo, &QComboBox::currentTextChanged,
                this, &CANInterface::onReceiveFrequencyPresetChanged);

        // 注意：定时器信号连接在构造函数中单独处理，因为需要先创建定时器实例
        qDebug() << "CANInterface: 连接设置完成";
    } catch (...) {
        qDebug() << "CANInterface: 连接设置时发生异常";
    }
}

/**
 * @brief 设置配置数据
 * @param config CAN配置数据结构
 * @details 保存用户配置的CAN参数到内部变量，供后续通信使用
 */
void CANInterface::setConfigData(const CANConfigData &config)
{
    // 保存配置数据到成员变量
    configData = config;
}

/**
 * @brief 更新控件状态
 * @details 根据当前连接状态和运行状态更新UI控件的启用/禁用状态和显示文本：
 *          1. 更新连接/断开按钮状态
 *          2. 更新数据操作按钮状态
 *          3. 更新心跳按钮状态和样式
 *          4. 更新状态标签显示
 */
void CANInterface::updateControlState()
{
    // === 设备连接按钮状态控制 ===
    ui->connectButton->setEnabled(!isConnected);      // 未连接时启用连接按钮
    ui->disconnectButton->setEnabled(isConnected);    // 已连接时启用断开按钮

    // === 数据操作按钮状态控制 ===
    ui->sendButton->setEnabled(isConnected);          // 已连接时启用发送按钮
    ui->continuousSendButton->setEnabled(isConnected); // 已连接时启用连续发送按钮

    // === 心跳按钮状态和样式控制 ===
    if (isHeartbeatRunning) {
        // 心跳运行时显示停止按钮，使用红色样式
        ui->heartbeatButton->setText("停止心跳包发送");
        ui->heartbeatButton->setStyleSheet("QPushButton { background-color: #ff6b6b; color: white; font-weight: bold; padding: 8px; }");
    } else {
        // 心跳停止时显示启动按钮，使用橙色样式
        ui->heartbeatButton->setText("发送心跳包");
        ui->heartbeatButton->setStyleSheet("QPushButton { background-color: #FF9800; color: white; font-weight: bold; padding: 8px; }");
    }
    ui->heartbeatButton->setEnabled(isConnected);     // 已连接时启用心跳按钮

    // === 状态标签显示控制 ===
    if (isConnected) {
        // 已连接状态：绿色文本
        ui->statusLabel->setText("已连接");
        ui->statusLabel->setStyleSheet("color: green; font-weight: bold;");
    } else {
        // 未连接状态：红色文本
        ui->statusLabel->setText("未连接");
        ui->statusLabel->setStyleSheet("color: red; font-weight: bold;");
    }
}

/**
 * @brief 连接设备槽函数实现
 * @details 智能连接策略：优先尝试真实硬件设备，失败时提供模拟模式选项。
 *          实现步骤：
 *          1. 加载ZLGCAN动态库
 *          2. 获取API函数指针
 *          3. 打开设备并获取设备信息
 *          4. 配置CAN通道参数
 *          5. 初始化并启动通道
 *          6. 启动通信或切换到模拟模式
 */
void CANInterface::onConnectDevice()
{
    // 智能连接标志：优先尝试真实设备，失败时提供模拟模式选项
    bool useRealDevice = false;

    // === 第一步：尝试加载ZLGCAN动态库 ===
    if (zlgcanLib.isLoaded() || loadZLGCANLibrary()) {
        // 尝试真实设备连接
        try {
            // === 第二步：获取ZLGCAN API函数指针 ===
            auto ZCAN_OpenDevice = (DEVICE_HANDLE(*)(UINT, UINT, UINT))zlgcanLib.resolve("ZCAN_OpenDevice");
            auto ZCAN_GetDeviceInf = (UINT(*)(DEVICE_HANDLE, ZCAN_DEVICE_INFO*))zlgcanLib.resolve("ZCAN_GetDeviceInf");
            auto ZCAN_InitCAN = (CHANNEL_HANDLE(*)(DEVICE_HANDLE, UINT, ZCAN_CHANNEL_INIT_CONFIG*))zlgcanLib.resolve("ZCAN_InitCAN");
            auto ZCAN_StartCAN = (UINT(*)(CHANNEL_HANDLE))zlgcanLib.resolve("ZCAN_StartCAN");

            // 检查所有必需的API函数是否成功获取
            if (ZCAN_OpenDevice && ZCAN_GetDeviceInf && ZCAN_InitCAN && ZCAN_StartCAN) {
                // === 第三步：打开ZLGCANFD100U设备 ===
                deviceHandle = ZCAN_OpenDevice(ZCAN_USBCANFD_100U, 0, 0);
                if (deviceHandle != INVALID_DEVICE_HANDLE) {
                    // === 第四步：获取并显示设备信息 ===
                    ZCAN_DEVICE_INFO deviceInfo;
                    if (ZCAN_GetDeviceInf(deviceHandle, &deviceInfo) == STATUS_OK) {
                        appendLog(QString("设备信息: %1").arg(QString::fromLocal8Bit((char*)deviceInfo.str_Serial_Num)), false);
                    }

                    // === 第五步：配置CAN通道参数 ===
                    ZCAN_CHANNEL_INIT_CONFIG config;
                    memset(&config, 0, sizeof(config));        // 清零配置结构
                    config.can_type = TYPE_CAN;                // 设置为CAN类型
                    config.can.mode = 0;                       // 设置为正常模式

                    // === 根据配置数据设置波特率时序参数 ===
                    if (configData.arbitrationBaud == "1000Kbps") {
                        config.can.timing0 = 0x00;             // 1Mbps波特率时序参数
                        config.can.timing1 = 0x14;
                    } else if (configData.arbitrationBaud == "500Kbps") {
                        config.can.timing0 = 0x00;             // 500Kbps波特率时序参数
                        config.can.timing1 = 0x1C;
                    } else if (configData.arbitrationBaud == "250Kbps") {
                        config.can.timing0 = 0x01;             // 250Kbps波特率时序参数
                        config.can.timing1 = 0x1C;
                    } else {
                        config.can.timing0 = 0x00;             // 默认使用500Kbps
                        config.can.timing1 = 0x1C;
                    }

                    // === 第六步：初始化CAN通道 ===
                    channelHandle = ZCAN_InitCAN(deviceHandle, 0, &config);
                    if (channelHandle != INVALID_CHANNEL_HANDLE) {
                        // === 第七步：启动CAN通道 ===
                        if (ZCAN_StartCAN(channelHandle) == STATUS_OK) {
                            useRealDevice = true;                    // 标记使用真实设备
                            appendLog("CAN设备连接成功 (真实设备)", false);
                        }
                    }
                }
            }
        } catch (...) {
            // 真实设备连接过程中发生异常，继续尝试模拟模式
        }
    }

    // === 第八步：处理真实设备连接失败的情况 ===
    if (!useRealDevice) {
        // 使用统一的消息框样式询问用户是否使用模拟模式
        QString message = "无法连接到真实的CANFD100U设备！\n\n"
                         "可能原因：\n"
                         "• 设备未连接或驱动未安装\n"
                         "• zlgcan.dll文件缺失\n"
                         "• 设备被其他程序占用\n\n"
                         "是否使用模拟模式进行测试？";

        int ret = MessageBoxUtils::question(this, "设备连接", message);

        // 如果用户选择不使用模拟模式，则退出连接过程
        if (ret == QMessageBox::No) {
            return;
        }

        // 用户选择使用模拟模式
        appendLog("CAN设备连接成功 (模拟模式)", false);
    }

    // === 第九步：设置连接状态并启动通信 ===
    isConnected = true;                    // 设置连接状态标志
    updateControlState();                  // 更新UI控件状态
    startCommunication();                  // 启动数据通信

    // === 记录配置信息到日志 ===
    appendLog(QString("仲裁域波特率: %1").arg(configData.arbitrationBaud), false);
    appendLog(QString("工作模式: %1").arg(configData.workMode), false);

    // === 显示连接成功提示 ===
    MessageBoxUtils::information(this, "提示",
        useRealDevice ? "CAN设备连接成功！" : "CAN设备连接成功！(模拟模式)");
}

/**
 * @brief 断开设备槽函数实现
 * @details 安全断开CAN设备连接，清理所有资源：
 *          1. 停止心跳功能
 *          2. 重置和关闭硬件设备
 *          3. 停止通信定时器
 *          4. 更新界面状态
 */
void CANInterface::onDisconnectDevice()
{
    // === 第一步：停止心跳功能 ===
    if (isHeartbeatRunning) {
        onSendHeartbeat(); // 调用心跳函数停止心跳包发送
    }

    // === 第二步：断开真实硬件设备 ===
    if (zlgcanLib.isLoaded() && deviceHandle != INVALID_DEVICE_HANDLE) {
        // 获取设备控制API函数指针
        auto ZCAN_ResetCAN = (UINT(*)(CHANNEL_HANDLE))zlgcanLib.resolve("ZCAN_ResetCAN");
        auto ZCAN_CloseDevice = (UINT(*)(DEVICE_HANDLE))zlgcanLib.resolve("ZCAN_CloseDevice");

        // 重置CAN通道
        if (ZCAN_ResetCAN && channelHandle != INVALID_CHANNEL_HANDLE) {
            ZCAN_ResetCAN(channelHandle);
        }

        // 关闭设备
        if (ZCAN_CloseDevice) {
            ZCAN_CloseDevice(deviceHandle);
        }

        // 清空设备句柄
        deviceHandle = INVALID_DEVICE_HANDLE;
        channelHandle = INVALID_CHANNEL_HANDLE;
    }

    // === 第三步：停止通信和更新状态 ===
    stopCommunication();                   // 停止所有定时器
    isConnected = false;                   // 设置连接状态为未连接
    updateControlState();                  // 更新UI控件状态

    // === 第四步：记录日志和提示用户 ===
    appendLog("CAN设备已断开连接", false);
    MessageBoxUtils::information(this, "提示", "CAN设备已断开连接！");
}

/**
 * @brief 发送数据槽函数实现
 * @details 发送单次CAN数据帧：
 *          1. 检查设备连接状态
 *          2. 验证输入数据有效性
 *          3. 解析CAN ID和数据
 *          4. 发送数据帧并记录日志
 */
void CANInterface::onSendData()
{
    // === 第一步：检查设备连接状态 ===
    if (!isConnected) {
        MessageBoxUtils::warning(this, "警告", "请先连接设备！");
        return;
    }

    // === 第二步：获取用户输入数据 ===
    QString canId = ui->canIdEdit->text().trimmed();      // 获取CAN ID
    QString data = ui->sendDataEdit->text().trimmed();    // 获取发送数据

    // === 第三步：验证输入数据有效性 ===
    if (canId.isEmpty()) {
        MessageBoxUtils::warning(this, "警告", "请输入CAN ID！");
        return;
    }

    if (data.isEmpty()) {
        MessageBoxUtils::warning(this, "警告", "请输入发送数据！");
        return;
    }

    // === 第四步：验证数据长度（CAN协议限制） ===
    QStringList dataBytes = data.split(' ', Qt::SkipEmptyParts);  // 按空格分割数据字节
    if (dataBytes.size() > 8) {
        MessageBoxUtils::warning(this, "警告", "CAN协议最多支持8字节数据！");
        return;
    }

    // === 第五步：执行数据发送和日志记录 ===
    sendCount++;                                                  // 增加发送计数
    QString logText = QString("[%1] ID:%2 数据:%3 (%4字节)")
                     .arg(QTime::currentTime().toString("HH:mm:ss.zzz"))  // 当前时间戳
                     .arg(canId.toUpper())                                 // CAN ID（大写）
                     .arg(data.toUpper())                                  // 发送数据（大写）
                     .arg(dataBytes.size());                               // 数据字节数

    // 记录发送日志
    appendLog(logText, false);

    // === 第六步：模拟接收回应数据 ===
    // 使用单次定时器模拟设备回应（延迟100ms）
    QTimer::singleShot(100, [this, canId]() {
        QString responseData = "AA BB CC DD";                     // 模拟回应数据
        QString responseLog = QString("[%1] ID:%2 数据:%3 (回应)")
                             .arg(QTime::currentTime().toString("HH:mm:ss.zzz"))
                             .arg(canId.toUpper())
                             .arg(responseData);
        appendLog(responseLog, true);                             // 记录接收日志
        receiveCount++;                                           // 增加接收计数
    });
}

/**
 * @brief 连续发送槽函数实现
 * @details 控制连续发送功能的启动和停止：
 *          1. 检查设备连接状态
 *          2. 切换连续发送状态
 *          3. 启动或停止连续发送定时器
 *          4. 更新按钮状态和样式
 */
void CANInterface::onContinuousSend()
{
    // === 第一步：检查设备连接状态 ===
    if (!isConnected) {
        MessageBoxUtils::warning(this, "警告", "请先连接设备！");
        return;
    }

    // === 第二步：根据当前状态切换连续发送功能 ===
    if (isContinuousSending) {
        // === 停止连续发送 ===
        if (continuousSendTimer->isActive()) {
            continuousSendTimer->stop();                          // 停止定时器
        }
        isContinuousSending = false;                              // 更新状态标志
        ui->continuousSendButton->setText("连续发送");            // 恢复按钮文本
        ui->continuousSendButton->setStyleSheet("QPushButton { background-color: #4CAF50; color: white; font-weight: bold; padding: 8px; }");
        appendLog("停止连续发送", false);                         // 记录日志
    } else {
        // === 开始连续发送 ===
        isContinuousSending = true;                               // 更新状态标志
        ui->continuousSendButton->setText("停止发送");            // 更改按钮文本
        ui->continuousSendButton->setStyleSheet("QPushButton { background-color: #f44336; color: white; font-weight: bold; padding: 8px; }");

        // 设置发送间隔频率
        int interval = ui->sendFrequencySpinBox->value();         // 获取用户设置的发送间隔
        continuousSendTimer->setInterval(interval);               // 设置定时器间隔
        continuousSendTimer->start();                             // 启动定时器

        appendLog(QString("开始连续发送，间隔: %1ms").arg(interval), false);
    }
}

/**
 * @brief 连续发送定时器槽函数实现
 * @details 连续发送定时器超时时调用，执行自动发送数据：
 *          1. 检查连续发送状态和设备连接
 *          2. 复用单次发送的数据处理逻辑
 *          3. 实现高频率的自动数据发送
 */
void CANInterface::onContinuousSendTimer()
{
    // 检查连续发送状态和设备连接状态
    if (isContinuousSending && isConnected) {
        // === 执行发送数据的逻辑（复用onSendData的核心逻辑） ===
        QString canId = ui->canIdEdit->text().trimmed();          // 获取CAN ID
        QString data = ui->sendDataEdit->text().trimmed();        // 获取发送数据

        // 使用默认值处理空输入
        if (canId.isEmpty()) {
            canId = "123";                                        // 默认CAN ID
        }

        if (data.isEmpty()) {
            data = "00 11 22 33 44 55 66 77";                    // 默认测试数据
        }

        // === 验证和转换数据格式 ===
        QStringList dataList = data.split(' ', Qt::SkipEmptyParts);  // 分割数据字节
        QByteArray dataBytes;                                     // 存储转换后的字节数据

        // 逐个验证和转换十六进制字符串为字节
        for (const QString &byte : dataList) {
            bool ok;
            quint8 value = byte.toUInt(&ok, 16);                  // 十六进制转换为数值
            if (!ok) {
                return;                                           // 数据格式错误，跳过本次发送
            }
            dataBytes.append(value);                              // 添加到字节数组
        }

        // 检查CAN协议数据长度限制
        if (dataBytes.size() > 8) {
            return;                                               // CAN数据长度超限，跳过本次发送
        }

        // === 执行模拟发送并记录日志 ===
        sendCount++;                                              // 增加发送计数
        QString logText = QString("[%1] ID:%2 数据:%3 (%4字节) [连续]")
                         .arg(QTime::currentTime().toString("HH:mm:ss.zzz"))  // 时间戳
                         .arg(canId.toUpper())                                 // CAN ID
                         .arg(data.toUpper())                                  // 发送数据
                         .arg(dataBytes.size());                               // 数据长度

        appendLog(logText, false);                                // 记录发送日志（标记为连续发送）
    }
}

/**
 * @brief 心跳包发送槽函数实现
 * @details 控制心跳包的启动和停止功能：
 *          1. 检查设备连接状态
 *          2. 切换心跳包发送状态
 *          3. 启动或停止心跳定时器
 *          4. 更新控件状态
 */
void CANInterface::onSendHeartbeat()
{
    // === 第一步：检查设备连接状态 ===
    if (!isConnected) {
        MessageBoxUtils::warning(this, "警告", "请先连接设备！");
        return;
    }

    // === 第二步：根据当前状态切换心跳功能 ===
    if (isHeartbeatRunning) {
        // === 停止心跳包发送 ===
        if (heartbeatTimer->isActive()) {
            heartbeatTimer->stop();                               // 停止心跳定时器
        }
        isHeartbeatRunning = false;                               // 更新心跳状态标志
        updateControlState();                                     // 更新控件状态
        appendLog("停止发送心跳包", false);                       // 记录日志
    } else {
        // === 开始心跳包发送 ===
        isHeartbeatRunning = true;                                // 更新心跳状态标志
        heartbeatTimer->setInterval(ui->sendFrequencySpinBox->value());  // 设置心跳间隔
        heartbeatTimer->start();                                  // 启动心跳定时器
        updateControlState();                                     // 更新控件状态
        appendLog("开始发送心跳包", false);                       // 记录日志
    }
}

/**
 * @brief 清空发送区域槽函数实现
 * @details 清空发送数据显示区域并重置发送计数器
 */
void CANInterface::onClearSendArea()
{
    ui->sendTextEdit->clear();                                    // 清空发送文本显示区域
    sendCount = 0;                                                // 重置发送计数器
}

/**
 * @brief 清空接收区域槽函数实现
 * @details 清空接收数据显示区域并重置接收计数器
 */
void CANInterface::onClearReceiveArea()
{
    ui->receiveTextEdit->clear();                                 // 清空接收文本显示区域
    receiveCount = 0;                                             // 重置接收计数器
}

/**
 * @brief 保存数据槽函数实现
 * @details 将CAN通信数据保存到文本文件：
 *          1. 弹出文件保存对话框
 *          2. 创建包含时间戳的文件名
 *          3. 保存发送和接收数据及统计信息
 *          4. 提供用户反馈
 */
void CANInterface::onSaveData()
{
    // === 第一步：弹出文件保存对话框 ===
    QString fileName = QFileDialog::getSaveFileName(this,
                                                   "保存CAN通信数据",                    // 对话框标题
                                                   QString("CAN_Data_%1.txt")           // 默认文件名（包含时间戳）
                                                   .arg(QDateTime::currentDateTime().toString("yyyyMMdd_HHmmss")),
                                                   "文本文件 (*.txt)");                 // 文件类型过滤器

    // === 第二步：执行文件保存操作 ===
    if (!fileName.isEmpty()) {
        QFile file(fileName);                                     // 创建文件对象
        if (file.open(QIODevice::WriteOnly | QIODevice::Text)) {  // 以写入模式打开文件
            QTextStream out(&file);                               // 创建文本流

            // === 写入文件头信息 ===
            out << "=== CAN通信数据记录 ===\n";
            out << QString("保存时间: %1\n").arg(QDateTime::currentDateTime().toString("yyyy-MM-dd HH:mm:ss"));
            out << QString("发送计数: %1\n").arg(sendCount);      // 发送数据统计
            out << QString("接收计数: %1\n\n").arg(receiveCount); // 接收数据统计

            // === 写入发送数据 ===
            out << "=== 发送数据 ===\n";
            out << ui->sendTextEdit->toPlainText() << "\n\n";     // 发送区域的所有文本

            // === 写入接收数据 ===
            out << "=== 接收数据 ===\n";
            out << ui->receiveTextEdit->toPlainText() << "\n";    // 接收区域的所有文本

            MessageBoxUtils::information(this, "提示", "数据保存成功！");
        } else {
            MessageBoxUtils::warning(this, "错误", "无法保存文件！");
        }
    }
}

/**
 * @brief 返回按钮槽函数实现
 * @details 处理返回到协议选择界面的操作：
 *          1. 检查设备连接状态并断开连接
 *          2. 发出返回协议选择界面的信号
 */
void CANInterface::onBackClicked()
{
    // 如果设备已连接，先断开连接
    if (isConnected) {
        onDisconnectDevice();
    }

    // 发出返回协议选择界面的信号
    emit backToProtocolSelect();
}

/**
 * @brief 心跳定时器槽函数实现
 * @details 心跳定时器超时时调用，发送心跳数据包：
 *          1. 检查心跳状态和设备连接
 *          2. 发送固定的心跳数据包
 *          3. 记录心跳发送日志
 */
void CANInterface::onHeartbeatTimer()
{
    // === 第一步：准备心跳数据包 ===
    QString heartbeatData = "FF FF FF FF";                        // 固定的心跳数据包内容
    QString canId = ui->canIdEdit->text().isEmpty() ? "123" : ui->canIdEdit->text();  // 使用用户设置的ID或默认ID

    // === 第二步：发送心跳包并记录日志 ===
    sendCount++;                                                  // 增加发送计数
    QString logText = QString("[%1] ID:%2 数据:%3 (心跳包)")
                     .arg(QTime::currentTime().toString("HH:mm:ss.zzz"))  // 时间戳
                     .arg(canId.toUpper())                                 // CAN ID
                     .arg(heartbeatData);                                  // 心跳数据

    appendLog(logText, false);                                    // 记录心跳发送日志
}

/**
 * @brief 接收定时器槽函数实现
 * @details 接收定时器超时时调用，处理CAN数据接收：
 *          1. 检查设备连接状态
 *          2. 尝试从真实硬件设备接收数据
 *          3. 如果硬件不可用，使用模拟数据
 *          4. 格式化并显示接收到的数据
 */
void CANInterface::onReceiveTimer()
{
    // === 第一步：检查设备连接状态 ===
    if (!isConnected) {
        return;                                                   // 设备未连接，直接返回
    }

    // === 第二步：尝试从真实设备接收数据 ===
    if (zlgcanLib.isLoaded() && channelHandle != INVALID_CHANNEL_HANDLE) {
        // 获取ZLGCAN API函数指针
        auto ZCAN_Receive = (UINT(*)(CHANNEL_HANDLE, ZCAN_Receive_Data*, UINT, int))zlgcanLib.resolve("ZCAN_Receive");
        auto ZCAN_GetReceiveNum = (UINT(*)(CHANNEL_HANDLE, BYTE))zlgcanLib.resolve("ZCAN_GetReceiveNum");

        if (ZCAN_Receive && ZCAN_GetReceiveNum) {
            // === 检查接收缓冲区中的数据数量 ===
            UINT receiveNum = ZCAN_GetReceiveNum(channelHandle, TYPE_CAN);

            if (receiveNum > 0) {
                // === 接收数据帧 ===
                ZCAN_Receive_Data receiveData[10];                // 一次最多接收10帧数据
                UINT actualReceived = ZCAN_Receive(channelHandle, receiveData, qMin(receiveNum, 10U), 0);

                // === 处理每一帧接收到的数据 ===
                for (UINT i = 0; i < actualReceived; i++) {
                    // 格式化CAN ID（十六进制显示）
                    QString canId = QString("0x%1").arg(receiveData[i].frame.can_id, 0, 16).toUpper();

                    // 格式化数据字节（十六进制显示，空格分隔）
                    QString dataStr;
                    for (int j = 0; j < receiveData[i].frame.can_dlc; j++) {
                        dataStr += QString("%1 ").arg(receiveData[i].frame.data[j], 2, 16, QChar('0')).toUpper();
                    }
                    dataStr = dataStr.trimmed();                  // 移除末尾空格

                    // 创建接收日志文本
                    QString logText = QString("[%1] ID:%2 数据:%3 (%4字节) [接收]")
                                     .arg(QTime::currentTime().toString("HH:mm:ss.zzz"))  // 时间戳
                                     .arg(canId)                                           // CAN ID
                                     .arg(dataStr)                                         // 数据内容
                                     .arg(receiveData[i].frame.can_dlc);                   // 数据长度

                    appendLog(logText, true);                     // 记录接收日志（true表示接收数据）
                    receiveCount++;                               // 增加接收计数
                }
            }
        }
    } else {
        // === 第三步：模拟接收数据（用于测试和演示） ===
        static int dataCounter = 0;                               // 静态计数器，用于生成变化的数据

        // 每次定时器触发都模拟接收数据（频率由用户控制）
        dataCounter++;
        QString simulatedData = QString("12 34 56 78 00 00 01 %1")
                               .arg(dataCounter % 256, 2, 16, QChar('0')).toUpper();  // 生成模拟数据
        QString canId = "0x123";                                  // 模拟CAN ID

        // 创建模拟接收日志
        QString logText = QString("[%1] ID:%2 数据:%3 (8字节) [接收]")
                         .arg(QTime::currentTime().toString("HH:mm:ss.zzz"))  // 时间戳
                         .arg(canId)                                           // CAN ID
                         .arg(simulatedData);                                  // 模拟数据

        appendLog(logText, true);                             // 记录接收日志（true表示接收数据）
        receiveCount++;                                       // 增加接收计数
    }
}

/**
 * @brief 从文本中提取频率数值的工具函数
 * @param text 包含频率信息的文本字符串
 * @return 提取出的频率数值（毫秒）
 * @details 支持多种格式的频率文本解析：
 *          1. "1000 ms" 格式的文本
 *          2. 纯数字格式
 *          3. 解析失败时返回默认值1000ms
 */
int CANInterface::extractFrequencyValue(const QString &text)
{
    // === 第一步：尝试匹配 "数字 ms" 格式 ===
    QRegularExpression regex(R"((\d+)\s*ms)");               // 正则表达式匹配数字+ms
    QRegularExpressionMatch match = regex.match(text);
    if (match.hasMatch()) {
        return match.captured(1).toInt();                     // 返回捕获的数字部分
    }

    // === 第二步：尝试直接转换为整数 ===
    bool ok;
    int value = text.toInt(&ok);                              // 尝试直接转换为整数
    if (ok && value > 0) {
        return value;                                         // 返回转换后的数值
    }

    // === 第三步：返回默认值 ===
    return 1000;                                              // 默认返回1000ms
}

/**
 * @brief 发送频率改变槽函数实现
 * @details 当用户调整发送频率时调用：
 *          1. 获取新的频率设置
 *          2. 更新心跳定时器间隔
 *          3. 更新连续发送定时器间隔
 */
void CANInterface::onSendFrequencyChanged()
{
    // 获取用户设置的新频率值
    int frequency = ui->sendFrequencySpinBox->value();

    // === 更新心跳定时器间隔 ===
    if (isHeartbeatRunning) {
        heartbeatTimer->setInterval(frequency);               // 实时更新心跳间隔
    }

    // === 更新连续发送定时器间隔 ===
    if (isContinuousSending) {
        continuousSendTimer->setInterval(frequency);          // 实时更新连续发送间隔
    }
}

/**
 * @brief 接收频率改变槽函数实现
 * @details 当用户调整接收频率时调用：
 *          1. 获取新的接收频率设置
 *          2. 更新接收定时器间隔
 *          3. 记录频率调整日志
 */
void CANInterface::onReceiveFrequencyChanged()
{
    // 获取用户设置的新接收频率
    int newInterval = ui->receiveFrequencySpinBox->value();

    // 更新接收定时器间隔
    receiveTimer->setInterval(newInterval);

    // 记录频率调整日志
    QString logText = QString("接收频率已调整为: %1ms").arg(newInterval);
    appendLog(logText, false);

    // 调试输出
    qDebug() << "CAN接收频率调整为:" << newInterval << "ms";
}

/**
 * @brief 发送频率预设选择槽函数实现
 * @details 当用户从预设下拉框选择频率时调用：
 *          1. 获取选择的预设文本
 *          2. 提取频率数值
 *          3. 更新SpinBox控件的值
 */
void CANInterface::onSendFrequencyPresetChanged()
{
    // 获取用户选择的预设文本
    QString selectedText = ui->sendFrequencyPresetCombo->currentText();

    // 如果选择的是默认提示文本，则不做任何操作
    if (selectedText == "常用频率") {
        return;
    }

    // === 从选择的文本中提取频率值并设置到SpinBox ===
    int frequency = extractFrequencyValue(selectedText);          // 提取频率数值
    if (frequency > 0) {
        ui->sendFrequencySpinBox->setValue(frequency);            // 设置SpinBox的值

        // 记录频率设置日志
        QString logText = QString("发送频率已设置为: %1ms").arg(frequency);
        appendLog(logText, false);

        // 调试输出
        qDebug() << "CAN发送频率预设选择:" << frequency << "ms";
    }
}

/**
 * @brief 接收频率预设选择槽函数实现
 * @details 当用户从接收频率预设下拉框选择频率时调用：
 *          1. 获取选择的预设文本
 *          2. 提取频率数值
 *          3. 更新接收频率SpinBox控件的值
 *          4. 记录频率调整日志
 */
void CANInterface::onReceiveFrequencyPresetChanged()
{
    // 获取用户选择的预设文本
    QString selectedText = ui->receiveFrequencyPresetCombo->currentText();

    // 如果选择的是默认提示文本，则不做任何操作
    if (selectedText == "常用频率") {
        return;
    }

    // === 从选择的文本中提取频率值并设置到SpinBox ===
    int frequency = extractFrequencyValue(selectedText);          // 提取频率数值
    if (frequency > 0) {
        ui->receiveFrequencySpinBox->setValue(frequency);         // 设置SpinBox的值

        // 记录频率设置日志
        QString logText = QString("接收频率已设置为: %1ms").arg(frequency);
        appendLog(logText, false);

        // 调试输出
        qDebug() << "CAN接收频率预设选择:" << frequency << "ms";
    }
}

// appendLog函数已在BaseCANInterface基类中实现

// startCommunication和stopCommunication函数已在BaseCANInterface基类中实现

/**
 * @brief 获取目标文本编辑器
 * @param isReceived 是否为接收数据
 * @return 对应的文本编辑器指针
 */
QTextEdit* CANInterface::getTargetTextEdit(bool isReceived)
{
    return isReceived ? ui->receiveTextEdit : ui->sendTextEdit;
}

/**
 * @brief 尝试连接真实设备
 * @return 是否连接成功
 */
bool CANInterface::tryConnectRealDevice()
{
    // 这里实现真实设备连接逻辑
    // 暂时返回false，使用模拟模式
    return false;
}

// updateControlState函数已在文件前面定义

/**
 * @brief 启动通信（重写基类方法）
 */
void CANInterface::startCommunication()
{
    BaseCANInterface::startCommunication();
}

/**
 * @brief 停止通信（重写基类方法）
 */
void CANInterface::stopCommunication()
{
    BaseCANInterface::stopCommunication();
}

/**
 * @brief 添加日志（重写基类方法）
 */
void CANInterface::appendLog(const QString &text, bool isReceived)
{
    BaseCANInterface::appendLog(text, isReceived);
}
