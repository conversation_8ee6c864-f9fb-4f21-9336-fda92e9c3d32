/**
 * @file multimotorcontrol_pure.cpp
 * @brief 纯UI设计的多电机控制界面类实现
 * @details 使用Qt Designer设计的纯UI界面，所有控件都在UI文件中定义，
 *          避免代码动态生成控件可能导致的崩溃问题
 * <AUTHOR>
 * @date 2025-01-28
 * @version 1.0
 */

#include "multimotorcontrol_pure.h"
#include "ui_multimotorcontrol_pure.h"
#include <QLabel>
#include <QPushButton>
#include <QComboBox>
#include <QSpinBox>
#include <QGroupBox>
#include <QDebug>
#include <QString>

/**
 * @brief 构造函数
 * @param parent 父窗口指针
 */
MultiMotorControlPure::MultiMotorControlPure(QWidget *parent)
    : QWidget(parent)
    , ui(new Ui::MultiMotorControlPure)
    , m_statusTimer(nullptr)
    , m_deviceManager(nullptr)
    , m_currentDataSource(DataSourceType::SIMULATION)
    , m_useRealData(false)
{
    try {
        // 初始化电机状态数组
        for (int i = 0; i < 6; i++) {
            m_motorRunning[i] = false;
            m_motorEnabled[i] = false;
            m_motorSpeed[i] = 0;
            m_motorPosition[i] = 0;
            m_motorTargetSpeed[i] = 1000;  // 默认目标速度1000 RPM
        }

        // 设置UI
        ui->setupUi(this);

        // 获取设备管理器实例
        m_deviceManager = CANFDDeviceManager::getInstance();

        // 连接设备管理器信号槽
        connect(m_deviceManager, &CANFDDeviceManager::motorStatusReceived,
                this, &MultiMotorControlPure::onMotorStatusReceived);
        connect(m_deviceManager, &CANFDDeviceManager::connectionStatusChanged,
                this, &MultiMotorControlPure::onConnectionStatusChanged);

        // 初始化界面（不连接信号槽）
        initializeUI();

        // 设置样式
        setupStyles();

        // 创建状态更新定时器
        m_statusTimer = new QTimer(this);
        m_statusTimer->setInterval(1000);  // 1秒更新一次
        connect(m_statusTimer, &QTimer::timeout, this, &MultiMotorControlPure::updateStatus);
        m_statusTimer->start();

    } catch (const std::exception& e) {
        qDebug() << "纯UI多电机控制界面创建异常:" << e.what();
    } catch (...) {
        qDebug() << "纯UI多电机控制界面创建发生未知异常";
    }
}

/**
 * @brief 析构函数
 */
MultiMotorControlPure::~MultiMotorControlPure()
{
    if (m_statusTimer) {
        m_statusTimer->stop();
    }
    delete ui;
}

/**
 * @brief 初始化界面
 * @details 设置初始状态，但不连接信号槽
 */
void MultiMotorControlPure::initializeUI()
{
    // 设置窗口标题
    setWindowTitle("六电机控制系统 - CANFD协议");

    // 初始化所有电机的状态显示
    for (int motorId = 1; motorId <= 6; motorId++) {
        // 构造控件名称
        QString statusValueName = QString("motor%1StatusValue").arg(motorId);
        QString speedValueName = QString("motor%1SpeedValue").arg(motorId);
        QString positionValueName = QString("motor%1PositionValue").arg(motorId);
        QString modeComboName = QString("motor%1ModeCombo").arg(motorId);
        QString targetSpeedSpinName = QString("motor%1TargetSpeedSpin").arg(motorId);

        // 查找并设置控件
        QLabel* statusLabel = findChild<QLabel*>(statusValueName);
        if (statusLabel) {
            statusLabel->setText("未连接");
            statusLabel->setStyleSheet("QLabel { color: #888888; font-weight: bold; }");
        }

        QLabel* speedLabel = findChild<QLabel*>(speedValueName);
        if (speedLabel) {
            speedLabel->setText("0 RPM");
        }

        QLabel* positionLabel = findChild<QLabel*>(positionValueName);
        if (positionLabel) {
            positionLabel->setText("0");
        }

        QComboBox* modeCombo = findChild<QComboBox*>(modeComboName);
        if (modeCombo) {
            modeCombo->setCurrentIndex(0);  // 默认速度控制
        }

        QSpinBox* targetSpeedSpin = findChild<QSpinBox*>(targetSpeedSpinName);
        if (targetSpeedSpin) {
            targetSpeedSpin->setValue(m_motorTargetSpeed[motorId-1]);
        }
    }

    qDebug() << "多电机控制界面初始化完成";
}

/**
 * @brief 设置样式
 * @details 应用额外的样式设置
 */
void MultiMotorControlPure::setupStyles()
{
    // 设置窗口最小尺寸
    setMinimumSize(1200, 800);
    
    // 确保电机组的样式正确应用
    QList<QGroupBox*> motorGroups = {
        ui->motor1Group
        // 其他电机组将在UI文件完善后添加
    };
    
    for (QGroupBox* group : motorGroups) {
        if (group) {
            // 确保分组框样式正确
            group->setStyleSheet(group->styleSheet());
        }
    }
    

}

/**
 * @brief 返回按钮点击槽函数实现
 * @details 响应用户点击返回按钮，发送返回主界面信号
 */
void MultiMotorControlPure::on_backBtn_clicked()
{
    qDebug() << "返回按钮点击";
    emit backToMainInterface();
}

// ================================
// 批量控制功能实现
// ================================

/**
 * @brief 全部启动按钮点击槽函数
 */
void MultiMotorControlPure::on_startAllBtn_clicked()
{
    qDebug() << "全部启动按钮点击";
    executeAllMotorsCommand(0x01);  // CMD_START
}

/**
 * @brief 全部停止按钮点击槽函数
 */
void MultiMotorControlPure::on_stopAllBtn_clicked()
{
    qDebug() << "全部停止按钮点击";
    executeAllMotorsCommand(0x00);  // CMD_STOP
}

/**
 * @brief 全部使能按钮点击槽函数
 */
void MultiMotorControlPure::on_enableAllBtn_clicked()
{
    qDebug() << "全部使能按钮点击";
    executeAllMotorsCommand(0x03);  // CMD_ENABLE
}

/**
 * @brief 全部失能按钮点击槽函数
 */
void MultiMotorControlPure::on_disableAllBtn_clicked()
{
    qDebug() << "全部失能按钮点击";
    executeAllMotorsCommand(0x04);  // CMD_DISABLE
}

/**
 * @brief 紧急停止按钮点击槽函数
 */
void MultiMotorControlPure::on_emergencyStopBtn_clicked()
{
    qDebug() << "紧急停止按钮点击";
    // 立即停止所有电机
    for (int i = 0; i < 6; i++) {
        m_motorRunning[i] = false;
        m_motorEnabled[i] = false;
    }
    executeAllMotorsCommand(0x00);  // CMD_STOP
    updateAllMotorsStatus();
}

/**
 * @brief 清除所有错误按钮点击槽函数
 */
void MultiMotorControlPure::on_clearAllErrorsBtn_clicked()
{
    qDebug() << "清除所有错误按钮点击";
    executeAllMotorsCommand(0x40);  // CMD_CLEAR_ERROR
}

/**
 * @brief 示波器按钮点击槽函数
 */
void MultiMotorControlPure::on_oscilloscopeBtn_clicked()
{
    qDebug() << "示波器按钮点击";
    emit openOscilloscope();
}

// ================================
// 电机1控制功能实现
// ================================

/**
 * @brief 电机1使能按钮点击槽函数实现
 * @param checked 按钮是否被选中
 */
void MultiMotorControlPure::on_motor1EnableBtn_clicked(bool checked)
{
    qDebug() << "电机1使能按钮点击，状态:" << (checked ? "使能" : "失能");

    m_motorEnabled[0] = checked;
    sendMotorCommand(1, checked ? 0x03 : 0x04);  // CMD_ENABLE : CMD_DISABLE
    updateMotorStatus(1);
}

/**
 * @brief 电机1启动按钮点击槽函数实现
 */
void MultiMotorControlPure::on_motor1StartBtn_clicked()
{
    qDebug() << "电机1启动按钮点击";

    // 检查是否已使能
    if (!m_motorEnabled[0]) {
        qDebug() << "电机1未使能，无法启动";
        return;
    }

    // 获取目标速度
    QSpinBox* targetSpeedSpin = findChild<QSpinBox*>("motor1TargetSpeedSpin");
    if (targetSpeedSpin) {
        m_motorTargetSpeed[0] = targetSpeedSpin->value();
    }

    // 更新运行状态
    setMotorRunning(1, true);
    sendMotorCommand(1, 0x01, m_motorTargetSpeed[0]);  // CMD_START
    updateMotorStatus(1);
}

/**
 * @brief 电机1停止按钮点击槽函数实现
 */
void MultiMotorControlPure::on_motor1StopBtn_clicked()
{
    qDebug() << "电机1停止按钮点击";

    // 更新运行状态
    setMotorRunning(1, false);
    sendMotorCommand(1, 0x00);  // CMD_STOP
    updateMotorStatus(1);
}

/**
 * @brief 电机1设置速度按钮点击槽函数实现
 */
void MultiMotorControlPure::on_motor1SetSpeedBtn_clicked()
{
    qDebug() << "电机1设置速度按钮点击";

    // 获取目标速度值
    QSpinBox* targetSpeedSpin = findChild<QSpinBox*>("motor1TargetSpeedSpin");
    if (targetSpeedSpin) {
        m_motorTargetSpeed[0] = targetSpeedSpin->value();
        qDebug() << "设置电机1目标速度为:" << m_motorTargetSpeed[0] << "RPM";

        // 如果电机正在运行，发送速度设置命令
        if (m_motorRunning[0]) {
            sendMotorCommand(1, 0x10, m_motorTargetSpeed[0]);  // CMD_SET_SPEED
        }
    }
}

// ================================
// 电机2-6控制功能实现
// ================================

/**
 * @brief 电机2使能按钮点击槽函数
 */
void MultiMotorControlPure::on_motor2EnableBtn_clicked(bool checked)
{
    qDebug() << "电机2使能按钮点击，状态:" << (checked ? "使能" : "失能");
    m_motorEnabled[1] = checked;
    sendMotorCommand(2, checked ? 0x03 : 0x04);
    updateMotorStatus(2);
}

void MultiMotorControlPure::on_motor2StartBtn_clicked()
{
    qDebug() << "电机2启动按钮点击";
    if (!m_motorEnabled[1]) {
        qDebug() << "电机2未使能，无法启动";
        return;
    }
    QSpinBox* targetSpeedSpin = findChild<QSpinBox*>("motor2TargetSpeedSpin");
    if (targetSpeedSpin) {
        m_motorTargetSpeed[1] = targetSpeedSpin->value();
    }
    setMotorRunning(2, true);
    sendMotorCommand(2, 0x01, m_motorTargetSpeed[1]);
    updateMotorStatus(2);
}

void MultiMotorControlPure::on_motor2StopBtn_clicked()
{
    qDebug() << "电机2停止按钮点击";
    setMotorRunning(2, false);
    sendMotorCommand(2, 0x00);
    updateMotorStatus(2);
}

void MultiMotorControlPure::on_motor2SetSpeedBtn_clicked()
{
    qDebug() << "电机2设置速度按钮点击";
    QSpinBox* targetSpeedSpin = findChild<QSpinBox*>("motor2TargetSpeedSpin");
    if (targetSpeedSpin) {
        m_motorTargetSpeed[1] = targetSpeedSpin->value();
        if (m_motorRunning[1]) {
            sendMotorCommand(2, 0x10, m_motorTargetSpeed[1]);
        }
    }
}

/**
 * @brief 电机3控制功能
 */
void MultiMotorControlPure::on_motor3EnableBtn_clicked(bool checked)
{
    qDebug() << "电机3使能按钮点击，状态:" << (checked ? "使能" : "失能");
    m_motorEnabled[2] = checked;
    sendMotorCommand(3, checked ? 0x03 : 0x04);
    updateMotorStatus(3);
}

void MultiMotorControlPure::on_motor3StartBtn_clicked()
{
    qDebug() << "电机3启动按钮点击";
    if (!m_motorEnabled[2]) return;
    QSpinBox* targetSpeedSpin = findChild<QSpinBox*>("motor3TargetSpeedSpin");
    if (targetSpeedSpin) m_motorTargetSpeed[2] = targetSpeedSpin->value();
    setMotorRunning(3, true);
    sendMotorCommand(3, 0x01, m_motorTargetSpeed[2]);
    updateMotorStatus(3);
}

void MultiMotorControlPure::on_motor3StopBtn_clicked()
{
    setMotorRunning(3, false);
    sendMotorCommand(3, 0x00);
    updateMotorStatus(3);
}

void MultiMotorControlPure::on_motor3SetSpeedBtn_clicked()
{
    QSpinBox* targetSpeedSpin = findChild<QSpinBox*>("motor3TargetSpeedSpin");
    if (targetSpeedSpin) {
        m_motorTargetSpeed[2] = targetSpeedSpin->value();
        if (m_motorRunning[2]) sendMotorCommand(3, 0x10, m_motorTargetSpeed[2]);
    }
}

/**
 * @brief 状态更新函数实现
 * @details 定期更新界面显示的状态信息，模拟真实的电机数据
 */
void MultiMotorControlPure::updateStatus()
{
    static int counter = 0;
    counter++;

    // 更新所有电机的状态显示
    for (int motorId = 1; motorId <= 6; motorId++) {
        if (m_motorRunning[motorId-1]) {
            // 模拟速度波动（±5%）
            int targetSpeed = m_motorTargetSpeed[motorId-1];
            int variation = (counter % 10 - 5) * targetSpeed / 100;
            int currentSpeed = targetSpeed + variation;
            m_motorSpeed[motorId-1] = currentSpeed;

            // 模拟位置变化
            m_motorPosition[motorId-1] += currentSpeed / 60;  // 每秒增加的位置

            // 更新界面显示
            updateMotorStatus(motorId);
        }
    }
}

// ================================
// 电机4-6控制功能实现
// ================================

/**
 * @brief 电机4控制功能
 */
void MultiMotorControlPure::on_motor4EnableBtn_clicked(bool checked)
{
    m_motorEnabled[3] = checked;
    sendMotorCommand(4, checked ? 0x03 : 0x04);
    updateMotorStatus(4);
}

void MultiMotorControlPure::on_motor4StartBtn_clicked()
{
    if (!m_motorEnabled[3]) return;
    QSpinBox* targetSpeedSpin = findChild<QSpinBox*>("motor4TargetSpeedSpin");
    if (targetSpeedSpin) m_motorTargetSpeed[3] = targetSpeedSpin->value();
    setMotorRunning(4, true);
    sendMotorCommand(4, 0x01, m_motorTargetSpeed[3]);
    updateMotorStatus(4);
}

void MultiMotorControlPure::on_motor4StopBtn_clicked()
{
    setMotorRunning(4, false);
    sendMotorCommand(4, 0x00);
    updateMotorStatus(4);
}

void MultiMotorControlPure::on_motor4SetSpeedBtn_clicked()
{
    QSpinBox* targetSpeedSpin = findChild<QSpinBox*>("motor4TargetSpeedSpin");
    if (targetSpeedSpin) {
        m_motorTargetSpeed[3] = targetSpeedSpin->value();
        if (m_motorRunning[3]) sendMotorCommand(4, 0x10, m_motorTargetSpeed[3]);
    }
}

/**
 * @brief 电机5控制功能
 */
void MultiMotorControlPure::on_motor5EnableBtn_clicked(bool checked)
{
    m_motorEnabled[4] = checked;
    sendMotorCommand(5, checked ? 0x03 : 0x04);
    updateMotorStatus(5);
}

void MultiMotorControlPure::on_motor5StartBtn_clicked()
{
    if (!m_motorEnabled[4]) return;
    QSpinBox* targetSpeedSpin = findChild<QSpinBox*>("motor5TargetSpeedSpin");
    if (targetSpeedSpin) m_motorTargetSpeed[4] = targetSpeedSpin->value();
    setMotorRunning(5, true);
    sendMotorCommand(5, 0x01, m_motorTargetSpeed[4]);
    updateMotorStatus(5);
}

void MultiMotorControlPure::on_motor5StopBtn_clicked()
{
    setMotorRunning(5, false);
    sendMotorCommand(5, 0x00);
    updateMotorStatus(5);
}

void MultiMotorControlPure::on_motor5SetSpeedBtn_clicked()
{
    QSpinBox* targetSpeedSpin = findChild<QSpinBox*>("motor5TargetSpeedSpin");
    if (targetSpeedSpin) {
        m_motorTargetSpeed[4] = targetSpeedSpin->value();
        if (m_motorRunning[4]) sendMotorCommand(5, 0x10, m_motorTargetSpeed[4]);
    }
}

/**
 * @brief 电机6控制功能
 */
void MultiMotorControlPure::on_motor6EnableBtn_clicked(bool checked)
{
    m_motorEnabled[5] = checked;
    sendMotorCommand(6, checked ? 0x03 : 0x04);
    updateMotorStatus(6);
}

void MultiMotorControlPure::on_motor6StartBtn_clicked()
{
    if (!m_motorEnabled[5]) return;
    QSpinBox* targetSpeedSpin = findChild<QSpinBox*>("motor6TargetSpeedSpin");
    if (targetSpeedSpin) m_motorTargetSpeed[5] = targetSpeedSpin->value();
    setMotorRunning(6, true);
    sendMotorCommand(6, 0x01, m_motorTargetSpeed[5]);
    updateMotorStatus(6);
}

void MultiMotorControlPure::on_motor6StopBtn_clicked()
{
    setMotorRunning(6, false);
    sendMotorCommand(6, 0x00);
    updateMotorStatus(6);
}

void MultiMotorControlPure::on_motor6SetSpeedBtn_clicked()
{
    QSpinBox* targetSpeedSpin = findChild<QSpinBox*>("motor6TargetSpeedSpin");
    if (targetSpeedSpin) {
        m_motorTargetSpeed[5] = targetSpeedSpin->value();
        if (m_motorRunning[5]) sendMotorCommand(6, 0x10, m_motorTargetSpeed[5]);
    }
}

// ================================
// 辅助函数实现
// ================================

/**
 * @brief 发送电机控制命令
 * @param motorId 电机ID (1-6)
 * @param command 控制命令
 * @param targetSpeed 目标速度 (可选)
 */
void MultiMotorControlPure::sendMotorCommand(int motorId, quint8 command, quint16 targetSpeed)
{
    qDebug() << QString("发送电机%1控制命令: 0x%2, 目标速度: %3 RPM")
                .arg(motorId)
                .arg(command, 2, 16, QChar('0'))
                .arg(targetSpeed);

    if (m_useRealData && m_deviceManager) {
        // 使用真实CANFD通信
        bool success = m_deviceManager->sendMotorCommand(motorId, command, targetSpeed);
        if (!success) {
            qDebug() << QString("电机%1命令发送失败，可能需要检查设备连接").arg(motorId);
        }
    } else {
        // 使用模拟数据，直接更新本地状态
        qDebug() << QString("模拟模式 - 电机%1命令已处理").arg(motorId);
    }
}

/**
 * @brief 更新指定电机的状态显示
 * @param motorId 电机ID (1-6)
 */
void MultiMotorControlPure::updateMotorStatus(int motorId)
{
    int index = motorId - 1;

    // 构造控件名称
    QString statusValueName = QString("motor%1StatusValue").arg(motorId);
    QString speedValueName = QString("motor%1SpeedValue").arg(motorId);
    QString positionValueName = QString("motor%1PositionValue").arg(motorId);

    // 更新状态显示
    QLabel* statusLabel = findChild<QLabel*>(statusValueName);
    if (statusLabel) {
        QString statusText;
        QString styleSheet;

        if (!m_motorEnabled[index]) {
            statusText = "未使能";
            styleSheet = "QLabel { color: #888888; font-weight: bold; }";
        } else if (m_motorRunning[index]) {
            statusText = "运行中";
            styleSheet = "QLabel { color: #0066CC; font-weight: bold; }";
        } else {
            statusText = "已停止";
            styleSheet = "QLabel { color: #FF6600; font-weight: bold; }";
        }

        statusLabel->setText(statusText);
        statusLabel->setStyleSheet(styleSheet);
    }

    // 更新速度显示
    QLabel* speedLabel = findChild<QLabel*>(speedValueName);
    if (speedLabel) {
        speedLabel->setText(QString("%1 RPM").arg(m_motorSpeed[index]));
    }

    // 更新位置显示
    QLabel* positionLabel = findChild<QLabel*>(positionValueName);
    if (positionLabel) {
        positionLabel->setText(QString::number(m_motorPosition[index]));
    }
}

/**
 * @brief 设置电机运行状态
 * @param motorId 电机ID (1-6)
 * @param running 运行状态
 */
void MultiMotorControlPure::setMotorRunning(int motorId, bool running)
{
    int index = motorId - 1;
    m_motorRunning[index] = running;

    if (!running) {
        m_motorSpeed[index] = 0;  // 停止时速度归零
    }
}

/**
 * @brief 检查电机是否已使能
 * @param motorId 电机ID (1-6)
 * @return 是否已使能
 */
bool MultiMotorControlPure::isMotorEnabled(int motorId)
{
    return m_motorEnabled[motorId - 1];
}

/**
 * @brief 执行所有电机的批量命令
 * @param command 控制命令
 */
void MultiMotorControlPure::executeAllMotorsCommand(quint8 command)
{
    qDebug() << QString("执行所有电机批量命令: 0x%1").arg(command, 2, 16, QChar('0'));

    for (int motorId = 1; motorId <= 6; motorId++) {
        int index = motorId - 1;

        switch (command) {
        case 0x00:  // CMD_STOP
            setMotorRunning(motorId, false);
            break;
        case 0x01:  // CMD_START
            if (m_motorEnabled[index]) {
                setMotorRunning(motorId, true);
            }
            break;
        case 0x03:  // CMD_ENABLE
            m_motorEnabled[index] = true;
            break;
        case 0x04:  // CMD_DISABLE
            m_motorEnabled[index] = false;
            setMotorRunning(motorId, false);
            break;
        }

        sendMotorCommand(motorId, command, m_motorTargetSpeed[index]);
        updateMotorStatus(motorId);
    }
}

/**
 * @brief 更新所有电机的状态显示
 */
void MultiMotorControlPure::updateAllMotorsStatus()
{
    for (int motorId = 1; motorId <= 6; motorId++) {
        updateMotorStatus(motorId);
    }
}

// ================================
// 设备连接检测和数据源管理
// ================================

/**
 * @brief 检测设备连接并显示选择对话框
 */
bool MultiMotorControlPure::checkDeviceConnectionAndPrompt()
{
    // 创建设备连接检测对话框
    DeviceConnectionDialog dialog("多电机控制系统", this);

    // 显示对话框
    int result = dialog.exec();

    if (result == QDialog::Accepted) {
        UserChoice choice = dialog.getUserChoice();
        DataSourceType dataSource = dialog.getDataSourceType();

        qDebug() << QString("用户选择: %1, 数据源: %2")
                    .arg(static_cast<int>(choice))
                    .arg(static_cast<int>(dataSource));

        // 根据用户选择切换数据源
        switchDataSource(dataSource);

        return true;
    } else {
        qDebug() << "用户取消了多电机控制系统";
        return false;
    }
}

/**
 * @brief 切换数据源
 */
void MultiMotorControlPure::switchDataSource(DataSourceType dataSource)
{
    m_currentDataSource = dataSource;
    m_useRealData = (dataSource == DataSourceType::REAL_DATA);

    qDebug() << QString("多电机控制系统切换到%1数据源")
                .arg(m_useRealData ? "真实" : "模拟");

    // 如果使用真实数据，启动设备监控
    if (m_useRealData) {
        m_deviceManager->startStatusMonitoring();
    } else {
        m_deviceManager->stopStatusMonitoring();
    }

    // 重置所有电机状态
    for (int i = 0; i < 6; i++) {
        m_motorRunning[i] = false;
        m_motorEnabled[i] = false;
        m_motorSpeed[i] = 0;
        m_motorPosition[i] = 0;
    }

    // 更新界面显示
    updateAllMotorsStatus();
}

/**
 * @brief 接收到电机状态数据槽函数
 */
void MultiMotorControlPure::onMotorStatusReceived(int motorId, const MotorStatusFrame& statusFrame)
{
    if (motorId < 1 || motorId > 6) {
        return;
    }

    int index = motorId - 1;

    // 更新电机状态数据
    m_motorSpeed[index] = statusFrame.current_speed;
    m_motorPosition[index] = statusFrame.current_position;
    m_motorEnabled[index] = (statusFrame.status_flags & 0x01) != 0;  // 使能状态
    m_motorRunning[index] = (statusFrame.status_flags & 0x02) != 0;  // 运行状态

    // 更新界面显示
    updateMotorStatus(motorId);

    qDebug() << QString("更新电机%1真实数据: 速度=%2, 位置=%3, 状态=0x%4")
                .arg(motorId)
                .arg(statusFrame.current_speed)
                .arg(statusFrame.current_position)
                .arg(statusFrame.status_flags, 2, 16, QChar('0'));
}

/**
 * @brief 设备连接状态改变槽函数
 */
void MultiMotorControlPure::onConnectionStatusChanged(CANFDConnectionStatus status)
{
    QString statusText;
    switch (status) {
    case CANFDConnectionStatus::DISCONNECTED:
        statusText = "设备已断开";
        break;
    case CANFDConnectionStatus::CONNECTING:
        statusText = "设备连接中";
        break;
    case CANFDConnectionStatus::CONNECTED:
        statusText = "设备已连接";
        break;
    case CANFDConnectionStatus::ACTIVE:
        statusText = "设备活跃";
        break;
    }

    qDebug() << QString("多电机控制系统 - CANFD设备状态: %1").arg(statusText);

    // 如果设备断开且当前使用真实数据，可以选择切换到模拟数据
    if (status == CANFDConnectionStatus::DISCONNECTED && m_useRealData) {
        // 这里可以添加自动切换到模拟数据的逻辑
        // 或者显示提示信息给用户
    }
}
