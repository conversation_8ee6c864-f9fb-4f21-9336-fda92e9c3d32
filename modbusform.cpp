/**
 * @file modbusform.cpp
 * @brief Modbus通信表单类实现文件
 * @details 实现了ModbusForm类的所有功能，包括Modbus RTU协议的串口通信、
 *          功能码处理、数据收发、CRC校验、日志记录等完整的Modbus通信功能。
 *          该类使用Qt Designer设计的UI界面。
 * <AUTHOR>
 * @date 2025-07-02
 * @version 1.0
 */

#include "modbusform.h"         // Modbus表单类声明
#include "ui_modbusform.h"      // Qt Designer生成的UI类
#include "messagebox_utils.h"   // 统一消息框工具类
#include <QFileDialog>          // Qt文件对话框类
#include <QDateTime>            // Qt日期时间类
#include <QRegularExpression>   // Qt正则表达式类
#include <QCloseEvent>          // Qt关闭事件类

/**
 * @brief Modbus功能码描述映射表
 * @details 静态常量映射表，用于将功能码数值转换为中文描述，
 *          便于用户理解和日志记录
 */
const QMap<uint8_t, QString> ModbusForm::functionCodeDescriptions = {
    {0x01, "读取线圈状态"},        // 读取线圈状态功能码
    {0x02, "读取输入状态"},        // 读取离散输入状态功能码
    {0x03, "读取保持寄存器"},      // 读取保持寄存器功能码
    {0x04, "读取输入寄存器"},      // 读取输入寄存器功能码
    {0x05, "写单个线圈"},          // 写单个线圈功能码
    {0x06, "写单个寄存器"},        // 写单个寄存器功能码
    {0x0F, "写多个线圈"},          // 写多个线圈功能码
    {0x10, "写多个寄存器"}         // 写多个寄存器功能码
};

/**
 * @brief ModbusForm构造函数实现
 * @param parent 父窗口指针
 * @details 初始化Modbus通信表单的所有组件和设置：
 *          1. 初始化成员变量和状态
 *          2. 创建串口和定时器对象
 *          3. 设置UI界面
 *          4. 建立信号槽连接
 */
ModbusForm::ModbusForm(QWidget *parent) :
    QWidget(parent),                                    // 调用基类构造函数
    ui(new Ui::ModbusForm),                            // 创建UI实例
    serialPort(new QSerialPort(this)),                 // 创建串口对象
    autoSendTimer(new QTimer(this)),                   // 创建自动发送定时器
    autoNewLine(true),                                 // 初始化自动换行标志
    isSerialOpened(false),                             // 初始化串口打开状态
    slaveAddress(1),                                   // 初始化从站地址为1
    functionCode(0x03)                                 // 初始化功能码为读取保持寄存器
{
    // 设置UI界面，加载由Qt Designer设计的界面布局
    ui->setupUi(this);

    // 设置窗口为全屏显示
    this->setWindowState(Qt::WindowMaximized);

    // 初始化UI控件和参数
    initUI();

    // 建立信号槽连接
    setupConnections();
}

/**
 * @brief ModbusForm析构函数实现
 * @details 清理资源和关闭连接：
 *          - 关闭串口连接
 *          - 释放UI资源
 */
ModbusForm::~ModbusForm()
{
    // 如果串口已打开，先关闭串口
    if (serialPort->isOpen()) {
        serialPort->close();
    }

    // 释放UI资源
    delete ui;
}

void ModbusForm::initUI()
{
    // 设置窗口标题
    setWindowTitle("Modbus通信设置");

    // 初始化串口设置
    foreach(const QSerialPortInfo &info, QSerialPortInfo::availablePorts()) {
        ui->portComboBox->addItem(info.portName());
    }

    // 波特率设置
    QList<qint32> baudRates = {1200, 2400, 4800, 9600, 19200, 38400, 57600, 115200};
    for(qint32 baudRate : baudRates) {
        ui->baudRateComboBox->addItem(QString::number(baudRate));
    }
    ui->baudRateComboBox->setCurrentText("9600");

    // 数据位设置
    ui->dataBitsComboBox->addItem("8");
    ui->dataBitsComboBox->addItem("7");                          // 7位数据位选项
    ui->dataBitsComboBox->addItem("6");                          // 6位数据位选项
    ui->dataBitsComboBox->addItem("5");                          // 5位数据位选项

    // === 第五步：停止位设置 ===
    ui->stopBitsComboBox->addItem("1");                          // 1位停止位选项
    ui->stopBitsComboBox->addItem("2");                          // 2位停止位选项

    // === 第六步：校验位设置 ===
    ui->parityComboBox->addItem("无");                           // 无校验选项
    ui->parityComboBox->addItem("奇校验");                       // 奇校验选项
    ui->parityComboBox->addItem("偶校验");                       // 偶校验选项

    // === 第七步：初始化功能码下拉框 ===
    setupFunctionCodes();                                        // 调用功能码设置函数

    // === 第八步：设置自动发送定时器默认间隔 ===
    ui->autoSendIntervalSpinBox->setValue(1000);                 // 设置默认1000ms间隔

    // === 第九步：初始化控件状态 ===
    enableControls(false);                                       // 初始状态下禁用大部分控件
}

/**
 * @brief 建立信号槽连接函数实现
 * @details 建立UI控件与槽函数之间的信号槽连接：
 *          1. 串口相关控件连接
 *          2. 数据发送接收控件连接
 *          3. 显示选项控件连接
 *          4. 自动发送功能连接
 *          5. CRC校验功能连接
 */
void ModbusForm::setupConnections()
{
    // === 第一步：串口相关信号连接 ===
    connect(ui->refreshPortButton, &QPushButton::clicked, this, &ModbusForm::onRefreshPortButtonClicked);     // 刷新端口按钮
    connect(ui->connectButton, &QPushButton::clicked, this, &ModbusForm::onConnectButtonClicked);             // 连接按钮
    connect(ui->disconnectButton, &QPushButton::clicked, this, &ModbusForm::onDisconnectButtonClicked);       // 断开连接按钮
    connect(serialPort, &QSerialPort::readyRead, this, &ModbusForm::onSerialDataReceived);                   // 串口数据接收信号
    connect(serialPort, &QSerialPort::errorOccurred, this, &ModbusForm::onSerialError);                      // 串口错误信号

    // === 第二步：数据发送接收相关信号连接 ===
    connect(ui->sendButton, &QPushButton::clicked, this, &ModbusForm::onSendButtonClicked);                  // 发送按钮
    connect(ui->clearReceiveButton, &QPushButton::clicked, this, &ModbusForm::onClearReceiveButtonClicked);  // 清空接收区按钮
    connect(ui->clearSendButton, &QPushButton::clicked, this, &ModbusForm::onClearSendButtonClicked);        // 清空发送区按钮
    connect(ui->saveLogButton, &QPushButton::clicked, this, &ModbusForm::onSaveLogButtonClicked);            // 保存日志按钮

    // === 第三步：显示选项相关信号连接 ===
    connect(ui->hexDisplayCheckBox, &QCheckBox::toggled, this, &ModbusForm::onHexDisplayToggled);            // 十六进制显示复选框
    connect(ui->autoNewLineCheckBox, &QCheckBox::toggled, this, &ModbusForm::onAutoNewLineToggled);          // 自动换行复选框
    connect(ui->hexSendCheckBox, &QCheckBox::toggled, this, &ModbusForm::onHexSendToggled);                  // 十六进制发送复选框

    // === 第四步：自动发送相关信号连接 ===
    connect(ui->autoSendCheckBox, &QCheckBox::toggled, this, &ModbusForm::onAutoSendToggled);                // 自动发送复选框
    connect(ui->autoSendIntervalSpinBox, QOverload<int>::of(&QSpinBox::valueChanged),
            this, &ModbusForm::onAutoSendIntervalChanged);                                                   // 自动发送间隔调整
    connect(autoSendTimer, &QTimer::timeout, this, &ModbusForm::onAutoSendTimer);                           // 自动发送定时器

    // === 第五步：CRC相关信号连接 ===
    connect(ui->autoCRCCheckBox, &QCheckBox::toggled, this, &ModbusForm::onAutoAppendCRCToggled);            // 自动添加CRC复选框

    // 返回按钮信号连接
    connect(ui->backButton, &QPushButton::clicked, this, &ModbusForm::onBackButtonClicked);

    // 功能码改变时的处理
    connect(ui->functionCodeComboBox, QOverload<int>::of(&QComboBox::currentIndexChanged),
            this, &ModbusForm::on_functionCodeComboBox_currentIndexChanged);
}

void ModbusForm::setupFunctionCodes()
{
    ui->functionCodeComboBox->clear();
    for (auto it = functionCodeDescriptions.begin(); it != functionCodeDescriptions.end(); ++it) {
        QString item = QString("0x%1 - %2").arg(it.key(), 2, 16, QChar('0')).arg(it.value());
        ui->functionCodeComboBox->addItem(item, it.key());
    }
}

void ModbusForm::enableControls(bool enabled)
{
    // 串口设置控件
    ui->portComboBox->setEnabled(!enabled);
    ui->baudRateComboBox->setEnabled(!enabled);
    ui->dataBitsComboBox->setEnabled(!enabled);
    ui->stopBitsComboBox->setEnabled(!enabled);
    ui->parityComboBox->setEnabled(!enabled);
    ui->connectButton->setEnabled(!enabled);
    ui->disconnectButton->setEnabled(enabled);
    ui->refreshPortButton->setEnabled(!enabled);

    // 数据发送接收控件
    ui->sendButton->setEnabled(enabled);
    ui->autoSendCheckBox->setEnabled(enabled);
    ui->autoSendIntervalSpinBox->setEnabled(enabled && ui->autoSendCheckBox->isChecked());
    ui->functionCodeComboBox->setEnabled(enabled);
    ui->slaveAddressSpinBox->setEnabled(enabled);
    ui->startAddressSpinBox->setEnabled(enabled);
    ui->quantitySpinBox->setEnabled(enabled);
    ui->sendTextEdit->setEnabled(enabled);
}

void ModbusForm::onRefreshPortButtonClicked()
{
    ui->portComboBox->clear();
    foreach(const QSerialPortInfo &info, QSerialPortInfo::availablePorts()) {
        ui->portComboBox->addItem(info.portName());
    }
}

void ModbusForm::onConnectButtonClicked()
{
    QString errorMsg;
    if (!checkSettings(errorMsg)) {
        MessageBoxUtils::warning(this, "错误", errorMsg);
        return;
    }

    serialPort->setPortName(ui->portComboBox->currentText());
    serialPort->setBaudRate(ui->baudRateComboBox->currentText().toInt());
    
    // 设置数据位
    switch (ui->dataBitsComboBox->currentText().toInt()) {
        case 5: serialPort->setDataBits(QSerialPort::Data5); break;
        case 6: serialPort->setDataBits(QSerialPort::Data6); break;
        case 7: serialPort->setDataBits(QSerialPort::Data7); break;
        case 8: serialPort->setDataBits(QSerialPort::Data8); break;
        default: serialPort->setDataBits(QSerialPort::Data8);
    }

    // 设置停止位
    serialPort->setStopBits(ui->stopBitsComboBox->currentText() == "1" ? 
                           QSerialPort::OneStop : QSerialPort::TwoStop);

    // 设置校验位
    QString parity = ui->parityComboBox->currentText();
    if (parity == "无") {
        serialPort->setParity(QSerialPort::NoParity);
    } else if (parity == "奇校验") {
        serialPort->setParity(QSerialPort::OddParity);
    } else if (parity == "偶校验") {
        serialPort->setParity(QSerialPort::EvenParity);
    }

    if (serialPort->open(QIODevice::ReadWrite)) {
        isSerialOpened = true;
        enableControls(true);
        appendLog("串口打开成功", false);
    } else {
        MessageBoxUtils::critical(this, "错误", "无法打开串口：" + serialPort->errorString());
    }
}

void ModbusForm::onDisconnectButtonClicked()
{
    if (serialPort->isOpen()) {
        serialPort->close();
    }
    isSerialOpened = false;
    enableControls(false);
    if (autoSendTimer->isActive()) {
        autoSendTimer->stop();
    }
    appendLog("串口已关闭", false);
}

void ModbusForm::onSerialDataReceived()
{
    QByteArray data = serialPort->readAll();
    if (data.isEmpty()) {
        return;
    }

    // 解析Modbus响应
    QString parsedData = parseModbusFrame(data, false);
    appendLog(parsedData, true);
}

void ModbusForm::onSerialError(QSerialPort::SerialPortError error)
{
    if (error == QSerialPort::NoError) {
        return;
    }

    QString errorString = "串口错误：";
    switch (error) {
        case QSerialPort::DeviceNotFoundError:
            errorString += "设备未找到";
            break;
        case QSerialPort::PermissionError:
            errorString += "访问权限被拒绝";
            break;
        case QSerialPort::OpenError:
            errorString += "设备已被占用";
            break;
        case QSerialPort::WriteError:
            errorString += "写入错误";
            break;
        case QSerialPort::ReadError:
            errorString += "读取错误";
            break;
        case QSerialPort::ResourceError:
            errorString += "设备被移除";
            break;
        default:
            errorString += "未知错误";
    }

    if (serialPort->isOpen()) {
        serialPort->close();
        isSerialOpened = false;
        enableControls(false);
        if (autoSendTimer->isActive()) {
            autoSendTimer->stop();
        }
    }

    appendLog(errorString, false);
    MessageBoxUtils::critical(this, "错误", errorString);
}

void ModbusForm::onSendButtonClicked()
{
    if (!serialPort->isOpen()) {
        MessageBoxUtils::warning(this, "警告", "请先打开串口");
        return;
    }

    QByteArray frame = createModbusFrame();
    if (frame.isEmpty()) {
        return;
    }

    if (serialPort->write(frame) != frame.size()) {
        MessageBoxUtils::warning(this, "错误", "发送数据失败");
        return;
    }

    QString parsedData = parseModbusFrame(frame, true);
    appendLog(parsedData, false);
}

void ModbusForm::onClearReceiveButtonClicked()
{
    ui->receiveTextEdit->clear();
}

void ModbusForm::onClearSendButtonClicked()
{
    ui->sendTextEdit->clear();
}

void ModbusForm::onSaveLogButtonClicked()
{
    QString fileName = QFileDialog::getSaveFileName(this,
        "保存日志", "", "文本文件 (*.txt);;所有文件 (*.*)");
    
    if (fileName.isEmpty()) {
        return;
    }

    saveLogToFile(fileName);
}

void ModbusForm::onHexDisplayToggled(bool checked)
{
    // 重新显示接收区的数据
    QString text = ui->receiveTextEdit->toPlainText();
    if (!text.isEmpty()) {
        ui->receiveTextEdit->clear();
        QStringList lines = text.split("\n");
        for (const QString &line : lines) {
            if (line.contains("接收:") || line.contains("发送:")) {
                appendLog(line, line.contains("接收:"));
            }
        }
    }
}

void ModbusForm::onAutoNewLineToggled(bool checked)
{
    autoNewLine = checked;
}

void ModbusForm::onAutoSendToggled(bool checked)
{
    ui->autoSendIntervalSpinBox->setEnabled(checked);
    if (checked) {
        autoSendTimer->start(ui->autoSendIntervalSpinBox->value());
    } else {
        autoSendTimer->stop();
    }
}

void ModbusForm::onAutoSendIntervalChanged(int value)
{
    if (autoSendTimer->isActive()) {
        autoSendTimer->setInterval(value);
    }
}

void ModbusForm::onAutoSendTimer()
{
    onSendButtonClicked();
}

void ModbusForm::onBackButtonClicked()
{
    // 如果串口已打开，先关闭
    if (serialPort->isOpen()) {
        serialPort->close();
        isSerialOpened = false;
    }

    // 停止自动发送
    if (autoSendTimer->isActive()) {
        autoSendTimer->stop();
    }

    // 发射返回信号，而不是直接关闭窗口
    emit backToProtocolSelect();
}

void ModbusForm::appendLog(const QString &text, bool isReceived)
{
    QString timestamp = QDateTime::currentDateTime().toString("HH:mm:ss.zzz");
    QString prefix = isReceived ? "接收: " : "发送: ";
    QString logText = QString("[%1] %2%3").arg(timestamp, prefix, text);
    
    if (autoNewLine) {
        logText += "\n";
    }
    
    ui->receiveTextEdit->moveCursor(QTextCursor::End);
    ui->receiveTextEdit->insertPlainText(logText);
    ui->receiveTextEdit->moveCursor(QTextCursor::End);
}

void ModbusForm::saveLogToFile(const QString &filename)
{
    QFile file(filename);
    if (!file.open(QIODevice::WriteOnly | QIODevice::Text)) {
        MessageBoxUtils::warning(this, "错误", "无法打开文件进行写入");
        return;
    }

    QTextStream out(&file);
    out << ui->receiveTextEdit->toPlainText();
    file.close();

    MessageBoxUtils::information(this, "成功", "日志保存成功");
}

QString ModbusForm::byteArrayToHexString(const QByteArray &data)
{
    QString result;
    for (int i = 0; i < data.size(); ++i) {
        result += QString("%1 ").arg(static_cast<quint8>(data.at(i)), 2, 16, QChar('0')).toUpper();
    }
    return result.trimmed();
}

QByteArray ModbusForm::hexStringToByteArray(const QString &hexString)
{
    QByteArray result;
    QString hex = hexString.simplified().replace(" ", "");
    
    for (int i = 0; i < hex.length(); i += 2) {
        QString byteString = hex.mid(i, 2);
        bool ok;
        char byte = static_cast<char>(byteString.toInt(&ok, 16));
        if (ok) {
            result.append(byte);
        }
    }
    return result;
}

bool ModbusForm::checkSettings(QString &errorMsg)
{
    if (ui->portComboBox->currentText().isEmpty()) {
        errorMsg = "请选择串口";
        return false;
    }

    if (ui->baudRateComboBox->currentText().isEmpty()) {
        errorMsg = "请选择波特率";
        return false;
    }

    return true;
}

QByteArray ModbusForm::createModbusFrame()
{
    QByteArray frame;
    
    // 获取从站地址
    slaveAddress = ui->slaveAddressSpinBox->value();
    frame.append(static_cast<char>(slaveAddress));

    // 获取功能码
    functionCode = ui->functionCodeComboBox->currentData().toUInt();
    frame.append(static_cast<char>(functionCode));

    // 获取起始地址
    quint16 startAddress = ui->startAddressSpinBox->value();
    frame.append(static_cast<char>((startAddress >> 8) & 0xFF));
    frame.append(static_cast<char>(startAddress & 0xFF));

    // 根据功能码处理数据
    switch (functionCode) {
        case 0x01:  // 读取线圈状态
        case 0x02:  // 读取输入状态
        case 0x03:  // 读取保持寄存器
        case 0x04:  // 读取输入寄存器
        {
            quint16 quantity = ui->quantitySpinBox->value();
            frame.append(static_cast<char>((quantity >> 8) & 0xFF));
            frame.append(static_cast<char>(quantity & 0xFF));
            break;
        }
        case 0x05:  // 写单个线圈
        {
            QString value = ui->sendTextEdit->toPlainText().trimmed();
            bool ok;
            quint16 coilValue = value.toUInt(&ok, 0);
            if (!ok) {
                MessageBoxUtils::warning(this, "错误", "无效的线圈值");
                return QByteArray();
            }
            frame.append(static_cast<char>(coilValue ? 0xFF : 0x00));
            frame.append(static_cast<char>(0x00));
            break;
        }
        case 0x06:  // 写单个寄存器
        {
            QString value = ui->sendTextEdit->toPlainText().trimmed();
            bool ok;
            quint16 registerValue = value.toUInt(&ok, 0);
            if (!ok) {
                MessageBoxUtils::warning(this, "错误", "无效的寄存器值");
                return QByteArray();
            }
            frame.append(static_cast<char>((registerValue >> 8) & 0xFF));
            frame.append(static_cast<char>(registerValue & 0xFF));
            break;
        }
        case 0x0F:  // 写多个线圈
        case 0x10:  // 写多个寄存器
        {
            QString data = ui->sendTextEdit->toPlainText().trimmed();
            QStringList values = data.split(QRegularExpression("\\s+"), Qt::SkipEmptyParts);
            
            if (values.isEmpty()) {
                MessageBoxUtils::warning(this, "错误", "请输入数据");
                return QByteArray();
            }

            quint16 quantity = values.size();
            frame.append(static_cast<char>((quantity >> 8) & 0xFF));
            frame.append(static_cast<char>(quantity & 0xFF));

            quint8 byteCount = (functionCode == 0x0F) ? (quantity + 7) / 8 : quantity * 2;
            frame.append(static_cast<char>(byteCount));

            if (functionCode == 0x0F) {
                // 写多个线圈
                quint8 currentByte = 0;
                quint8 bitCount = 0;
                
                for (const QString &value : values) {
                    bool ok;
                    bool coilValue = value.toUInt(&ok, 0);
                    if (!ok) {
                        MessageBoxUtils::warning(this, "错误", "无效的线圈值");
                        return QByteArray();
                    }
                    
                    if (coilValue) {
                        currentByte |= (1 << bitCount);
                    }
                    
                    bitCount++;
                    if (bitCount == 8 || &value == &values.last()) {
                        frame.append(static_cast<char>(currentByte));
                        currentByte = 0;
                        bitCount = 0;
                    }
                }
            } else {
                // 写多个寄存器
                for (const QString &value : values) {
                    bool ok;
                    quint16 registerValue = value.toUInt(&ok, 0);
                    if (!ok) {
                        MessageBoxUtils::warning(this, "错误", "无效的寄存器值");
                        return QByteArray();
                    }
                    frame.append(static_cast<char>((registerValue >> 8) & 0xFF));
                    frame.append(static_cast<char>(registerValue & 0xFF));
                }
            }
            break;
        }
        default:
            MessageBoxUtils::warning(this, "错误", "不支持的功能码");
            return QByteArray();
    }

    // 计算并添加CRC
    if (ui->autoCRCCheckBox->isChecked()) {
        QByteArray crc = calculateCRC(frame);
        frame.append(crc);
    }

    return frame;
}

QByteArray ModbusForm::calculateCRC(const QByteArray &data)
{
    quint16 crc = 0xFFFF;
    
    for (int i = 0; i < data.size(); ++i) {
        crc ^= static_cast<quint8>(data.at(i));
        for (int j = 0; j < 8; ++j) {
            if (crc & 0x0001) {
                crc = (crc >> 1) ^ 0xA001;
            } else {
                crc = crc >> 1;
            }
        }
    }
    
    QByteArray result;
    result.append(static_cast<char>(crc & 0xFF));         // 低字节在前
    result.append(static_cast<char>((crc >> 8) & 0xFF));  // 高字节在后
    return result;
}

QString ModbusForm::parseModbusFrame(const QByteArray &frame, bool isSend)
{
    if (frame.size() < MIN_FRAME_LENGTH) {
        return "帧长度错误";
    }

    QString result;
    if (ui->hexDisplayCheckBox->isChecked()) {
        result = byteArrayToHexString(frame);
    } else {
        // 解析从站地址
        quint8 address = static_cast<quint8>(frame.at(0));
        result = QString("从站地址: %1").arg(address);

        // 解析功能码
        quint8 fc = static_cast<quint8>(frame.at(1));
        result += QString(" | 功能码: 0x%1").arg(fc, 2, 16, QChar('0'));

        if (!isSend) {
            // 检查是否是异常响应
            if (fc & 0x80) {
                result = parseErrorResponse(frame);
            } else {
                // 根据功能码解析响应
                switch (fc) {
                    case 0x01:
                    case 0x02:
                    case 0x03:
                    case 0x04:
                        result = parseReadResponse(frame);
                        break;
                    case 0x05:
                    case 0x06:
                    case 0x0F:
                    case 0x10:
                        result = parseWriteResponse(frame);
                        break;
                    default:
                        result += " | 不支持的功能码";
                }
            }
        } else {
            // 发送帧的解析
            quint16 startAddr = (static_cast<quint8>(frame.at(2)) << 8) | 
                               static_cast<quint8>(frame.at(3));
            result += QString(" | 起始地址: %1").arg(startAddr);

            switch (fc) {
                case 0x01:
                case 0x02:
                case 0x03:
                case 0x04:
                {
                    quint16 quantity = (static_cast<quint8>(frame.at(4)) << 8) | 
                                     static_cast<quint8>(frame.at(5));
                    result += QString(" | 数量: %1").arg(quantity);
                    break;
                }
                case 0x05:
                {
                    quint16 value = (static_cast<quint8>(frame.at(4)) << 8) | 
                                   static_cast<quint8>(frame.at(5));
                    result += QString(" | 值: %1").arg(value ? "ON" : "OFF");
                    break;
                }
                case 0x06:
                {
                    quint16 value = (static_cast<quint8>(frame.at(4)) << 8) | 
                                   static_cast<quint8>(frame.at(5));
                    result += QString(" | 值: %1").arg(value);
                    break;
                }
                case 0x0F:
                case 0x10:
                {
                    quint16 quantity = (static_cast<quint8>(frame.at(4)) << 8) | 
                                     static_cast<quint8>(frame.at(5));
                    quint8 byteCount = static_cast<quint8>(frame.at(6));
                    result += QString(" | 数量: %1 | 字节数: %2").arg(quantity).arg(byteCount);
                    
                    result += " | 数据:";
                    for (int i = 7; i < frame.size() - 2; ++i) {
                        result += QString(" %1").arg(static_cast<quint8>(frame.at(i)), 2, 16, QChar('0'));
                    }
                    break;
                }
            }
        }

        // 解析CRC
        if (frame.size() >= 2) {
            quint16 crc = (static_cast<quint8>(frame.at(frame.size()-1)) << 8) | 
                          static_cast<quint8>(frame.at(frame.size()-2));
            result += QString(" | CRC: 0x%1").arg(crc, 4, 16, QChar('0'));
        }
    }

    return result;
}

QString ModbusForm::parseReadResponse(const QByteArray &frame)
{
    quint8 byteCount = static_cast<quint8>(frame.at(2));
    QString result = QString("字节数: %1 | 数据:").arg(byteCount);
    
    for (int i = 3; i < frame.size() - 2; ++i) {
        result += QString(" %1").arg(static_cast<quint8>(frame.at(i)), 2, 16, QChar('0'));
    }
    
    return result;
}

QString ModbusForm::parseWriteResponse(const QByteArray &frame)
{
    quint16 address = (static_cast<quint8>(frame.at(2)) << 8) | 
                      static_cast<quint8>(frame.at(3));
    quint16 value = (static_cast<quint8>(frame.at(4)) << 8) | 
                    static_cast<quint8>(frame.at(5));
    
    return QString("地址: %1 | 值: %2").arg(address).arg(value);
}

QString ModbusForm::parseErrorResponse(const QByteArray &frame)
{
    quint8 errorCode = static_cast<quint8>(frame.at(2));
    QString errorMsg;
    
    switch (errorCode) {
        case 0x01:
            errorMsg = "非法功能码";
            break;
        case 0x02:
            errorMsg = "非法数据地址";
            break;
        case 0x03:
            errorMsg = "非法数据值";
            break;
        case 0x04:
            errorMsg = "从站设备故障";
            break;
        case 0x05:
            errorMsg = "确认";
            break;
        case 0x06:
            errorMsg = "从站设备忙";
            break;
        case 0x08:
            errorMsg = "存储奇偶性差错";
            break;
        default:
            errorMsg = QString("未知错误(0x%1)").arg(errorCode, 2, 16, QChar('0'));
    }
    
    return QString("异常响应 | %1").arg(errorMsg);
}

bool ModbusForm::validateFrame(const QByteArray &frame, QString &errorMsg)
{
    if (frame.size() < MIN_FRAME_LENGTH) {
        errorMsg = "帧长度小于最小长度";
        return false;
    }
    
    if (frame.size() > MAX_FRAME_LENGTH) {
        errorMsg = "帧长度超过最大长度";
        return false;
    }
    
    if (!validateCRC(frame)) {
        errorMsg = "CRC校验错误";
        return false;
    }
    
    return true;
}

bool ModbusForm::validateCRC(const QByteArray &frame)
{
    if (frame.size() < 2) {
        return false;
    }
    
    QByteArray data = frame.left(frame.size() - 2);
    QByteArray receivedCRC = frame.right(2);
    QByteArray calculatedCRC = calculateCRC(data);
    
    return receivedCRC == calculatedCRC;
}

bool ModbusForm::checkRegisterRange(int address, int length, QString &errorMsg)
{
    if (address < 0 || address > 65535) {
        errorMsg = "地址超出范围(0-65535)";
        return false;
    }
    
    if (length < 1 || length > 125) {
        errorMsg = "长度超出范围(1-125)";
        return false;
    }
    
    if (address + length > 65536) {
        errorMsg = "地址范围超出最大值65535";
        return false;
    }
    
    return true;
}

void ModbusForm::onAutoAppendCRCToggled(bool checked)
{
    // 自动CRC校验切换时的处理
    Q_UNUSED(checked);
    // 不需要特殊处理，createModbusFrame函数会根据复选框状态自动处理CRC
}

void ModbusForm::onHexSendToggled(bool checked)
{
    // 十六进制发送切换时的处理
    Q_UNUSED(checked);
    // 不需要特殊处理，发送时会根据复选框状态自动处理数据格式
}

void ModbusForm::on_functionCodeComboBox_currentIndexChanged(int index)
{
    // 功能码改变时的处理
    if (index >= 0) {
        functionCode = ui->functionCodeComboBox->currentData().toUInt();
    }
}

void ModbusForm::onModbusDataReceived(const QByteArray &data)
{
    // Modbus数据接收处理
    if (data.isEmpty()) {
        return;
    }

    // 解析并显示数据
    QString parsedData = parseModbusFrame(data, false);
    appendLog(parsedData, true);
}

void ModbusForm::onSaveDataClicked()
{
    // 保存数据到文件
    QString filename = QFileDialog::getSaveFileName(this,
        "保存数据", "", "CSV文件 (*.csv);;所有文件 (*.*)");
    
    if (filename.isEmpty()) {
        return;
    }

    QFile file(filename);
    if (!file.open(QIODevice::WriteOnly | QIODevice::Text)) {
        MessageBoxUtils::warning(this, "错误", "无法打开文件进行写入");
        return;
    }

    QTextStream out(&file);
    
    // 写入表头
    if (tableWidget && tableWidget->columnCount() > 0) {
        QStringList headers;
        for (int i = 0; i < tableWidget->columnCount(); ++i) {
            headers << tableWidget->horizontalHeaderItem(i)->text();
        }
        out << headers.join(",") << "\n";

        // 写入数据
        for (int row = 0; row < tableWidget->rowCount(); ++row) {
            QStringList rowData;
            for (int col = 0; col < tableWidget->columnCount(); ++col) {
                QTableWidgetItem *item = tableWidget->item(row, col);
                rowData << (item ? item->text() : "");
            }
            out << rowData.join(",") << "\n";
        }
    }

    file.close();
    MessageBoxUtils::information(this, "成功", "数据已保存");
}

void ModbusForm::onClearDataClicked()
{
    // 清空数据表格
    if (tableWidget) {
        tableWidget->setRowCount(0);
        dataBuffer.clear();
    }
}

void ModbusForm::closeEvent(QCloseEvent *event)
{
    // 关闭窗口时的处理
    if (serialPort->isOpen()) {
        serialPort->close();
    }
    event->accept();
}
