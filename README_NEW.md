# 🚀 六电机控制系统上位机软件

[![Qt](https://img.shields.io/badge/Qt-6.6.3-green.svg)](https://www.qt.io/)
[![C++](https://img.shields.io/badge/C++-17-blue.svg)](https://isocpp.org/)
[![License](https://img.shields.io/badge/License-MIT-yellow.svg)](LICENSE)
[![Platform](https://img.shields.io/badge/Platform-Windows-lightgrey.svg)](https://www.microsoft.com/windows)

一个功能完整的工业级电机控制上位机软件，支持通过CANFD协议控制多达6个电机，具备实时监控、参数配置、故障诊断等功能。

## ✨ 主要特性

### 🎯 核心功能
- **多电机控制** - 同时控制6个电机的启动、停止、速度设置
- **CANFD通信** - 基于ZLGCANFD100U设备的高速通信
- **实时监控** - 电机状态、速度、位置的实时显示
- **虚拟示波器** - 电机数据的波形显示和分析
- **参数配置** - 波特率、采样点等通信参数设置

### 🛠️ 技术特点
- **现代化UI** - 基于Qt6的红白配色现代化界面
- **模块化设计** - 清晰的代码架构和组件分离
- **协议完整** - 完整的CANFD协议栈实现
- **设备集成** - 自动设备检测和连接管理
- **数据管理** - 智能的数据缓存和状态管理

## 🏗️ 项目结构

```
MotorUpperComputer/
├── 📁 docs/                    # 项目文档
│   ├── 六电机控制系统CANFD协议规范_V1.0.md
│   ├── 下位机CANFD协议开发文档.md
│   └── 项目开发规划与优化指南.md
├── 📁 icon/                    # 界面图标资源
├── 📁 include/                 # 头文件
├── 📁 lib/                     # 库文件
├── 📁 zlgcan_x64/             # ZLGCAN SDK
├── 🔧 主要源文件
│   ├── mainwindow.cpp/h        # 主窗口
│   ├── multimotorcontrol_pure.cpp/h  # 多电机控制
│   ├── canfddevicemanager.cpp/h      # CANFD设备管理
│   ├── motorprotocol.cpp/h           # 电机协议
│   ├── oscilloscopeinterface.cpp/h  # 示波器界面
│   └── baudratecalculator_new.cpp/h # 波特率计算器
└── 📄 untitled.pro            # Qt项目文件
```

## 🚀 快速开始

### 环境要求
- **操作系统**: Windows 10/11 (64位)
- **开发环境**: Qt 6.6.3 + MinGW 64位
- **硬件设备**: ZLGCANFD100U CAN/CANFD适配器
- **编译器**: GCC 11.2.0 (MinGW)

### 安装步骤

1. **克隆项目**
   ```bash
   git clone https://github.com/yourusername/MotorUpperComputer.git
   cd MotorUpperComputer
   ```

2. **安装Qt环境**
   - 下载并安装 [Qt 6.6.3](https://www.qt.io/download)
   - 确保包含MinGW 64位编译器

3. **配置ZLGCAN SDK**
   - 项目已包含必要的SDK文件
   - 确保zlgcan_x64目录下的dll文件可访问

4. **编译运行**
   ```bash
   # 使用Qt Creator打开项目
   qtcreator untitled.pro
   
   # 或使用命令行编译
   qmake
   make
   ```

## 📖 使用指南

### 基本操作流程

1. **设备连接**
   - 连接ZLGCANFD100U设备到电脑
   - 启动软件，点击"连接设置"配置设备

2. **协议配置**
   - 选择"CAN/CANFD协议"
   - 配置波特率和采样点参数
   - 保存配置并应用

3. **电机控制**
   - 进入"多电机控制"界面
   - 逐个使能需要控制的电机
   - 设置目标速度并启动电机

4. **数据监控**
   - 使用"虚拟示波器"查看实时数据
   - 监控电机运行状态和参数

### 界面说明

- **主界面**: 功能导航和状态显示
- **多电机控制**: 6个电机的独立控制面板
- **协议配置**: CAN/CANFD通信参数设置
- **示波器**: 实时数据波形显示
- **设备管理**: 硬件连接和状态监控

## 🔧 开发文档

详细的开发文档请参考：
- [CANFD协议规范](docs/六电机控制系统CANFD协议规范_V1.0.md)
- [下位机开发指南](docs/下位机CANFD协议开发文档.md)
- [项目优化建议](docs/项目开发规划与优化指南.md)

## 🤝 贡献指南

欢迎提交Issue和Pull Request来改进项目！

### 开发环境设置
1. Fork本项目
2. 创建功能分支: `git checkout -b feature/AmazingFeature`
3. 提交更改: `git commit -m 'Add some AmazingFeature'`
4. 推送分支: `git push origin feature/AmazingFeature`
5. 提交Pull Request

## 📄 许可证

本项目采用MIT许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 📞 联系方式

- 项目维护者: [您的姓名]
- 邮箱: [<EMAIL>]
- 项目链接: [https://github.com/yourusername/MotorUpperComputer](https://github.com/yourusername/MotorUpperComputer)

---

⭐ 如果这个项目对您有帮助，请给它一个星标！
