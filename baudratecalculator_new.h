#ifndef BAUDRATECALCULATOR_NEW_H
#define BAUDRATECALCULATOR_NEW_H

#include <QDialog>
#include <QComboBox>
#include <QSpinBox>
#include <QDoubleSpinBox>
#include <QTableWidget>
#include <QTableWidgetItem>
#include <QPushButton>
#include <QTextEdit>
#include <QHeaderView>
#include <QCheckBox>
#include <QLineEdit>
#include <QDoubleValidator>

QT_BEGIN_NAMESPACE
namespace Ui { class BaudrateCalculator; }
QT_END_NAMESPACE

/**
 * @brief CAN波特率计算结果结构
 * @details 存储波特率计算的详细参数
 */
struct BaudrateResult {
    int brp;            ///< 波特率预分频器
    int tseg1;          ///< 时间段1
    int tseg2;          ///< 时间段2
    int sjw;            ///< 同步跳转宽度
    double samplePoint; ///< 采样点百分比
    int actualBaud;     ///< 实际波特率
    double error;       ///< 误差百分比
};

/**
 * @brief CAN波特率计算器对话框 - 按照ZCANPro设计重新实现
 * @details 提供与ZCANPro官方软件相同的波特率和采样点配置界面
 */
class BaudrateCalculator : public QDialog
{
    Q_OBJECT

public:
    explicit BaudrateCalculator(QWidget *parent = nullptr);
    ~BaudrateCalculator();

    /**
     * @brief 获取仲裁段配置结果
     * @return 仲裁段波特率配置
     */
    BaudrateResult getArbitrationResult() const { return arbitrationResult; }

    /**
     * @brief 获取数据段配置结果
     * @return 数据段波特率配置
     */
    BaudrateResult getDataResult() const { return dataResult; }

private slots:
    /**
     * @brief 时钟频率改变槽函数
     */
    void onClockChanged();

    /**
     * @brief 仲裁段波特率改变槽函数
     */
    void onArbitrationBaudChanged();

    /**
     * @brief 仲裁段参数改变槽函数
     */
    void onArbitrationParameterChanged();

    /**
     * @brief 数据段波特率改变槽函数
     */
    void onDataBaudChanged();

    /**
     * @brief 数据段参数改变槽函数
     */
    void onDataParameterChanged();

    /**
     * @brief 仲裁段表格选择变化槽函数
     */
    void onArbitrationSelectionChanged();

    /**
     * @brief 数据段表格选择变化槽函数
     */
    void onDataSelectionChanged();

    /**
     * @brief 复制配置结果按钮槽函数
     * @details 复制结果输入框中的配置字符串到剪贴板
     */
    void onCopyResult();

    /**
     * @brief 返回按钮槽函数
     */
    void onReturnClicked();

    /**
     * @brief 仲裁段BRP输入框文本变化处理
     * @details 根据输入的BRP值过滤和高亮显示匹配的行
     */
    void onArbitrationBrpFilterChanged();

    /**
     * @brief 仲裁段TSEG1输入框文本变化处理
     * @details 根据输入的TSEG1值过滤和高亮显示匹配的行
     */
    void onArbitrationTseg1FilterChanged();

    /**
     * @brief 仲裁段TSEG2输入框文本变化处理
     * @details 根据输入的TSEG2值过滤和高亮显示匹配的行
     */
    void onArbitrationTseg2FilterChanged();

    /**
     * @brief 数据段BRP输入框文本变化处理
     * @details 根据输入的BRP值过滤和高亮显示匹配的行
     */
    void onDataBrpFilterChanged();

    /**
     * @brief 数据段TSEG1输入框文本变化处理
     * @details 根据输入的TSEG1值过滤和高亮显示匹配的行
     */
    void onDataTseg1FilterChanged();

    /**
     * @brief 数据段TSEG2输入框文本变化处理
     * @details 根据输入的TSEG2值过滤和高亮显示匹配的行
     */
    void onDataTseg2FilterChanged();

    /**
     * @brief 清除仲裁段过滤条件
     * @details 清空所有仲裁段输入框并恢复表格显示
     */
    void onArbitrationClearClicked();

    /**
     * @brief 清除数据段过滤条件
     * @details 清空所有数据段输入框并恢复表格显示
     */
    void onDataClearClicked();

private:
    /**
     * @brief 初始化UI界面
     * @details 设置表格、默认值等，复刻ZCANPro的界面布局
     */
    void initializeUI();

    /**
     * @brief 设置仲裁段表格
     * @details 配置仲裁段参数表格的列宽和样式
     */
    void setupArbitrationTable();

    /**
     * @brief 设置数据段表格
     * @details 配置数据段参数表格的列宽和样式
     */
    void setupDataTable();

    /**
     * @brief 连接信号槽
     * @details 连接UI控件的信号到相应的槽函数
     */
    void connectSignals();

    /**
     * @brief 填充仲裁段表格数据
     * @details 根据当前配置计算并显示仲裁段的各种可能配置
     */
    void fillArbitrationTableData();

    /**
     * @brief 填充数据段表格数据
     * @details 根据当前配置计算并显示数据段的各种可能配置
     */
    void fillDataTableData();

    /**
     * @brief 计算波特率配置
     * @details 根据当前设置计算仲裁段和数据段的波特率配置
     */
    void calculateBaudrates();

    /**
     * @brief 更新结果显示
     * @details 根据当前选择的配置更新结果显示区域
     */
    void updateResultDisplay();

    /**
     * @brief 过滤表格数据
     * @details 根据输入的BRP、TSEG1、TSEG2值过滤和高亮显示匹配的行
     * @param table 要过滤的表格
     * @param brpValue BRP过滤值（-1表示不过滤）
     * @param tseg1Value TSEG1过滤值（-1表示不过滤）
     * @param tseg2Value TSEG2过滤值（-1表示不过滤）
     */
    void filterTableData(QTableWidget* table, int brpValue, int tseg1Value, int tseg2Value);

    /**
     * @brief 计算所有可能的波特率配置
     * @param targetBaud 目标波特率 (bps)
     * @param maxDiff 最大允许误差 (%)
     * @param isDataSegment 是否为数据段 (影响参数范围)
     * @return 可能的配置列表，按误差排序
     */
    QVector<BaudrateResult> calculatePossibleConfigurations(int targetBaud, double maxDiff, bool isDataSegment);

    /**
     * @brief 计算Value十六进制值
     * @param brp 波特率预分频器
     * @param tseg1 时间段1
     * @param tseg2 时间段2
     * @param sjw 同步跳转宽度
     * @return 十六进制Value字符串
     */
    QString calculateValueHex(int brp, int tseg1, int tseg2, int sjw);

    // UI界面指针
    Ui::BaudrateCalculator *ui;

    // 配置参数
    int clockFrequency;  ///< 时钟频率 (Hz)
    BaudrateResult arbitrationResult;  ///< 仲裁段结果
    BaudrateResult dataResult;         ///< 数据段结果

    // 配置结果相关成员变量
    QString currentConfigString;       ///< 当前配置字符串

public:
    /**
     * @brief 获取当前配置字符串
     * @return 当前配置的字符串表示
     */
    QString getCurrentConfigString() const { return currentConfigString; }
};

#endif // BAUDRATECALCULATOR_NEW_H
