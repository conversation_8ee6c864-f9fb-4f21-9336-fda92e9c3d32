# 六电机控制系统 CANFD 协议开发文档 (下位机端)

## 📋 文档信息
- **版本**: V1.0
- **日期**: 2025-01-28
- **适用对象**: 下位机开发工程师
- **协议类型**: CANFD (CAN with Flexible Data-Rate)
- **控制对象**: 6个电机的独立控制

---

## 🎯 1. 系统架构概述

### 1.1 整体架构
```
上位机 ←→ CANFD总线 ←→ 下位机控制器 ←→ 6个电机驱动器
   ↓                      ↓              ↓
数据存储区              数据存储区        电机硬件
(实时监控)             (控制逻辑)        (执行机构)
```

### 1.2 下位机职责
- **接收处理**: 解析上位机发送的控制命令
- **执行控制**: 根据命令控制对应电机运行
- **状态反馈**: 实时上报电机运行状态
- **安全保护**: 实现各种保护和容错机制
- **数据管理**: 维护电机参数和运行数据

### 1.3 技术规格
- **总线类型**: CANFD (支持最大64字节数据帧)
- **波特率**: 仲裁段500Kbps，数据段2Mbps
- **电机数量**: 6个独立控制
- **实时性要求**: 控制周期≤20ms，状态反馈≤10ms
- **可靠性**: 支持错误检测、重传机制

---

## 🆔 2. CAN ID 分配表

### 2.1 ID分配原则
- **11位标准ID**: 使用标准CAN ID格式
- **优先级设计**: ID越小优先级越高
- **功能分组**: 按功能类型分配ID段

### 2.2 详细ID分配表

| 功能类别 | CAN ID | 方向 | 描述 | 优先级 |
|---------|--------|------|------|--------|
| **系统级命令** |
| 系统广播 | 0x000 | 上→下 | 系统广播命令(同步控制) | 最高 |
| 系统状态 | 0x080 | 下→上 | 系统整体状态反馈 | 高 |
| **电机控制命令** |
| 电机1控制 | 0x101 | 上→下 | 电机1控制命令 | 中 |
| 电机2控制 | 0x102 | 上→下 | 电机2控制命令 | 中 |
| 电机3控制 | 0x103 | 上→下 | 电机3控制命令 | 中 |
| 电机4控制 | 0x104 | 上→下 | 电机4控制命令 | 中 |
| 电机5控制 | 0x105 | 上→下 | 电机5控制命令 | 中 |
| 电机6控制 | 0x106 | 上→下 | 电机6控制命令 | 中 |
| **电机状态反馈** |
| 电机1状态 | 0x181 | 下→上 | 电机1状态反馈 | 中 |
| 电机2状态 | 0x182 | 下→上 | 电机2状态反馈 | 中 |
| 电机3状态 | 0x183 | 下→上 | 电机3状态反馈 | 中 |
| 电机4状态 | 0x184 | 下→上 | 电机4状态反馈 | 中 |
| 电机5状态 | 0x185 | 下→上 | 电机5状态反馈 | 中 |
| 电机6状态 | 0x186 | 下→上 | 电机6状态反馈 | 中 |
| **紧急和心跳** |
| 紧急停止 | 0x700 | 双向 | 紧急停止命令 | 最高 |
| 心跳包 | 0x701 | 下→上 | 系统心跳信号 | 低 |

### 2.3 ID使用规则
```c
// CAN ID定义宏
#define CAN_ID_SYSTEM_BROADCAST    0x000
#define CAN_ID_SYSTEM_STATUS       0x080
#define CAN_ID_MOTOR_CMD_BASE      0x100
#define CAN_ID_MOTOR_STATUS_BASE   0x180
#define CAN_ID_EMERGENCY_STOP      0x700
#define CAN_ID_HEARTBEAT           0x701

// 根据电机ID计算CAN ID
#define GET_MOTOR_CMD_ID(motor_id)    (CAN_ID_MOTOR_CMD_BASE + motor_id)
#define GET_MOTOR_STATUS_ID(motor_id) (CAN_ID_MOTOR_STATUS_BASE + motor_id)
```

---

## 📦 3. 数据帧格式定义

### 3.1 数据结构对齐
```c
#pragma pack(1)  // 强制1字节对齐，确保数据结构一致性
```

### 3.2 电机控制命令帧 (上位机→下位机)

#### 3.2.1 数据结构定义
```c
typedef struct {
    // === 帧头信息 (4字节) ===
    uint8_t  frame_type;      // 帧类型: 0x01=控制命令
    uint8_t  motor_id;        // 电机ID: 1-6
    uint8_t  sequence;        // 序列号(防重复，循环0-255)
    uint8_t  priority;        // 优先级: 0=最高, 255=最低
    
    // === 控制参数 (12字节) ===
    uint8_t  command;         // 控制命令(见CommandType枚举)
    uint8_t  control_mode;    // 控制模式(见ControlMode枚举)
    uint16_t target_speed;    // 目标速度 (RPM, 0-65535)
    uint32_t target_position; // 目标位置 (脉冲数)
    uint16_t acceleration;    // 加速度 (RPM/s)
    uint16_t deceleration;    // 减速度 (RPM/s)
    
    // === 校验和时间戳 (4字节) ===
    uint16_t checksum;        // 校验和(CRC16)
    uint16_t timestamp;       // 时间戳(毫秒低16位)
} MotorControlFrame;
// 总长度: 20字节
```

#### 3.2.2 控制命令枚举
```c
typedef enum {
    CMD_STOP           = 0x00,  // 停止电机
    CMD_START          = 0x01,  // 启动电机
    CMD_RESET          = 0x02,  // 复位电机控制器
    CMD_ENABLE         = 0x03,  // 使能电机
    CMD_DISABLE        = 0x04,  // 失能电机
    CMD_SET_SPEED      = 0x10,  // 设置目标速度
    CMD_SET_POSITION   = 0x11,  // 设置目标位置
    CMD_JOG_FORWARD    = 0x20,  // 正向点动
    CMD_JOG_REVERSE    = 0x21,  // 反向点动
    CMD_HOME           = 0x30,  // 回零操作
    CMD_CLEAR_ERROR    = 0x40   // 清除错误状态
} CommandType;
```

#### 3.2.3 控制模式枚举
```c
typedef enum {
    MODE_POSITION      = 0x01,  // 位置控制模式
    MODE_SPEED         = 0x02,  // 速度控制模式
    MODE_TORQUE        = 0x03,  // 转矩控制模式
    MODE_PROFILE_POS   = 0x04,  // 轮廓位置模式
    MODE_PROFILE_VEL   = 0x05,  // 轮廓速度模式
    MODE_HOMING        = 0x06   // 回零模式
} ControlMode;
```

### 3.3 电机状态反馈帧 (下位机→上位机)

#### 3.3.1 数据结构定义
```c
typedef struct {
    // === 帧头信息 (4字节) ===
    uint8_t  frame_type;      // 帧类型: 0x02=状态反馈
    uint8_t  motor_id;        // 电机ID: 1-6
    uint8_t  sequence;        // 序列号(与控制命令对应)
    uint8_t  status_flags;    // 状态标志位(见StatusFlags)
    
    // === 实时数据 (24字节) ===
    uint16_t current_speed;   // 当前速度 (RPM)
    uint32_t current_position; // 当前位置 (脉冲数)
    uint16_t motor_current;   // 电机电流 (mA)
    uint16_t motor_voltage;   // 电机电压 (mV)
    uint16_t temperature;     // 温度 (0.1°C)
    uint16_t torque;          // 转矩 (0.1Nm)
    uint32_t encoder_position; // 编码器位置 (脉冲)
    uint16_t following_error; // 跟随误差 (脉冲)
    uint16_t load_ratio;      // 负载率 (0.1%)
    
    // === 状态信息 (8字节) ===
    uint8_t  control_mode;    // 当前控制模式
    uint8_t  error_code;      // 错误代码
    uint16_t warning_code;    // 警告代码
    uint32_t runtime;         // 运行时间 (秒)
    
    // === 校验和时间戳 (4字节) ===
    uint16_t checksum;        // 校验和(CRC16)
    uint16_t timestamp;       // 时间戳(毫秒低16位)
} MotorStatusFrame;
// 总长度: 40字节
```

#### 3.3.2 状态标志位定义
```c
typedef enum {
    STATUS_ENABLED         = 0x01,  // 电机使能状态
    STATUS_RUNNING         = 0x02,  // 电机运行状态
    STATUS_ERROR           = 0x04,  // 错误状态
    STATUS_WARNING         = 0x08,  // 警告状态
    STATUS_HOMED           = 0x10,  // 已完成回零
    STATUS_MOVING          = 0x20,  // 正在运动
    STATUS_TARGET_REACHED  = 0x40,  // 已到达目标位置
    STATUS_EMERGENCY       = 0x80   // 紧急停止状态
} StatusFlags;
```

### 3.4 系统广播帧

#### 3.4.1 数据结构定义
```c
typedef struct {
    uint8_t  frame_type;      // 帧类型: 0x03=系统广播
    uint8_t  broadcast_cmd;   // 广播命令(见BroadcastCommand)
    uint8_t  motor_mask;      // 电机掩码 (bit0-5对应电机1-6)
    uint8_t  reserved;        // 保留字段
    uint32_t sync_timestamp;  // 同步时间戳
    uint32_t reserved2[2];    // 保留字段
} SystemBroadcastFrame;
// 总长度: 16字节
```

#### 3.4.2 广播命令枚举
```c
typedef enum {
    BROADCAST_STOP_ALL    = 0x00,  // 停止所有电机
    BROADCAST_START_ALL   = 0x01,  // 启动所有电机
    BROADCAST_EMERGENCY   = 0x02,  // 紧急停止所有电机
    BROADCAST_SYNC        = 0x03,  // 同步信号
    BROADCAST_RESET_ALL   = 0x04   // 复位所有电机
} BroadcastCommand;
```

---

## ⏰ 4. 通信时序规范

### 4.1 正常运行时序
```
时间轴:  0ms    10ms   20ms   30ms   40ms   50ms   60ms
上位机: CMD1 → CMD2 → CMD3 → CMD4 → CMD5 → CMD6 → CMD1...
下位机:   ↓      ↓      ↓      ↓      ↓      ↓      ↓
       STA1   STA2   STA3   STA4   STA5   STA6   STA1...

说明:
- CMD1-6: 电机1-6的控制命令
- STA1-6: 电机1-6的状态反馈
- 每个电机控制周期: 60ms (6个电机轮询)
- 单个电机控制间隔: 10ms
```

### 4.2 通信频率要求
- **控制命令接收**: 实时响应，处理延迟<1ms
- **状态反馈发送**: 100Hz (每10ms发送一个电机状态)
- **系统心跳**: 1Hz (每秒发送一次)
- **紧急停止**: 立即响应，延迟<0.5ms

### 4.3 超时处理
- **命令超时**: 连续100ms未收到控制命令，电机自动停止
- **通信超时**: 连续1秒未收到任何命令，进入安全模式
- **心跳超时**: 连续5秒未发送心跳，上位机判断下位机异常

---

## 💾 5. 下位机数据存储设计

### 5.1 电机控制器数据结构
```c
typedef struct {
    // === 通信相关 ===
    MotorControlFrame last_cmd;        // 最后接收的控制命令
    MotorStatusFrame  current_status;  // 当前状态数据
    uint32_t last_cmd_time;           // 最后命令接收时间
    uint8_t  cmd_sequence;            // 命令序列号
    
    // === 电机参数 ===
    struct {
        uint16_t max_speed;           // 最大速度 (RPM)
        uint16_t max_current;         // 最大电流 (mA)
        uint32_t encoder_resolution;  // 编码器分辨率
        uint16_t gear_ratio;          // 减速比
        int32_t  soft_limit_pos;      // 软件正限位
        int32_t  soft_limit_neg;      // 软件负限位
        uint16_t home_speed;          // 回零速度
        uint8_t  home_method;         // 回零方式
    } params;
    
    // === 运行状态 ===
    uint8_t  is_enabled;              // 使能状态
    uint8_t  current_mode;            // 当前控制模式
    uint32_t error_flags;             // 错误标志
    uint32_t warning_flags;           // 警告标志
    uint32_t total_runtime;           // 总运行时间
    
    // === 控制变量 ===
    int32_t  target_position;         // 目标位置
    int16_t  target_speed;            // 目标速度
    int16_t  current_speed;           // 当前速度
    int32_t  current_position;        // 当前位置
    
} MotorController;

// 全局电机控制器数组
MotorController g_motor_controllers[6];
```

### 5.2 系统全局数据
```c
typedef struct {
    // === 系统状态 ===
    uint8_t  system_state;            // 系统状态
    uint32_t system_runtime;          // 系统运行时间
    uint32_t last_heartbeat_time;     // 最后心跳时间
    
    // === 通信统计 ===
    uint32_t total_cmd_received;      // 接收命令总数
    uint32_t total_status_sent;       // 发送状态总数
    uint32_t comm_error_count;        // 通信错误计数
    
    // === 系统配置 ===
    uint8_t  motor_count;             // 电机数量
    uint32_t can_baudrate;            // CAN波特率
    uint8_t  debug_level;             // 调试级别
    
} SystemData;

SystemData g_system_data;
```

---

## 🔧 6. 核心功能实现

### 6.1 CAN消息接收处理
```c
/**
 * @brief CAN消息接收中断处理函数
 * @param can_msg: 接收到的CAN消息
 */
void CAN_RxCallback(CAN_Message* can_msg) {
    switch(can_msg->id) {
        case CAN_ID_SYSTEM_BROADCAST:
            handle_system_broadcast(can_msg);
            break;

        case CAN_ID_EMERGENCY_STOP:
            handle_emergency_stop();
            break;

        default:
            // 检查是否为电机控制命令
            if(can_msg->id >= 0x101 && can_msg->id <= 0x106) {
                uint8_t motor_id = can_msg->id - 0x100;
                handle_motor_command(motor_id, can_msg);
            }
            break;
    }
}

/**
 * @brief 处理电机控制命令
 * @param motor_id: 电机ID (1-6)
 * @param can_msg: CAN消息
 */
void handle_motor_command(uint8_t motor_id, CAN_Message* can_msg) {
    if(motor_id < 1 || motor_id > 6) return;

    MotorControlFrame* cmd = (MotorControlFrame*)can_msg->data;
    MotorController* motor = &g_motor_controllers[motor_id - 1];

    // 校验数据完整性
    if(!verify_checksum(cmd, sizeof(MotorControlFrame))) {
        motor->error_flags |= ERROR_CHECKSUM_FAIL;
        return;
    }

    // 检查序列号防重复
    if(cmd->sequence == motor->cmd_sequence) {
        return; // 重复命令，忽略
    }

    // 更新命令数据
    memcpy(&motor->last_cmd, cmd, sizeof(MotorControlFrame));
    motor->cmd_sequence = cmd->sequence;
    motor->last_cmd_time = get_system_time_ms();

    // 执行控制命令
    execute_motor_command(motor_id, cmd);
}

/**
 * @brief 执行电机控制命令
 * @param motor_id: 电机ID (1-6)
 * @param cmd: 控制命令
 */
void execute_motor_command(uint8_t motor_id, MotorControlFrame* cmd) {
    MotorController* motor = &g_motor_controllers[motor_id - 1];

    switch(cmd->command) {
        case CMD_STOP:
            motor_stop(motor_id);
            break;

        case CMD_START:
            motor_start(motor_id);
            break;

        case CMD_RESET:
            motor_reset(motor_id);
            break;

        case CMD_ENABLE:
            motor_enable(motor_id, true);
            break;

        case CMD_DISABLE:
            motor_enable(motor_id, false);
            break;

        case CMD_SET_SPEED:
            motor_set_speed(motor_id, cmd->target_speed);
            break;

        case CMD_SET_POSITION:
            motor_set_position(motor_id, cmd->target_position);
            break;

        case CMD_JOG_FORWARD:
            motor_jog(motor_id, JOG_FORWARD);
            break;

        case CMD_JOG_REVERSE:
            motor_jog(motor_id, JOG_REVERSE);
            break;

        case CMD_HOME:
            motor_home(motor_id);
            break;

        case CMD_CLEAR_ERROR:
            motor_clear_error(motor_id);
            break;

        default:
            motor->error_flags |= ERROR_INVALID_CMD;
            break;
    }
}
```

### 6.2 状态反馈发送

```c
/**
 * @brief 发送电机状态反馈
 * @param motor_id: 电机ID (1-6)
 */
void send_motor_status(uint8_t motor_id) {
    if(motor_id < 1 || motor_id > 6) return;

    MotorController* motor = &g_motor_controllers[motor_id - 1];
    CAN_Message can_msg;

    // 设置CAN消息头
    can_msg.id = GET_MOTOR_STATUS_ID(motor_id);
    can_msg.dlc = sizeof(MotorStatusFrame);
    can_msg.is_extended = 0;
    can_msg.is_canfd = 1;

    // 填充状态数据
    MotorStatusFrame* status = (MotorStatusFrame*)can_msg.data;
    memset(status, 0, sizeof(MotorStatusFrame));

    // 帧头信息
    status->frame_type = 0x02;
    status->motor_id = motor_id;
    status->sequence = motor->cmd_sequence;
    status->status_flags = get_motor_status_flags(motor_id);

    // 实时数据
    status->current_speed = motor->current_speed;
    status->current_position = motor->current_position;
    status->motor_current = get_motor_current(motor_id);
    status->motor_voltage = get_motor_voltage(motor_id);
    status->temperature = get_motor_temperature(motor_id);
    status->torque = get_motor_torque(motor_id);
    status->encoder_position = get_encoder_position(motor_id);
    status->following_error = calculate_following_error(motor_id);
    status->load_ratio = calculate_load_ratio(motor_id);

    // 状态信息
    status->control_mode = motor->current_mode;
    status->error_code = get_error_code(motor->error_flags);
    status->warning_code = get_warning_code(motor->warning_flags);
    status->runtime = motor->total_runtime;

    // 校验和时间戳
    status->timestamp = get_system_time_ms() & 0xFFFF;
    status->checksum = calculate_checksum(status, sizeof(MotorStatusFrame) - 2);

    // 发送CAN消息
    CAN_Transmit(&can_msg);
}

/**
 * @brief 获取电机状态标志
 * @param motor_id: 电机ID (1-6)
 * @return 状态标志位
 */
uint8_t get_motor_status_flags(uint8_t motor_id) {
    MotorController* motor = &g_motor_controllers[motor_id - 1];
    uint8_t flags = 0;

    if(motor->is_enabled) flags |= STATUS_ENABLED;
    if(is_motor_running(motor_id)) flags |= STATUS_RUNNING;
    if(motor->error_flags != 0) flags |= STATUS_ERROR;
    if(motor->warning_flags != 0) flags |= STATUS_WARNING;
    if(is_motor_homed(motor_id)) flags |= STATUS_HOMED;
    if(is_motor_moving(motor_id)) flags |= STATUS_MOVING;
    if(is_target_reached(motor_id)) flags |= STATUS_TARGET_REACHED;
    if(is_emergency_stop()) flags |= STATUS_EMERGENCY;

    return flags;
}
```

### 6.3 系统广播处理

```c
/**
 * @brief 处理系统广播命令
 * @param can_msg: CAN消息
 */
void handle_system_broadcast(CAN_Message* can_msg) {
    SystemBroadcastFrame* broadcast = (SystemBroadcastFrame*)can_msg->data;

    switch(broadcast->broadcast_cmd) {
        case BROADCAST_STOP_ALL:
            for(int i = 1; i <= 6; i++) {
                if(broadcast->motor_mask & (1 << (i-1))) {
                    motor_stop(i);
                }
            }
            break;

        case BROADCAST_START_ALL:
            for(int i = 1; i <= 6; i++) {
                if(broadcast->motor_mask & (1 << (i-1))) {
                    motor_start(i);
                }
            }
            break;

        case BROADCAST_EMERGENCY:
            emergency_stop_all_motors();
            break;

        case BROADCAST_SYNC:
            // 同步所有电机操作
            sync_all_motors(broadcast->sync_timestamp);
            break;

        case BROADCAST_RESET_ALL:
            for(int i = 1; i <= 6; i++) {
                if(broadcast->motor_mask & (1 << (i-1))) {
                    motor_reset(i);
                }
            }
            break;
    }
}

/**
 * @brief 紧急停止处理
 */
void handle_emergency_stop(void) {
    // 立即停止所有电机
    for(int i = 1; i <= 6; i++) {
        motor_emergency_stop(i);
        g_motor_controllers[i-1].error_flags |= ERROR_EMERGENCY_STOP;
    }

    // 设置系统紧急状态
    g_system_data.system_state = SYSTEM_STATE_EMERGENCY;

    // 发送紧急停止确认
    send_emergency_ack();
}
```

---

## 🛡️ 7. 错误处理和诊断

### 7.1 错误代码定义

```c
// 错误代码枚举
typedef enum {
    ERR_NO_ERROR          = 0x00,  // 无错误

    // 通信错误 (0x01-0x0F)
    ERR_COMM_TIMEOUT      = 0x01,  // 通信超时
    ERR_CHECKSUM_FAIL     = 0x02,  // 校验和错误
    ERR_INVALID_CMD       = 0x03,  // 无效命令
    ERR_INVALID_PARAM     = 0x04,  // 无效参数
    ERR_SEQUENCE_ERROR    = 0x05,  // 序列号错误

    // 电机硬件错误 (0x10-0x2F)
    ERR_MOTOR_FAULT       = 0x10,  // 电机故障
    ERR_OVERCURRENT       = 0x11,  // 过流保护
    ERR_OVERVOLTAGE       = 0x12,  // 过压保护
    ERR_UNDERVOLTAGE      = 0x13,  // 欠压保护
    ERR_OVERTEMP          = 0x14,  // 过温保护
    ERR_ENCODER_FAULT     = 0x15,  // 编码器故障
    ERR_HALL_FAULT        = 0x16,  // 霍尔传感器故障

    // 运动控制错误 (0x30-0x4F)
    ERR_POSITION_LIMIT    = 0x30,  // 位置限制
    ERR_SPEED_LIMIT       = 0x31,  // 速度限制
    ERR_FOLLOWING_ERROR   = 0x32,  // 跟随误差过大
    ERR_HOME_TIMEOUT      = 0x33,  // 回零超时
    ERR_NOT_HOMED         = 0x34,  // 未回零

    // 系统错误 (0x50-0x6F)
    ERR_SYSTEM_FAULT      = 0x50,  // 系统故障
    ERR_EMERGENCY_STOP    = 0x51,  // 紧急停止
    ERR_POWER_FAULT       = 0x52,  // 电源故障
    ERR_WATCHDOG_RESET    = 0x53   // 看门狗复位
} ErrorCode;

// 警告代码枚举
typedef enum {
    WARN_NO_WARNING       = 0x00,  // 无警告
    WARN_HIGH_TEMP        = 0x01,  // 温度偏高
    WARN_HIGH_CURRENT     = 0x02,  // 电流偏高
    WARN_LOW_VOLTAGE      = 0x03,  // 电压偏低
    WARN_COMM_DELAY       = 0x04,  // 通信延迟
    WARN_LOAD_HIGH        = 0x05   // 负载偏高
} WarningCode;
```

### 7.2 错误处理函数

```c
/**
 * @brief 设置电机错误
 * @param motor_id: 电机ID (1-6)
 * @param error_code: 错误代码
 */
void set_motor_error(uint8_t motor_id, ErrorCode error_code) {
    if(motor_id < 1 || motor_id > 6) return;

    MotorController* motor = &g_motor_controllers[motor_id - 1];
    motor->error_flags |= (1 << error_code);

    // 记录错误时间
    motor->last_error_time = get_system_time_ms();

    // 根据错误类型执行相应处理
    switch(error_code) {
        case ERR_OVERCURRENT:
        case ERR_OVERTEMP:
        case ERR_MOTOR_FAULT:
            // 严重错误，立即停止电机
            motor_emergency_stop(motor_id);
            break;

        case ERR_POSITION_LIMIT:
        case ERR_SPEED_LIMIT:
            // 限制错误，停止当前运动
            motor_stop(motor_id);
            break;

        default:
            // 其他错误，记录但不停止
            break;
    }

    // 发送错误报告
    send_error_report(motor_id, error_code);
}

/**
 * @brief 清除电机错误
 * @param motor_id: 电机ID (1-6)
 */
void clear_motor_error(uint8_t motor_id) {
    if(motor_id < 1 || motor_id > 6) return;

    MotorController* motor = &g_motor_controllers[motor_id - 1];
    motor->error_flags = 0;
    motor->warning_flags = 0;
}

/**
 * @brief 检查通信超时
 */
void check_communication_timeout(void) {
    uint32_t current_time = get_system_time_ms();

    for(int i = 0; i < 6; i++) {
        MotorController* motor = &g_motor_controllers[i];

        // 检查命令超时 (100ms)
        if(current_time - motor->last_cmd_time > 100) {
            if(motor->is_enabled && is_motor_running(i + 1)) {
                // 命令超时，停止电机
                motor_stop(i + 1);
                set_motor_error(i + 1, ERR_COMM_TIMEOUT);
            }
        }

        // 检查通信超时 (1000ms)
        if(current_time - motor->last_cmd_time > 1000) {
            // 进入安全模式
            motor_enable(i + 1, false);
            set_motor_error(i + 1, ERR_COMM_TIMEOUT);
        }
    }
}
```

---

## ⚙️ 8. 系统初始化和配置

### 8.1 系统初始化

```c
/**
 * @brief 系统初始化
 */
void system_init(void) {
    // 硬件初始化
    hardware_init();

    // CAN总线初始化
    can_init();

    // 电机控制器初始化
    motor_controllers_init();

    // 定时器初始化
    timer_init();

    // 中断初始化
    interrupt_init();

    // 系统数据初始化
    memset(&g_system_data, 0, sizeof(SystemData));
    g_system_data.motor_count = 6;
    g_system_data.can_baudrate = 500000;  // 500Kbps
    g_system_data.system_state = SYSTEM_STATE_INIT;

    // 启动系统任务
    start_system_tasks();

    g_system_data.system_state = SYSTEM_STATE_READY;
}

/**
 * @brief 电机控制器初始化
 */
void motor_controllers_init(void) {
    for(int i = 0; i < 6; i++) {
        MotorController* motor = &g_motor_controllers[i];

        // 清零所有数据
        memset(motor, 0, sizeof(MotorController));

        // 设置默认参数
        motor->params.max_speed = 3000;        // 3000 RPM
        motor->params.max_current = 5000;      // 5A
        motor->params.encoder_resolution = 2500; // 2500 PPR
        motor->params.gear_ratio = 1;          // 1:1
        motor->params.soft_limit_pos = 100000; // 正限位
        motor->params.soft_limit_neg = -100000; // 负限位
        motor->params.home_speed = 100;        // 回零速度
        motor->params.home_method = 1;         // 回零方式

        // 初始化状态
        motor->is_enabled = false;
        motor->current_mode = MODE_POSITION;
        motor->error_flags = 0;
        motor->warning_flags = 0;

        // 初始化硬件
        motor_hardware_init(i + 1);
    }
}

/**
 * @brief CAN总线初始化
 */
void can_init(void) {
    CAN_Config config;

    // 配置CAN参数
    config.baudrate_arbitration = 500000;  // 仲裁段500Kbps
    config.baudrate_data = 2000000;        // 数据段2Mbps
    config.sample_point = 75;              // 采样点75%
    config.sjw = 1;                        // 同步跳转宽度
    config.mode = CAN_MODE_NORMAL;         // 正常模式
    config.enable_canfd = true;            // 启用CANFD

    // 初始化CAN控制器
    CAN_Init(&config);

    // 配置接收过滤器
    setup_can_filters();

    // 启用CAN中断
    CAN_EnableInterrupt(CAN_IT_RX | CAN_IT_ERROR);
}

/**
 * @brief 配置CAN接收过滤器
 */
void setup_can_filters(void) {
    CAN_Filter filter;

    // 过滤器0: 系统广播 (0x000)
    filter.id = 0x000;
    filter.mask = 0x7FF;
    filter.mode = CAN_FILTER_EXACT;
    CAN_SetFilter(0, &filter);

    // 过滤器1: 电机控制命令 (0x101-0x106)
    filter.id = 0x100;
    filter.mask = 0x7F8;  // 匹配0x100-0x107
    filter.mode = CAN_FILTER_RANGE;
    CAN_SetFilter(1, &filter);

    // 过滤器2: 紧急停止 (0x700)
    filter.id = 0x700;
    filter.mask = 0x7FF;
    filter.mode = CAN_FILTER_EXACT;
    CAN_SetFilter(2, &filter);
}
```

### 8.2 系统任务调度

```c
/**
 * @brief 启动系统任务
 */
void start_system_tasks(void) {
    // 创建状态发送任务 (10ms周期)
    create_task(status_send_task, 10, TASK_PRIORITY_HIGH);

    // 创建心跳任务 (1000ms周期)
    create_task(heartbeat_task, 1000, TASK_PRIORITY_LOW);

    // 创建超时检查任务 (50ms周期)
    create_task(timeout_check_task, 50, TASK_PRIORITY_MEDIUM);

    // 创建电机控制任务 (1ms周期)
    create_task(motor_control_task, 1, TASK_PRIORITY_HIGHEST);
}

/**
 * @brief 状态发送任务
 */
void status_send_task(void) {
    static uint8_t motor_index = 0;

    // 轮询发送电机状态
    send_motor_status(motor_index + 1);

    motor_index = (motor_index + 1) % 6;
}

/**
 * @brief 心跳任务
 */
void heartbeat_task(void) {
    CAN_Message heartbeat_msg;

    heartbeat_msg.id = CAN_ID_HEARTBEAT;
    heartbeat_msg.dlc = 8;
    heartbeat_msg.is_extended = 0;
    heartbeat_msg.is_canfd = 0;

    // 填充心跳数据
    heartbeat_msg.data[0] = g_system_data.system_state;
    heartbeat_msg.data[1] = g_system_data.motor_count;
    *(uint32_t*)&heartbeat_msg.data[2] = g_system_data.system_runtime;
    *(uint16_t*)&heartbeat_msg.data[6] = g_system_data.comm_error_count;

    CAN_Transmit(&heartbeat_msg);

    g_system_data.last_heartbeat_time = get_system_time_ms();
}

/**
 * @brief 超时检查任务
 */
void timeout_check_task(void) {
    check_communication_timeout();
    check_motor_faults();
    update_system_statistics();
}
```

---

## 🔍 9. 调试和测试

### 9.1 调试接口

```c
// 调试级别定义
typedef enum {
    DEBUG_LEVEL_NONE    = 0,  // 无调试输出
    DEBUG_LEVEL_ERROR   = 1,  // 仅错误信息
    DEBUG_LEVEL_WARNING = 2,  // 错误和警告
    DEBUG_LEVEL_INFO    = 3,  // 一般信息
    DEBUG_LEVEL_DEBUG   = 4,  // 详细调试信息
    DEBUG_LEVEL_VERBOSE = 5   // 所有信息
} DebugLevel;

/**
 * @brief 调试输出宏
 */
#define DEBUG_PRINT(level, format, ...) \
    do { \
        if(g_system_data.debug_level >= level) { \
            printf("[%s] " format "\r\n", get_debug_level_string(level), ##__VA_ARGS__); \
        } \
    } while(0)

/**
 * @brief 打印电机状态
 * @param motor_id: 电机ID (1-6)
 */
void debug_print_motor_status(uint8_t motor_id) {
    if(motor_id < 1 || motor_id > 6) return;

    MotorController* motor = &g_motor_controllers[motor_id - 1];

    DEBUG_PRINT(DEBUG_LEVEL_INFO, "=== Motor %d Status ===", motor_id);
    DEBUG_PRINT(DEBUG_LEVEL_INFO, "Enabled: %s", motor->is_enabled ? "Yes" : "No");
    DEBUG_PRINT(DEBUG_LEVEL_INFO, "Mode: %d", motor->current_mode);
    DEBUG_PRINT(DEBUG_LEVEL_INFO, "Speed: %d RPM", motor->current_speed);
    DEBUG_PRINT(DEBUG_LEVEL_INFO, "Position: %ld", motor->current_position);
    DEBUG_PRINT(DEBUG_LEVEL_INFO, "Error Flags: 0x%08lX", motor->error_flags);
    DEBUG_PRINT(DEBUG_LEVEL_INFO, "Warning Flags: 0x%08lX", motor->warning_flags);
}

/**
 * @brief 打印CAN消息
 * @param msg: CAN消息
 * @param is_tx: 是否为发送消息
 */
void debug_print_can_message(CAN_Message* msg, bool is_tx) {
    if(g_system_data.debug_level < DEBUG_LEVEL_DEBUG) return;

    DEBUG_PRINT(DEBUG_LEVEL_DEBUG, "%s CAN ID:0x%03X DLC:%d Data:",
                is_tx ? "TX" : "RX", msg->id, msg->dlc);

    for(int i = 0; i < msg->dlc; i++) {
        printf("%02X ", msg->data[i]);
    }
    printf("\r\n");
}
```

### 9.2 测试用例

```c
/**
 * @brief 电机控制测试
 */
void test_motor_control(void) {
    DEBUG_PRINT(DEBUG_LEVEL_INFO, "Starting motor control test...");

    // 测试1: 使能电机
    for(int i = 1; i <= 6; i++) {
        motor_enable(i, true);
        delay_ms(100);

        if(!g_motor_controllers[i-1].is_enabled) {
            DEBUG_PRINT(DEBUG_LEVEL_ERROR, "Motor %d enable failed!", i);
        }
    }

    // 测试2: 速度控制
    for(int i = 1; i <= 6; i++) {
        motor_set_speed(i, 1000);  // 1000 RPM
        delay_ms(500);

        if(abs(g_motor_controllers[i-1].current_speed - 1000) > 50) {
            DEBUG_PRINT(DEBUG_LEVEL_WARNING, "Motor %d speed error!", i);
        }
    }

    // 测试3: 停止电机
    for(int i = 1; i <= 6; i++) {
        motor_stop(i);
        delay_ms(200);

        if(g_motor_controllers[i-1].current_speed != 0) {
            DEBUG_PRINT(DEBUG_LEVEL_WARNING, "Motor %d stop failed!", i);
        }
    }

    DEBUG_PRINT(DEBUG_LEVEL_INFO, "Motor control test completed.");
}

/**
 * @brief CAN通信测试
 */
void test_can_communication(void) {
    DEBUG_PRINT(DEBUG_LEVEL_INFO, "Starting CAN communication test...");

    // 发送测试心跳
    heartbeat_task();

    // 发送测试状态
    for(int i = 1; i <= 6; i++) {
        send_motor_status(i);
        delay_ms(10);
    }

    // 检查发送统计
    DEBUG_PRINT(DEBUG_LEVEL_INFO, "Total status sent: %ld",
                g_system_data.total_status_sent);

    DEBUG_PRINT(DEBUG_LEVEL_INFO, "CAN communication test completed.");
}
```

---

## 📚 10. 开发指南和注意事项

### 10.1 开发环境要求

- **编译器**: GCC 4.9+ 或 Keil MDK 5.0+
- **MCU**: 支持CANFD的32位微控制器 (如STM32H7系列)
- **CAN收发器**: 支持CANFD的收发器 (如TJA1043)
- **调试工具**: J-Link 或 ST-Link
- **CAN分析仪**: PCAN-View 或 ZLG CANTest

### 10.2 编程规范

```c
// 1. 命名规范
// 函数名: 小写+下划线
void motor_control_init(void);

// 变量名: 小写+下划线
uint32_t motor_speed;

// 宏定义: 大写+下划线
#define MAX_MOTOR_COUNT 6

// 结构体: 首字母大写+驼峰
typedef struct MotorController {
    // ...
} MotorController;

// 2. 注释规范
/**
 * @brief 函数简要描述
 * @param param1: 参数1描述
 * @param param2: 参数2描述
 * @return 返回值描述
 * @note 注意事项
 */

// 3. 错误处理
if(error_condition) {
    DEBUG_PRINT(DEBUG_LEVEL_ERROR, "Error description");
    return ERROR_CODE;
}
```

### 10.3 性能优化建议

1. **中断处理优化**
   - CAN接收中断处理时间 < 100μs
   - 复杂处理放到主循环中执行
   - 使用环形缓冲区缓存数据

2. **内存管理**
   - 避免动态内存分配
   - 使用静态数组和结构体
   - 注意内存对齐

3. **实时性保证**
   - 关键任务使用高优先级
   - 避免长时间关中断
   - 使用硬件定时器

### 10.4 安全注意事项

1. **电机安全**
   - 实现硬件急停功能
   - 软件限位保护
   - 过流过温保护

2. **通信安全**
   - 校验和检查
   - 超时保护
   - 重复命令过滤

3. **系统安全**
   - 看门狗保护
   - 电源监控
   - 故障自恢复

### 10.5 常见问题解决

**Q1: CAN消息发送失败**
```c
// 检查CAN总线状态
if(CAN_GetState() != CAN_STATE_READY) {
    CAN_Reset();
    can_init();
}
```

**Q2: 电机响应延迟**
```c
// 检查中断优先级设置
NVIC_SetPriority(CAN_RX_IRQn, 0);  // 最高优先级
```

**Q3: 数据校验失败**
```c
// 确保数据结构对齐
#pragma pack(1)
typedef struct {
    // 数据成员
} __attribute__((packed)) DataStruct;
```

---

## 📖 11. 附录

### 11.1 完整示例代码

参考代码文件:
- `motor_control.c` - 电机控制实现
- `can_protocol.c` - CAN协议处理
- `system_init.c` - 系统初始化
- `debug_utils.c` - 调试工具

### 11.2 测试用例

参考测试文件:
- `test_motor.c` - 电机功能测试
- `test_can.c` - CAN通信测试
- `test_system.c` - 系统集成测试

### 11.3 配置文件模板

```c
// config.h - 系统配置文件
#ifndef CONFIG_H
#define CONFIG_H

// 系统配置
#define MOTOR_COUNT           6
#define CAN_BAUDRATE_ARB      500000    // 仲裁段波特率
#define CAN_BAUDRATE_DATA     2000000   // 数据段波特率
#define SYSTEM_TICK_FREQ      1000      // 系统时钟频率(Hz)

// 电机参数
#define DEFAULT_MAX_SPEED     3000      // 默认最大速度(RPM)
#define DEFAULT_MAX_CURRENT   5000      // 默认最大电流(mA)
#define DEFAULT_ENCODER_PPR   2500      // 默认编码器分辨率

// 通信参数
#define CMD_TIMEOUT_MS        100       // 命令超时时间
#define COMM_TIMEOUT_MS       1000      // 通信超时时间
#define STATUS_SEND_FREQ      100       // 状态发送频率(Hz)

// 调试配置
#define DEBUG_LEVEL           DEBUG_LEVEL_INFO
#define ENABLE_CAN_DEBUG      1
#define ENABLE_MOTOR_DEBUG    1

#endif // CONFIG_H
```

---

## 📞 技术支持

如有技术问题，请联系:
- **邮箱**: <EMAIL>
- **电话**: 400-xxx-xxxx
- **技术文档**: http://docs.company.com

**文档版本**: V1.0
**最后更新**: 2025-01-28
```
