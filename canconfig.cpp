/**
 * @file canconfig.cpp
 * @brief CAN配置对话框类实现文件
 * @details 实现了CANConfig类的所有功能，包括UI界面创建、事件处理、
 *          配置数据管理等。提供完整的CAN/CANFD协议参数配置功能。
 * <AUTHOR>
 * @date 2025-07-02
 * @version 1.0
 */

#include "canconfig.h"      // CAN配置对话框类声明
#include <QApplication>     // Qt应用程序类

/**
 * @brief CANConfig构造函数实现
 * @param parent 父窗口指针
 * @details 初始化CAN配置对话框的所有组件和设置：
 *          1. 设置窗口属性（标题、大小、模态）
 *          2. 创建UI界面布局和控件
 *          3. 设置信号槽连接
 *          4. 初始化默认配置值
 */
CANConfig::CANConfig(QWidget *parent)
    : QDialog(parent)                           // 调用基类构造函数
{
    // 设置窗口标题
    setWindowTitle("波特率计算器");
    // 设置固定窗口大小，防止用户调整
    setFixedSize(500, 600);
    // 设置为模态对话框，阻塞父窗口交互
    setModal(true);

    // 设置现代化样式
    setStyleSheet(
        "QDialog {"
        "    background: qlineargradient(x1:0, y1:0, x2:0, y2:1, "
        "                               stop:0 #2c3e50, stop:1 #34495e);"
        "    color: #ecf0f1;"
        "    font-family: 'Microsoft YaHei UI';"
        "}"
        "QGroupBox {"
        "    background: rgba(255, 255, 255, 0.1);"
        "    border: 2px solid rgba(255, 255, 255, 0.2);"
        "    border-radius: 15px;"
        "    font-size: 14px;"
        "    font-weight: bold;"
        "    color: #ecf0f1;"
        "    padding-top: 15px;"
        "    margin-top: 10px;"
        "}"
        "QGroupBox::title {"
        "    subcontrol-origin: margin;"
        "    left: 20px;"
        "    padding: 5px 15px;"
        "    background: rgba(52, 152, 219, 0.8);"
        "    border-radius: 8px;"
        "    color: white;"
        "}"
        "QLabel {"
        "    color: #ecf0f1;"
        "    font-size: 12px;"
        "    font-weight: bold;"
        "    padding: 5px;"
        "}"
        "QComboBox {"
        "    background: rgba(255, 255, 255, 0.15);"
        "    border: 2px solid rgba(255, 255, 255, 0.3);"
        "    border-radius: 8px;"
        "    padding: 8px 12px;"
        "    color: #ecf0f1;"
        "    font-size: 11px;"
        "    min-height: 20px;"
        "}"
        "QComboBox:hover {"
        "    background: rgba(255, 255, 255, 0.25);"
        "    border: 2px solid rgba(52, 152, 219, 0.6);"
        "}"
        "QComboBox:focus {"
        "    border: 2px solid #3498db;"
        "    background: rgba(255, 255, 255, 0.2);"
        "}"
        "QComboBox::drop-down {"
        "    border: none;"
        "    width: 30px;"
        "}"
        "QComboBox::down-arrow {"
        "    image: none;"
        "    border-left: 5px solid transparent;"
        "    border-right: 5px solid transparent;"
        "    border-top: 8px solid #ecf0f1;"
        "    margin-right: 8px;"
        "}"
        "QComboBox QAbstractItemView {"
        "    background: #34495e;"
        "    border: 2px solid #3498db;"
        "    border-radius: 8px;"
        "    color: #ecf0f1;"
        "    selection-background-color: #3498db;"
        "    outline: none;"
        "}"
        "QCheckBox {"
        "    color: #ecf0f1;"
        "    font-size: 12px;"
        "    font-weight: bold;"
        "    spacing: 8px;"
        "}"
        "QCheckBox::indicator {"
        "    width: 18px;"
        "    height: 18px;"
        "    border: 2px solid rgba(255, 255, 255, 0.3);"
        "    border-radius: 4px;"
        "    background: rgba(255, 255, 255, 0.1);"
        "}"
        "QCheckBox::indicator:hover {"
        "    border: 2px solid #3498db;"
        "    background: rgba(52, 152, 219, 0.2);"
        "}"
        "QCheckBox::indicator:checked {"
        "    background: #3498db;"
        "    border: 2px solid #2980b9;"
        "}"
        "QCheckBox::indicator:checked:hover {"
        "    background: #5dade2;"
        "}"
    );

    // 创建并设置用户界面
    setupUI();
    // 设置信号槽连接
    setupConnections();

    // 设置默认配置值
    protocolComboBox->setCurrentText("CAN");   // 默认选择CAN协议
    onProtocolChanged();                       // 触发协议变化处理，更新界面
}

/**
 * @brief CANConfig析构函数实现
 * @details 清理资源，Qt的父子关系会自动清理UI控件，无需手动删除
 */
CANConfig::~CANConfig()
{
    // Qt的父子关系会自动清理UI控件，无需手动删除
}

/**
 * @brief 设置用户界面
 * @details 创建并布局所有UI控件，包括配置参数区域和按钮区域：
 *          1. 创建主布局和分组框
 *          2. 创建各种配置选择控件
 *          3. 创建按钮区域
 *          4. 设置布局层次结构
 */
void CANConfig::setupUI()
{
    // 创建主垂直布局管理器
    mainLayout = new QVBoxLayout(this);

    // 创建配置参数分组框
    QGroupBox *configGroup = new QGroupBox("配置参数");
    configLayout = new QGridLayout(configGroup);

    int row = 0;  // 网格布局行计数器

    // === 协议类型选择 ===
    configLayout->addWidget(new QLabel("协议:"), row, 0);
    protocolComboBox = new QComboBox();
    protocolComboBox->addItems({"CAN", "CANFD"});  // 添加协议选项
    configLayout->addWidget(protocolComboBox, row++, 1);

    // === CANFD标准选择 ===
    configLayout->addWidget(new QLabel("CANFD标准:"), row, 0);
    canfdStandardComboBox = new QComboBox();
    canfdStandardComboBox->addItems({"CAN FD", "CAN FD ISO"});  // 添加CANFD标准选项
    configLayout->addWidget(canfdStandardComboBox, row++, 1);

    // === 标准/加速类型选择 ===
    configLayout->addWidget(new QLabel("标准/加速:"), row, 0);
    standardTypeComboBox = new QComboBox();
    standardTypeComboBox->addItems({"标准", "加速"});  // 添加标准类型选项
    configLayout->addWidget(standardTypeComboBox, row++, 1);

    // === 仲裁域波特率选择 ===
    configLayout->addWidget(new QLabel("仲裁域波特率:"), row, 0);
    arbitrationBaudComboBox = new QComboBox();
    // 添加常用的仲裁域波特率选项，包含采样点信息
    arbitrationBaudComboBox->addItems({"500kbps 80%", "1Mbps 80%", "250kbps 80%"});
    configLayout->addWidget(arbitrationBaudComboBox, row++, 1);

    // === 数据域波特率选择 ===
    configLayout->addWidget(new QLabel("数据域波特率:"), row, 0);
    dataBaudComboBox = new QComboBox();
    // 添加CANFD数据域波特率选项，通常比仲裁域更高
    dataBaudComboBox->addItems({"1Mbps 80%", "2Mbps 80%", "4Mbps 80%", "5Mbps 80%"});
    configLayout->addWidget(dataBaudComboBox, row++, 1);

    // === 波特率计算器按钮 ===
    configLayout->addWidget(new QLabel("采样点配置:"), row, 0);
    QPushButton *baudrateCalcButton = new QPushButton("波特率计算器");
    baudrateCalcButton->setToolTip("打开波特率计算器，精确配置采样点参数");
    baudrateCalcButton->setStyleSheet(
        "QPushButton {"
        "    background-color: #4CAF50;"
        "    color: white;"
        "    border: none;"
        "    padding: 8px 16px;"
        "    border-radius: 4px;"
        "    font-weight: bold;"
        "}"
        "QPushButton:hover {"
        "    background-color: #45a049;"
        "}"
        "QPushButton:pressed {"
        "    background-color: #3d8b40;"
        "}"
    );
    configLayout->addWidget(baudrateCalcButton, row++, 1);

    // 连接波特率计算器按钮信号
    connect(baudrateCalcButton, &QPushButton::clicked, this, &CANConfig::onBaudrateCalculatorClicked);

    // === 自定义波特率选择 ===
    configLayout->addWidget(new QLabel("自定义波特率:"), row, 0);
    customBaudComboBox = new QComboBox();
    // 提供自定义波特率选项，用于特殊需求
    customBaudComboBox->addItems({"无", "自定义1", "自定义2"});
    configLayout->addWidget(customBaudComboBox, row++, 1);

    // === 工作模式选择 ===
    configLayout->addWidget(new QLabel("工作模式:"), row, 0);
    workModeComboBox = new QComboBox();
    // 添加CAN控制器支持的各种工作模式
    workModeComboBox->addItems({"正常模式", "只听模式", "自测模式", "单次模式"});
    configLayout->addWidget(workModeComboBox, row++, 1);

    // === 终端电阻设置 ===
    configLayout->addWidget(new QLabel("终端电阻:"), row, 0);
    terminalResistanceComboBox = new QComboBox();
    // 120欧姆终端电阻开关，用于总线阻抗匹配
    terminalResistanceComboBox->addItems({"使能", "禁用"});
    configLayout->addWidget(terminalResistanceComboBox, row++, 1);

    // === 总线利用率上报设置 ===
    configLayout->addWidget(new QLabel("上报总线利用率:"), row, 0);
    busUtilizationComboBox = new QComboBox();
    // 是否启用总线利用率统计和上报功能
    busUtilizationComboBox->addItems({"使能", "禁用"});
    configLayout->addWidget(busUtilizationComboBox, row++, 1);

    // === 清空缓冲区复选框 ===
    clearBufferCheckBox = new QCheckBox("清空");
    // 跨两列显示复选框，用于选择是否清空接收缓冲区
    configLayout->addWidget(clearBufferCheckBox, row++, 0, 1, 2);

    // 将配置分组框添加到主布局
    mainLayout->addWidget(configGroup);

    // === 按钮区域创建 ===
    buttonLayout = new QHBoxLayout();
    confirmButton = new QPushButton("确认");
    cancelButton = new QPushButton("取消");

    // 设置按钮样式
    QString buttonStyle =
        "QPushButton {"
        "    background: qlineargradient(x1:0, y1:0, x2:0, y2:1, "
        "                               stop:0 #27ae60, stop:1 #229954);"
        "    color: white;"
        "    border: none;"
        "    border-radius: 10px;"
        "    padding: 12px 25px;"
        "    font-size: 13px;"
        "    font-weight: bold;"
        "    font-family: 'Microsoft YaHei UI';"
        "    min-width: 80px;"
        "}"
        "QPushButton:hover {"
        "    background: qlineargradient(x1:0, y1:0, x2:0, y2:1, "
        "                               stop:0 #2ecc71, stop:1 #27ae60);"
        "    transform: translateY(-2px);"
        "}"
        "QPushButton:pressed {"
        "    background: qlineargradient(x1:0, y1:0, x2:0, y2:1, "
        "                               stop:0 #229954, stop:1 #1e8449);"
        "}";

    QString cancelButtonStyle =
        "QPushButton {"
        "    background: qlineargradient(x1:0, y1:0, x2:0, y2:1, "
        "                               stop:0 #95a5a6, stop:1 #7f8c8d);"
        "    color: white;"
        "    border: none;"
        "    border-radius: 10px;"
        "    padding: 12px 25px;"
        "    font-size: 13px;"
        "    font-weight: bold;"
        "    font-family: 'Microsoft YaHei UI';"
        "    min-width: 80px;"
        "}"
        "QPushButton:hover {"
        "    background: qlineargradient(x1:0, y1:0, x2:0, y2:1, "
        "                               stop:0 #bdc3c7, stop:1 #95a5a6);"
        "    transform: translateY(-2px);"
        "}"
        "QPushButton:pressed {"
        "    background: qlineargradient(x1:0, y1:0, x2:0, y2:1, "
        "                               stop:0 #7f8c8d, stop:1 #566573);"
        "}";

    confirmButton->setStyleSheet(buttonStyle);
    cancelButton->setStyleSheet(cancelButtonStyle);

    // 添加弹性空间，使按钮居中对齐
    buttonLayout->addStretch();
    buttonLayout->addWidget(confirmButton);
    buttonLayout->addSpacing(20);  // 按钮间距
    buttonLayout->addWidget(cancelButton);
    buttonLayout->addStretch();

    // 将按钮布局添加到主布局
    mainLayout->addLayout(buttonLayout);
}

/**
 * @brief 设置信号槽连接
 * @details 连接UI控件的信号到相应的槽函数：
 *          1. 协议选择变化信号
 *          2. 确认按钮点击信号
 *          3. 取消按钮点击信号
 */
void CANConfig::setupConnections()
{
    // 连接协议选择下拉框的索引变化信号到协议变化处理槽函数
    connect(protocolComboBox, QOverload<int>::of(&QComboBox::currentIndexChanged),
            this, &CANConfig::onProtocolChanged);
    // 连接确认按钮点击信号到确认处理槽函数
    connect(confirmButton, &QPushButton::clicked, this, &CANConfig::onConfirmClicked);
    // 连接取消按钮点击信号到取消处理槽函数
    connect(cancelButton, &QPushButton::clicked, this, &CANConfig::onCancelClicked);
}

/**
 * @brief 协议类型改变事件处理函数
 * @details 响应用户更改协议类型选择，动态调整界面显示：
 *          1. 获取当前选择的协议类型
 *          2. 根据协议类型启用或禁用相关控件
 *          3. 为CAN模式设置合适的默认值
 */
void CANConfig::onProtocolChanged()
{
    // 获取当前选择的协议类型
    QString protocol = protocolComboBox->currentText();

    // 判断是否为CANFD协议
    bool isCANFD = (protocol == "CANFD");

    // 根据协议类型设置CANFD相关控件的启用状态
    canfdStandardComboBox->setEnabled(isCANFD);  // CANFD标准选择
    dataBaudComboBox->setEnabled(isCANFD);       // 数据域波特率选择

    if (!isCANFD) {
        // CAN模式下为数据域波特率设置默认值（虽然禁用但保持一致性）
        dataBaudComboBox->setCurrentText("1Mbps 80%");
    }
}

/**
 * @brief 确认按钮点击事件处理函数
 * @details 响应用户点击确认按钮的操作：
 *          1. 从UI控件收集所有配置参数
 *          2. 将参数保存到配置数据结构
 *          3. 接受对话框并关闭
 */
void CANConfig::onConfirmClicked()
{
    // 从UI控件收集所有配置数据
    configData.protocol = protocolComboBox->currentText();                    // 协议类型
    configData.canfdStandard = canfdStandardComboBox->currentText();         // CANFD标准
    configData.standardType = standardTypeComboBox->currentText();           // 标准类型
    configData.arbitrationBaud = arbitrationBaudComboBox->currentText();     // 仲裁域波特率
    configData.dataBaud = dataBaudComboBox->currentText();                   // 数据域波特率
    configData.customBaud = customBaudComboBox->currentText();               // 自定义波特率
    configData.workMode = workModeComboBox->currentText();                   // 工作模式
    // 将下拉框文本转换为布尔值
    configData.terminalResistance = (terminalResistanceComboBox->currentText() == "使能");
    configData.busUtilization = (busUtilizationComboBox->currentText() == "使能");
    configData.clearBuffer = clearBufferCheckBox->isChecked();               // 复选框状态

    // 接受对话框，触发accepted信号并关闭对话框
    accept();
}

/**
 * @brief 取消按钮点击事件处理函数
 * @details 响应用户点击取消按钮，拒绝对话框并关闭
 */
void CANConfig::onCancelClicked()
{
    // 拒绝对话框，触发rejected信号并关闭对话框
    reject();
}

/**
 * @brief 获取配置数据
 * @return CANConfigData 当前保存的配置数据
 * @details 返回对话框中保存的配置参数，供外部调用者使用
 */
CANConfigData CANConfig::getConfigData() const
{
    return configData;
}

/**
 * @brief 设置配置数据
 * @param data 要设置的配置数据
 * @details 将外部配置数据应用到对话框的UI控件：
 *          1. 保存配置数据到内部变量
 *          2. 将数据设置到各个UI控件
 *          3. 触发协议变化处理以更新界面状态
 */
void CANConfig::setConfigData(const CANConfigData &data)
{
    // 保存配置数据到内部变量
    configData = data;

    // 将配置数据应用到各个UI控件
    protocolComboBox->setCurrentText(data.protocol);                         // 设置协议类型
    canfdStandardComboBox->setCurrentText(data.canfdStandard);              // 设置CANFD标准
    standardTypeComboBox->setCurrentText(data.standardType);                // 设置标准类型
    arbitrationBaudComboBox->setCurrentText(data.arbitrationBaud);          // 设置仲裁域波特率
    dataBaudComboBox->setCurrentText(data.dataBaud);                        // 设置数据域波特率
    customBaudComboBox->setCurrentText(data.customBaud);                    // 设置自定义波特率
    workModeComboBox->setCurrentText(data.workMode);                        // 设置工作模式
    // 将布尔值转换为下拉框文本
    terminalResistanceComboBox->setCurrentText(data.terminalResistance ? "使能" : "禁用");
    busUtilizationComboBox->setCurrentText(data.busUtilization ? "使能" : "禁用");
    clearBufferCheckBox->setChecked(data.clearBuffer);                      // 设置复选框状态

    // 触发协议变化处理，确保界面状态正确
    onProtocolChanged();
}

/**
 * @brief 波特率计算器按钮点击槽函数
 * @details 打开波特率计算器对话框，允许用户精确配置采样点参数
 */
void CANConfig::onBaudrateCalculatorClicked()
{
    // 创建波特率计算器对话框
    BaudrateCalculator calculator(this);

    // 显示对话框并等待用户操作
    if (calculator.exec() == QDialog::Accepted) {
        // 用户点击了应用配置，获取计算结果
        BaudrateResult arbResult = calculator.getArbitrationResult();
        BaudrateResult dataResult = calculator.getDataResult();

        // 更新仲裁域波特率下拉框
        QString arbText = QString("%1kbps %2%")
                         .arg(arbResult.actualBaud / 1000)
                         .arg(arbResult.samplePoint, 0, 'f', 1);

        // 检查是否已存在该选项，如果不存在则添加
        int arbIndex = arbitrationBaudComboBox->findText(arbText);
        if (arbIndex == -1) {
            arbitrationBaudComboBox->addItem(arbText);
            arbitrationBaudComboBox->setCurrentText(arbText);
        } else {
            arbitrationBaudComboBox->setCurrentIndex(arbIndex);
        }

        // 更新数据域波特率下拉框
        QString dataText = QString("%1Mbps %2%")
                          .arg(dataResult.actualBaud / 1000000.0, 0, 'f', 1)
                          .arg(dataResult.samplePoint, 0, 'f', 1);

        // 检查是否已存在该选项，如果不存在则添加
        int dataIndex = dataBaudComboBox->findText(dataText);
        if (dataIndex == -1) {
            dataBaudComboBox->addItem(dataText);
            dataBaudComboBox->setCurrentText(dataText);
        } else {
            dataBaudComboBox->setCurrentIndex(dataIndex);
        }

        // 显示配置成功消息
        QMessageBox::information(this, "配置成功",
                                QString("波特率配置已更新：\n"
                                       "仲裁段: %1\n"
                                       "数据段: %2")
                                .arg(arbText)
                                .arg(dataText));
    }
}
