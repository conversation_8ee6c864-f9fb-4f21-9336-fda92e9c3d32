/**
 * @file canform.h
 * @brief CAN协议表单类头文件
 * @details 定义了CAN协议表单类CANForm，提供基于Qt Designer设计的CAN协议
 *          通信界面和功能。该类使用.ui文件定义界面布局，包括设备管理、
 *          通道控制、数据收发、心跳功能等完整的CAN通信功能。
 * <AUTHOR>
 * @date 2025-07-02
 * @version 1.0
 */

#ifndef CANFORM_H
#define CANFORM_H

#include <QWidget>          // Qt窗口基类
#include <QTimer>           // Qt定时器类
#include <QLibrary>         // Qt动态库加载类
#include "zlgcan.h"         // ZLGCAN硬件SDK头文件
#include "canconfig.h"      // CAN配置数据结构

// 前向声明避免循环包含
class CANCommForm;

// Qt Designer生成的UI类命名空间
namespace Ui {
class CANForm;
}

/**
 * @class CANForm
 * @brief CAN协议表单类
 * @details 继承自QWidget，提供基于Qt Designer设计的CAN协议通信界面。
 *          该类使用.ui文件定义界面布局，集成ZLGCAN硬件SDK，提供完整的
 *          CAN设备管理、数据通信、心跳功能等。
 */
class CANForm : public QWidget
{
    Q_OBJECT  // Qt元对象系统宏，支持信号槽机制

public:
    /**
     * @brief 构造函数
     * @param parent 父窗口指针，默认为nullptr
     * @details 初始化CAN协议表单，加载UI界面和设置各种功能
     */
    explicit CANForm(QWidget *parent = nullptr);

    /**
     * @brief 析构函数
     * @details 清理资源，断开设备连接，释放内存
     */
    ~CANForm();

signals:
    /**
     * @brief 返回协议选择界面信号
     * @details 当用户点击返回按钮时发出此信号
     */
    void backToProtocolSelect();

private slots:
    // === 设备管理槽函数 ===
    /**
     * @brief 打开设备槽函数（旧版本）
     * @details 原来的设备打开函数，保留备用
     */
    void onOpenDeviceOld();

    /**
     * @brief 关闭设备槽函数
     * @details 关闭当前连接的CAN设备
     */
    void onCloseDevice();

    /**
     * @brief 刷新设备列表槽函数
     * @details 重新扫描并更新可用的CAN设备列表
     */
    void onRefreshDevices();

    // === 通道控制槽函数 ===
    /**
     * @brief 启动CAN通道槽函数
     * @details 启动CAN通道，开始数据通信
     */
    void onStartCAN();

    /**
     * @brief 停止CAN通道槽函数
     * @details 停止CAN通道，结束数据通信
     */
    void onStopCAN();

    /**
     * @brief 重置CAN通道槽函数
     * @details 重置CAN通道状态，清除错误状态
     */
    void onResetCAN();

    // === 数据收发槽函数 ===
    /**
     * @brief 发送数据槽函数
     * @details 发送用户输入的CAN数据帧
     */
    void onSendData();

    /**
     * @brief 接收数据槽函数
     * @details 接收并处理CAN数据帧
     */
    void onReceiveData();

    /**
     * @brief 清空发送区域槽函数
     * @details 清空发送数据显示区域
     */
    void onClearSendArea();

    /**
     * @brief 清空接收区域槽函数
     * @details 清空接收数据显示区域
     */
    void onClearReceiveArea();

    // === 心跳包控制槽函数 ===
    /**
     * @brief 启动心跳槽函数
     * @details 启动心跳数据包的定时发送
     */
    void onStartHeartbeat();

    /**
     * @brief 停止心跳槽函数
     * @details 停止心跳数据包的发送
     */
    void onStopHeartbeat();

    /**
     * @brief 心跳定时器超时槽函数
     * @details 心跳定时器超时时调用，发送心跳数据包
     */
    void onHeartbeatTimeout();

    // === 配置相关槽函数 ===
    void onProtocolChanged(int index);
    void onBaudrateChanged(int index);
    void onTerminalResistanceChanged(bool enabled);

    // 返回按钮
    void onBackButtonClicked();

    // 新增配置相关槽函数
    void onShowConfigDialog();
    void onConfigConfirmed();

private:
    Ui::CANForm *ui;
    DEVICE_HANDLE deviceHandle;    // 设备句柄
    CHANNEL_HANDLE channelHandle;  // 通道句柄
    QTimer *heartbeatTimer;        // 心跳包定时器
    QTimer *receiveTimer;          // 接收数据定时器
    bool isDeviceOpened;           // 设备打开状态
    bool isChannelStarted;         // 通道启动状态
    bool isHeartbeatRunning;       // 心跳包运行状态

    // 设备配置参数
    UINT deviceType;               // 设备类型
    UINT deviceIndex;              // 设备索引
    UINT channelIndex;             // 通道索引

    // 动态库加载
    QLibrary *zlgcanLib;
    bool loadZLGCANLibrary();

    // ZLGCAN API函数指针
    typedef DEVICE_HANDLE (*ZCAN_OpenDevice_t)(UINT, UINT, UINT);
    typedef UINT (*ZCAN_CloseDevice_t)(DEVICE_HANDLE);
    typedef UINT (*ZCAN_GetDeviceInf_t)(DEVICE_HANDLE, ZCAN_DEVICE_INFO*);
    typedef CHANNEL_HANDLE (*ZCAN_InitCAN_t)(DEVICE_HANDLE, UINT, ZCAN_CHANNEL_INIT_CONFIG*);
    typedef UINT (*ZCAN_StartCAN_t)(CHANNEL_HANDLE);
    typedef UINT (*ZCAN_ResetCAN_t)(CHANNEL_HANDLE);
    typedef UINT (*ZCAN_Transmit_t)(CHANNEL_HANDLE, ZCAN_Transmit_Data*, UINT);
    typedef UINT (*ZCAN_Receive_t)(CHANNEL_HANDLE, ZCAN_Receive_Data*, UINT, int);
    typedef UINT (*ZCAN_TransmitFD_t)(CHANNEL_HANDLE, ZCAN_TransmitFD_Data*, UINT);
    typedef UINT (*ZCAN_ReceiveFD_t)(CHANNEL_HANDLE, ZCAN_ReceiveFD_Data*, UINT, int);
    typedef UINT (*ZCAN_IsDeviceOnLine_t)(DEVICE_HANDLE);
    typedef UINT (*ZCAN_GetReceiveNum_t)(CHANNEL_HANDLE, BYTE);
    typedef UINT (*ZCAN_ClearBuffer_t)(CHANNEL_HANDLE);

    ZCAN_OpenDevice_t ZCAN_OpenDevice;
    ZCAN_CloseDevice_t ZCAN_CloseDevice;
    ZCAN_GetDeviceInf_t ZCAN_GetDeviceInf;
    ZCAN_InitCAN_t ZCAN_InitCAN;
    ZCAN_StartCAN_t ZCAN_StartCAN;
    ZCAN_ResetCAN_t ZCAN_ResetCAN;
    ZCAN_Transmit_t ZCAN_Transmit;
    ZCAN_Receive_t ZCAN_Receive;
    ZCAN_TransmitFD_t ZCAN_TransmitFD;
    ZCAN_ReceiveFD_t ZCAN_ReceiveFD;
    ZCAN_IsDeviceOnLine_t ZCAN_IsDeviceOnLine;
    ZCAN_GetReceiveNum_t ZCAN_GetReceiveNum;
    ZCAN_ClearBuffer_t ZCAN_ClearBuffer;

    // 初始化函数
    void initUI();
    void setupConnections();
    void loadDeviceInfo();

    // 辅助函数
    bool openDevice();
    void closeDevice();
    bool initializeChannel();
    void updateControlState();
    void appendLog(const QString &text, bool isReceived);

    // 新增成员变量
    CANConfig *configDialog;
    CANCommForm *commForm;
    CANConfigData currentConfig;
};

#endif // CANFORM_H 