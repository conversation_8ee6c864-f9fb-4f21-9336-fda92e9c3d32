/**
 * @file mainwindow.h
 * @brief 主窗口类头文件
 * @details 定义了应用程序的主窗口类MainWindow，这是整个应用程序的核心界面。
 *          主窗口提供了协议选择、虚拟示波器、连接设置等功能的入口，
 *          并管理各个子窗口的显示和交互。
 * <AUTHOR>
 * @date 2025-07-02
 * @version 1.0
 */

#ifndef MAINWINDOW_H
#define MAINWINDOW_H

#include <QMainWindow>           // Qt主窗口基类
#include <QDebug>                // Qt调试输出类
#include <QResizeEvent>          // Qt窗口大小改变事件类
#include "protocolselectform.h"  // 协议选择窗口类
#include "oscilloscopeinterface.h" // 虚拟示波器界面类
#include "multimotorinterface.h" // 多电机控制界面类（旧版本）
#include "multimotorcontrol_pure.h" // 纯UI多电机控制界面类（新版本）
#include "motordatamanager.h"    // 电机数据管理器类

// Qt命名空间开始标记
QT_BEGIN_NAMESPACE
namespace Ui {
    class MainWindow;  // UI类前向声明，由Qt Designer生成
}
QT_END_NAMESPACE

/**
 * @class MainWindow
 * @brief 主窗口类
 * @details 继承自QMainWindow，是应用程序的主界面窗口。
 *          负责管理整个应用程序的界面布局和功能模块的切换，
 *          包括协议选择、连接设置、虚拟示波器等功能页面。
 */
class MainWindow : public QMainWindow
{
    Q_OBJECT  // Qt元对象系统宏，支持信号槽机制

public:
    /**
     * @brief 构造函数
     * @param parent 父窗口指针，默认为nullptr
     * @details 初始化主窗口，设置UI界面，创建子窗口实例
     */
    MainWindow(QWidget *parent = nullptr);

    /**
     * @brief 析构函数
     * @details 清理资源，释放UI对象和子窗口实例
     */
    ~MainWindow();

protected:
    /**
     * @brief 窗口大小改变事件处理
     * @param event 大小改变事件
     */
    void resizeEvent(QResizeEvent *event) override;

private slots:
    // === 主界面按钮槽函数 ===
    /**
     * @brief 协议选择按钮点击槽函数
     * @details 响应用户点击协议选择按钮，打开协议选择窗口
     */
    void on_protocolButton_clicked();

    /**
     * @brief 虚拟示波器按钮点击槽函数
     * @details 响应用户点击虚拟示波器按钮，切换到示波器页面
     */
    void on_btnWave_clicked();

    /**
     * @brief 帮助按钮点击槽函数
     * @details 响应用户点击帮助按钮，显示帮助信息
     */
    void on_btnHelp_clicked();

    /**
     * @brief 退出按钮点击槽函数
     * @details 响应用户点击退出按钮，关闭应用程序
     */
    void on_btnExit_clicked();

    // === 新增功能按钮槽函数 ===
    void on_btnFunctionSettings_clicked();
    void on_btnMotion_clicked();
    void on_btnParameterStorage_clicked();
    void on_btnPIDSettings_clicked();
    void on_btnEncoder_clicked();
    void on_btnStatusMonitor_clicked();
    void on_btnSafePower_clicked();
    void on_btnPositionProtection_clicked();
    void on_btnSafeSpeed_clicked();
    void on_btnFaultDiagnosis_clicked();
    void on_btnSave_clicked();

    // === 第二批新增功能按钮槽函数 ===
    void on_btnCurrentLoop_clicked();
    void on_btnIncrementalProtection_clicked();
    void on_btnMechanicalZeroCalibration_clicked();
    void on_btnConsole_clicked();
    void on_btnMotorStop_clicked();
    void on_btnMotorSetup_clicked();
    void on_btnSelfCheck_clicked();
    void on_btnIOSettings_clicked();
    void on_btnMultiMotorControl_clicked();

    // === 虚拟示波器相关槽函数 ===
    /**
     * @brief 示波器页面返回按钮点击槽函数
     * @details 响应用户点击返回按钮，从示波器页面返回主页面
     * @note 现在示波器使用独立窗口，此函数用于窗口管理
     */
    void on_btnBackFromWave_clicked();

    /**
     * @brief 示波器清除按钮点击槽函数
     * @details 响应用户点击清除按钮，清空示波器显示数据
     */
    void on_btnClearWave_clicked();

    // === 子窗口回调槽函数 ===
    /**
     * @brief 处理协议选择界面返回槽函数
     * @details 当协议选择窗口关闭时调用，执行必要的清理工作
     */
    void onProtocolSelectBack();

    /**
     * @brief 处理示波器界面返回槽函数
     * @details 当示波器界面关闭时调用，重新显示主界面
     */
    void onOscilloscopeBack();

    // 多电机控制现在使用Qt自动连接版本 on_btnMultiMotorControl_clicked()
    // void onMultiMotorControlClicked();  // 已移除，避免链接错误

    /**
     * @brief 处理多电机界面返回槽函数
     * @details 当多电机界面关闭时调用，重新显示主界面
     */
    void onMultiMotorBack();

    /**
     * @brief 为特定电机打开示波器槽函数
     * @param motor_id 电机ID (1-6)
     */
    void onOpenOscilloscopeForMotor(quint8 motor_id);

private:
    // === 私有成员变量 ===
    /**
     * @brief UI界面指针
     * @details 指向由Qt Designer生成的UI类实例，包含所有界面控件
     */
    Ui::MainWindow *ui;

    /**
     * @brief 协议选择窗口指针
     * @details 指向协议选择窗口实例，用于管理协议选择界面的显示和交互
     */
    ProtocolSelectForm *protocolSelectForm;

    /**
     * @brief 虚拟示波器界面指针
     * @details 指向虚拟示波器界面实例，用于管理示波器界面的显示和交互
     */
    OscilloscopeInterface *oscilloscopeInterface;

    /**
     * @brief 多电机控制界面指针（旧版本）
     * @details 指向多电机控制界面实例，用于管理多电机控制界面的显示和交互
     */
    MultiMotorInterface *multiMotorInterface;

    /**
     * @brief 纯UI多电机控制界面指针（新版本）
     * @details 指向纯UI设计的多电机控制界面实例，避免代码动态生成控件
     */
    MultiMotorControlPure *multiMotorControlPure;

    /**
     * @brief 电机数据管理器指针
     * @details 用于管理多电机的状态数据、通信数据等
     */
    MotorDataManager *motorDataManager;

    /**
     * @brief logo背景标签指针
     * @details 用于跟踪和管理logo背景标签，便于重新设置
     */
    QLabel *logoBackgroundLabel;

    /**
     * @brief 预初始化关键组件
     * @details 在程序启动时预先初始化关键组件，防止首次使用时崩溃
     */
    void preInitializeCriticalComponents();

    /**
     * @brief 设置增强背景效果函数
     * @details 在主界面设置虚化logo、科技网格、光效等背景元素
     */
    void setupEnhancedBackground();

    /**
     * @brief 设置虚化logo背景
     */
    void setupBlurredLogo();

    /**
     * @brief 设置科技网格背景
     */
    void setupTechGrid();

    /**
     * @brief 设置光效元素
     */
    void setupLightEffects();

    /**
     * @brief 初始化多电机控制组件
     * @details 创建电机数据管理器和多电机控制界面，并建立信号连接
     */
    void initializeMultiMotorControl();
};

#endif // MAINWINDOW_H
