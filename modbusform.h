/**
 * @file modbusform.h
 * @brief Modbus通信表单类头文件
 * @details 定义了Modbus通信表单类ModbusForm，提供基于Qt Designer设计的
 *          Modbus RTU协议通信界面和功能。该类使用.ui文件定义界面布局，
 *          包括串口配置、Modbus功能码实现、数据收发处理等完整功能。
 * <AUTHOR>
 * @date 2025-07-02
 * @version 1.0
 */

#ifndef MODBUSFORM_H
#define MODBUSFORM_H

#include <QWidget>              // Qt窗口基类
#include <QSerialPort>          // Qt串口通信类
#include <QSerialPortInfo>      // Qt串口信息类
#include <QMessageBox>          // Qt消息框类
#include <QDebug>               // Qt调试输出类
#include <QTimer>               // Qt定时器类
#include <QMap>                 // Qt映射容器类
#include <QTableWidget>         // Qt表格控件类
#include <QVector>              // Qt向量容器类
#include <QVariant>             // Qt变体类型类
#include <QRegularExpression>   // Qt正则表达式类
#include <QFile>                // Qt文件操作类
#include <QTextStream>          // Qt文本流类
#include <QDateTime>            // Qt日期时间类
#include <QFileDialog>          // Qt文件对话框类

// Qt Designer生成的UI类命名空间
namespace Ui {
class ModbusForm;
}

/**
 * @class ModbusForm
 * @brief Modbus通信表单类
 * @details 继承自QWidget，提供基于Qt Designer设计的Modbus RTU协议通信界面。
 *          该类使用.ui文件定义界面布局，集成Qt串口通信功能，提供完整的
 *          Modbus协议实现，包括：
 *          1. 串口配置和管理功能
 *          2. Modbus RTU协议功能码实现
 *          3. 数据发送和接收处理
 *          4. CRC校验和数据验证
 *          5. 数据日志记录和保存
 */
class ModbusForm : public QWidget
{
    Q_OBJECT  // Qt元对象系统宏，支持信号槽机制

public:
    /**
     * @brief 构造函数
     * @param parent 父窗口指针，默认为nullptr
     * @details 初始化Modbus通信表单，包括：
     *          - 加载UI界面布局
     *          - 初始化串口对象和配置
     *          - 创建定时器和信号槽连接
     *          - 设置默认参数和状态
     */
    explicit ModbusForm(QWidget *parent = nullptr);

    /**
     * @brief 析构函数
     * @details 清理资源和关闭连接：
     *          - 关闭串口连接
     *          - 停止所有定时器
     *          - 释放内存资源
     */
    ~ModbusForm();

signals:
    /**
     * @brief 返回协议选择界面信号
     */
    void backToProtocolSelect();

private slots:
    /**
     * @brief 刷新串口列表按钮点击处理
     * 更新可用串口列表
     */
    void onRefreshPortButtonClicked();

    /**
     * @brief 连接串口按钮点击处理
     * 根据设置打开选定的串口
     */
    void onConnectButtonClicked();

    /**
     * @brief 断开串口按钮点击处理
     * 关闭当前打开的串口
     */
    void onDisconnectButtonClicked();

    /**
     * @brief 串口数据接收处理
     * 处理接收到的Modbus数据帧
     */
    void onSerialDataReceived();

    /**
     * @brief 串口错误处理
     * @param error 错误类型
     * 处理串口通信过程中的错误
     */
    void onSerialError(QSerialPort::SerialPortError error);

    /**
     * @brief 发送按钮点击处理
     * 构造并发送Modbus数据帧
     */
    void onSendButtonClicked();

    /**
     * @brief 清空接收区按钮点击处理
     */
    void onClearReceiveButtonClicked();

    /**
     * @brief 清空发送区按钮点击处理
     */
    void onClearSendButtonClicked();

    /**
     * @brief 保存日志按钮点击处理
     * 将通信日志保存到文件
     */
    void onSaveLogButtonClicked();

    /**
     * @brief 十六进制显示切换处理
     * @param checked 是否选中
     */
    void onHexDisplayToggled(bool checked);

    /**
     * @brief 自动换行切换处理
     * @param checked 是否选中
     */
    void onAutoNewLineToggled(bool checked);

    /**
     * @brief 自动发送切换处理
     * @param checked 是否选中
     */
    void onAutoSendToggled(bool checked);

    /**
     * @brief 自动发送间隔改变处理
     * @param value 新的间隔值
     */
    void onAutoSendIntervalChanged(int value);

    /**
     * @brief 自动发送定时器超时处理
     */
    void onAutoSendTimer();

    /**
     * @brief 自动添加CRC切换处理
     * @param checked 是否选中
     */
    void onAutoAppendCRCToggled(bool checked);

    /**
     * @brief 功能码改变处理
     * @param index 新选择的索引
     */
    void on_functionCodeComboBox_currentIndexChanged(int index);

    /**
     * @brief 十六进制发送切换处理
     * @param checked 是否选中
     */
    void onHexSendToggled(bool checked);

    /**
     * @brief 返回按钮点击处理
     */
    void onBackButtonClicked();

    /**
     * @brief Modbus数据接收处理
     * @param data 接收到的数据
     */
    void onModbusDataReceived(const QByteArray &data);

    /**
     * @brief 保存数据按钮点击处理
     */
    void onSaveDataClicked();

    /**
     * @brief 清空数据按钮点击处理
     */
    void onClearDataClicked();

protected:
    /**
     * @brief 关闭事件处理
     * @param event 关闭事件
     */
    void closeEvent(QCloseEvent *event) override;

private:
    static const int MIN_FRAME_LENGTH = 4;  ///< 最小帧长度
    static const int MAX_FRAME_LENGTH = 256;  ///< 最大帧长度

    /**
     * @brief 功能码描述映射表
     * 存储Modbus功能码及其描述
     */
    static const QMap<uint8_t, QString> functionCodeDescriptions;

    Ui::ModbusForm *ui;  ///< UI对象指针
    QSerialPort *serialPort;  ///< 串口对象指针
    QTimer *autoSendTimer;  ///< 自动发送定时器
    bool autoNewLine;  ///< 自动换行标志
    bool isSerialOpened;  ///< 串口是否打开
    uint8_t slaveAddress;  ///< 从站地址
    uint8_t functionCode;  ///< 功能码

    QTableWidget *tableWidget;  ///< 数据表格
    QVector<QVariantMap> dataBuffer;  ///< 数据缓存

    /**
     * @brief 初始化用户界面
     * 设置UI组件的初始状态
     */
    void initUI();

    /**
     * @brief 设置信号连接
     * 建立所有信号槽连接
     */
    void setupConnections();

    /**
     * @brief 启用/禁用控件
     * @param enabled 是否启用
     */
    void enableControls(bool enabled);

    /**
     * @brief 添加日志
     * @param text 日志文本
     * @param isReceived 是否为接收数据
     */
    void appendLog(const QString &text, bool isReceived);

    /**
     * @brief 保存日志到文件
     * @param filename 文件名
     */
    void saveLogToFile(const QString &filename);

    /**
     * @brief 字节数组转十六进制字符串
     * @param data 字节数组
     * @return 十六进制字符串
     */
    QString byteArrayToHexString(const QByteArray &data);

    /**
     * @brief 十六进制字符串转字节数组
     * @param hexString 十六进制字符串
     * @return 字节数组
     */
    QByteArray hexStringToByteArray(const QString &hexString);

    /**
     * @brief 设置功能码
     * 初始化功能码下拉列表
     */
    void setupFunctionCodes();

    /**
     * @brief 创建Modbus数据帧
     * @return 构造的数据帧
     */
    QByteArray createModbusFrame();

    /**
     * @brief 计算CRC校验值
     * @param data 需要计算校验的数据
     * @return 包含CRC校验值的字节数组(2字节)
     */
    QByteArray calculateCRC(const QByteArray &data);

    /**
     * @brief 解析Modbus数据帧
     * @param frame Modbus数据帧
     * @param isSend 是否为发送帧
     * @return 解析结果字符串
     */
    QString parseModbusFrame(const QByteArray &frame, bool isSend);

    /**
     * @brief 解析读取响应
     * @param frame 响应帧
     * @return 解析结果字符串
     */
    QString parseReadResponse(const QByteArray &frame);

    /**
     * @brief 解析写入响应
     * @param frame 响应帧
     * @return 解析结果字符串
     */
    QString parseWriteResponse(const QByteArray &frame);

    /**
     * @brief 解析错误响应
     * @param frame 响应帧
     * @return 解析结果字符串
     */
    QString parseErrorResponse(const QByteArray &frame);

    /**
     * @brief 验证数据帧
     * @param frame 数据帧
     * @param errorMsg 错误信息
     * @return 验证结果
     */
    bool validateFrame(const QByteArray &frame, QString &errorMsg);

    /**
     * @brief 验证CRC校验
     * @param frame 数据帧
     * @return 校验结果
     */
    bool validateCRC(const QByteArray &frame);

    /**
     * @brief 检查设置
     * @param errorMsg 错误信息
     * @return 检查结果
     */
    bool checkSettings(QString &errorMsg);

    /**
     * @brief 检查寄存器范围
     * @param address 起始地址
     * @param length 长度
     * @param errorMsg 错误信息
     * @return 检查结果
     */
    bool checkRegisterRange(int address, int length, QString &errorMsg);
};

#endif // MODBUSFORM_H 