<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>ProtocolSelectForm</class>
 <widget class="QWidget" name="ProtocolSelectForm">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>800</width>
    <height>600</height>
   </rect>
  </property>
  <property name="minimumSize">
   <size>
    <width>800</width>
    <height>600</height>
   </size>
  </property>
  <property name="windowTitle">
   <string>协议选择</string>
  </property>
  <property name="styleSheet">
   <string>QWidget#ProtocolSelectForm {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                               stop:0 #ffffff, stop:1 #f8f9fa);
}</string>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout">
   <property name="spacing">
    <number>30</number>
   </property>
   <property name="leftMargin">
    <number>40</number>
   </property>
   <property name="topMargin">
    <number>30</number>
   </property>
   <property name="rightMargin">
    <number>40</number>
   </property>
   <property name="bottomMargin">
    <number>30</number>
   </property>
   <item>
    <widget class="QLabel" name="logoLabel">
     <property name="minimumSize">
      <size>
       <width>80</width>
       <height>80</height>
      </size>
     </property>
     <property name="maximumSize">
      <size>
       <width>80</width>
       <height>80</height>
      </size>
     </property>
     <property name="styleSheet">
      <string>QLabel {
    background: transparent;
    padding: 10px;
}</string>
     </property>
     <property name="text">
      <string/>
     </property>
     <property name="pixmap">
      <pixmap resource="resources.qrc">:/icon/qiange.png</pixmap>
     </property>
     <property name="scaledContents">
      <bool>true</bool>
     </property>
     <property name="alignment">
      <set>Qt::AlignCenter</set>
     </property>
    </widget>
   </item>
   <item>
    <widget class="QLabel" name="titleLabel">
     <property name="font">
      <font>
       <family>Microsoft YaHei UI</family>
       <pointsize>20</pointsize>
       <bold>true</bold>
      </font>
     </property>
     <property name="styleSheet">
      <string>QLabel {
    color: #dc3545;
    background: transparent;
    padding: 10px;
    font-weight: bold;
}</string>
     </property>
     <property name="text">
      <string>请选择通信协议</string>
     </property>
     <property name="alignment">
      <set>Qt::AlignCenter</set>
     </property>
    </widget>
   </item>
   <item>
    <widget class="QFrame" name="buttonFrame">
     <property name="styleSheet">
      <string>QFrame#buttonFrame {
    background: rgba(255, 255, 255, 0.9);
    border-radius: 20px;
    border: 2px solid #dc3545;
}</string>
     </property>
     <property name="frameShape">
      <enum>QFrame::NoFrame</enum>
     </property>
     <layout class="QHBoxLayout" name="horizontalLayout">
      <property name="spacing">
       <number>30</number>
      </property>
      <property name="leftMargin">
       <number>30</number>
      </property>
      <property name="topMargin">
       <number>30</number>
      </property>
      <property name="rightMargin">
       <number>30</number>
      </property>
      <property name="bottomMargin">
       <number>30</number>
      </property>
      <item>
       <widget class="QPushButton" name="modbusButton">
        <property name="minimumSize">
         <size>
          <width>280</width>
          <height>120</height>
         </size>
        </property>
        <property name="font">
         <font>
          <family>Microsoft YaHei UI</family>
          <pointsize>16</pointsize>
          <bold>true</bold>
         </font>
        </property>
        <property name="styleSheet">
         <string>QPushButton {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                               stop:0 #ffffff, stop:1 #f8f9fa);
    color: #dc3545;
    border: 2px solid #dc3545;
    border-radius: 15px;
    padding: 15px;
    text-align: center;
    font-weight: bold;
}

QPushButton:hover {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                               stop:0 #dc3545, stop:1 #c82333);
    color: white;
    transform: translateY(-3px);
}

QPushButton:pressed {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                               stop:0 #c82333, stop:1 #a71e2a);
    color: white;
}</string>
        </property>
        <property name="text">
         <string>Modbus协议</string>
        </property>
        <property name="icon">
         <iconset resource="resources.qrc">
          <normaloff>:/icon/modbus.png</normaloff>:/icon/modbus.png</iconset>
        </property>
        <property name="iconSize">
         <size>
          <width>48</width>
          <height>48</height>
         </size>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QPushButton" name="canButton">
        <property name="minimumSize">
         <size>
          <width>280</width>
          <height>120</height>
         </size>
        </property>
        <property name="font">
         <font>
          <family>Microsoft YaHei UI</family>
          <pointsize>16</pointsize>
          <bold>true</bold>
         </font>
        </property>
        <property name="styleSheet">
         <string>QPushButton {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                               stop:0 #ffffff, stop:1 #f8f9fa);
    color: #dc3545;
    border: 2px solid #dc3545;
    border-radius: 15px;
    padding: 15px;
    text-align: center;
    font-weight: bold;
}

QPushButton:hover {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                               stop:0 #dc3545, stop:1 #c82333);
    color: white;
    transform: translateY(-3px);
}

QPushButton:pressed {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                               stop:0 #a71e2a, stop:1 #721c24);
    color: white;
}</string>
        </property>
        <property name="text">
         <string>CAN/CANFD协议</string>
        </property>
        <property name="icon">
         <iconset resource="resources.qrc">
          <normaloff>:/icon/can.png</normaloff>:/icon/can.png</iconset>
        </property>
        <property name="iconSize">
         <size>
          <width>48</width>
          <height>48</height>
         </size>
        </property>
       </widget>
      </item>
     </layout>
    </widget>
   </item>
   <item>
    <spacer name="verticalSpacer">
     <property name="orientation">
      <enum>Qt::Vertical</enum>
     </property>
     <property name="sizeHint" stdset="0">
      <size>
       <width>20</width>
       <height>40</height>
      </size>
     </property>
    </spacer>
   </item>
   <item>
    <layout class="QHBoxLayout" name="backButtonLayout">
     <item>
      <spacer name="horizontalSpacer">
       <property name="orientation">
        <enum>Qt::Horizontal</enum>
       </property>
       <property name="sizeHint" stdset="0">
        <size>
         <width>40</width>
         <height>20</height>
        </size>
       </property>
      </spacer>
     </item>
     <item>
      <widget class="QPushButton" name="backButton">
       <property name="minimumSize">
        <size>
         <width>150</width>
         <height>50</height>
        </size>
       </property>
       <property name="font">
        <font>
         <family>Microsoft YaHei UI</family>
         <pointsize>14</pointsize>
         <bold>true</bold>
        </font>
       </property>
       <property name="styleSheet">
        <string>QPushButton {
    background-color: #ffffff;
    color: #dc3545;
    border: 2px solid #dc3545;
    border-radius: 10px;
    padding: 10px;
    text-align: center;
    font-weight: bold;
}

QPushButton:hover {
    background-color: #dc3545;
    color: white;
    transform: translateY(-2px);
}

QPushButton:pressed {
    background-color: #c82333;
    color: white;
}</string>
       </property>
       <property name="text">
        <string>返回</string>
       </property>
       <property name="icon">
        <iconset resource="resources.qrc">
         <normaloff>:/icon/exit.png</normaloff>:/icon/exit.png</iconset>
       </property>
       <property name="iconSize">
        <size>
         <width>24</width>
         <height>24</height>
        </size>
       </property>
      </widget>
     </item>
     <item>
      <spacer name="horizontalSpacer_2">
       <property name="orientation">
        <enum>Qt::Horizontal</enum>
       </property>
       <property name="sizeHint" stdset="0">
        <size>
         <width>40</width>
         <height>20</height>
        </size>
       </property>
      </spacer>
     </item>
    </layout>
   </item>
  </layout>
 </widget>
 <resources>
  <include location="resources.qrc"/>
 </resources>
 <connections/>
</ui> 